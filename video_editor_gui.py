import tkinter as tk
from tkinter import filedialog, messagebox
import subprocess
import os
import shutil
import threading

class VideoProcessorGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("视频轨道处理器")
        self.ffmpeg_path = self.find_ffmpeg()
        
        # UI布局
        row = 0
        
        # FFmpeg路径配置
        tk.Label(root, text="FFmpeg路径:").grid(row=row, column=0, padx=5, pady=5)
        self.ffmpeg_entry = tk.Entry(root, width=40)
        self.ffmpeg_entry.grid(row=row, column=1, padx=5, pady=5)
        self.ffmpeg_entry.insert(0, self.ffmpeg_path if self.ffmpeg_path else "未找到FFmpeg")
        tk.Button(root, text="设置路径", command=self.set_ffmpeg_path).grid(row=row, column=2, padx=5, pady=5)
        row += 1

        # 输入视频
        tk.Label(root, text="输入视频:").grid(row=row, column=0, padx=5, pady=5)
        self.input_entry = tk.Entry(root, width=40)
        self.input_entry.grid(row=row, column=1, padx=5, pady=5)
        tk.Button(root, text="浏览", command=self.select_input).grid(row=row, column=2, padx=5, pady=5)
        row += 1

        # 输出视频
        tk.Label(root, text="输出视频:").grid(row=row, column=0, padx=5, pady=5)
        self.output_entry = tk.Entry(root, width=40)
        self.output_entry.grid(row=row, column=1, padx=5, pady=5)
        tk.Button(root, text="浏览", command=self.select_output).grid(row=row, column=2, padx=5, pady=5)
        row += 1

        # 空视频参数
        tk.Label(root, text="空视频时长(秒):").grid(row=row, column=0, padx=5, pady=5)
        self.duration_entry = tk.Entry(root, width=10)
        self.duration_entry.insert(0, "3")  # 默认3秒
        self.duration_entry.grid(row=row, column=1, padx=5, pady=5, sticky="w")
        row += 1

        # 处理按钮
        self.process_btn = tk.Button(root, text="开始处理", command=self.process_video)
        self.process_btn.grid(row=row, column=1, pady=10)
        row += 1

        # 状态显示
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        tk.Label(root, textvariable=self.status_var).grid(row=row, column=0, columnspan=3)

    def find_ffmpeg(self):
        """查找系统FFmpeg路径"""
        try:
            result = subprocess.run(['ffmpeg', '-version'], capture_output=True, text=True)
            if result.returncode == 0:
                return 'ffmpeg'
        except FileNotFoundError:
            pass
        return None

    def set_ffmpeg_path(self):
        """设置FFmpeg路径"""
        filepath = filedialog.askopenfilename(title="选择FFmpeg可执行文件")
        if filepath:
            self.ffmpeg_path = filepath
            self.ffmpeg_entry.delete(0, tk.END)
            self.ffmpeg_entry.insert(0, filepath)

    def select_input(self):
        """选择输入视频文件"""
        filepath = filedialog.askopenfilename(filetypes=[("视频文件", "*.mp4 *.avi *.mov")])
        if filepath:
            self.input_entry.delete(0, tk.END)
            self.input_entry.insert(0, filepath)
            # 自动设置输出路径
            dirname = os.path.dirname(filepath)
            filename, ext = os.path.splitext(os.path.basename(filepath))
            output_path = os.path.join(dirname, f"{filename}_with_empty_track{ext}")
            self.output_entry.delete(0, tk.END)
            self.output_entry.insert(0, output_path)

    def select_output(self):
        """选择输出视频文件"""
        filepath = filedialog.asksaveasfilename(
            defaultextension=".mp4",
            filetypes=[("MP4文件", "*.mp4")]
        )
        if filepath:
            self.output_entry.delete(0, tk.END)
            self.output_entry.insert(0, filepath)

    def check_ffmpeg(self):
        """检查FFmpeg是否可用"""
        if not self.ffmpeg_path:
            return False
        try:
            result = subprocess.run([self.ffmpeg_path, '-version'], capture_output=True, text=True)
            return result.returncode == 0
        except Exception:
            return False

    def show_error(self, message):
        """显示错误消息"""
        self.status_var.set("处理失败")
        messagebox.showerror("错误", message)
        self.process_btn.config(state=tk.NORMAL)

    def process_video(self):
        """启动视频处理线程"""
        self.process_btn.config(state=tk.DISABLED)
        self.status_var.set("准备处理...")
        threading.Thread(target=self._process_video_thread, daemon=True).start()

    def _process_video_thread(self):
        """视频处理线程"""
        try:
            input_path = self.input_entry.get()
            output_path = self.output_entry.get()
            duration = self.duration_entry.get()

            # 验证输入
            if not all([input_path, output_path, duration]):
                self.root.after(0, lambda: self.show_error("请填写所有字段"))
                return

            try:
                duration = float(duration)
                if duration <= 0:
                    raise ValueError("时长必须大于0")
            except ValueError:
                self.root.after(0, lambda: self.show_error("请输入有效的时长数字"))
                return

            # 检查FFmpeg
            if not self.check_ffmpeg():
                self.root.after(0, lambda: self.show_error("找不到FFmpeg，请确保已安装并添加到PATH"))
                return

            self.root.after(0, lambda: self.status_var.set("处理中..."))

            # 创建临时目录
            temp_dir = os.path.join(os.path.dirname(output_path), "temp_video")
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
            os.makedirs(temp_dir, exist_ok=True)

            # 创建日志目录
            log_dir = os.path.join(os.path.dirname(output_path), "video_processor_logs")
            os.makedirs(log_dir, exist_ok=True)

            def run_command(cmd, log_name):
                """运行命令并记录日志"""
                log_file = os.path.join(log_dir, f"{log_name}.log")
                with open(log_file, 'w') as f:
                    f.write(f"执行命令: {' '.join(cmd)}\n\n")
                    try:
                        result = subprocess.run(
                            cmd, stdout=f, stderr=subprocess.STDOUT, text=True, check=True
                        )
                        return True
                    except subprocess.CalledProcessError as e:
                        f.write(f"\n命令执行失败: {str(e)}\n")
                        raise

            try:
                # 创建2x4的空视频
                temp_video = os.path.join(temp_dir, "temp_empty.mp4")
                self.root.after(0, lambda: self.status_var.set("正在创建空视频..."))
                
                empty_video_cmd = [
                    self.ffmpeg_path,
                    '-y',
                    '-f', 'lavfi',
                    '-i', f'color=c=black:s=2x4:d={duration}:r=30',
                    '-f', 'lavfi',
                    '-i', 'anullsrc=r=44100:cl=stereo',
                    '-c:v', 'libx264',
                    '-preset', 'ultrafast',
                    '-t', str(duration),
                    '-pix_fmt', 'yuv420p',
                    os.path.normpath(temp_video)
                ]
                run_command(empty_video_cmd, "create_empty_video")

                # 合并视频
                self.root.after(0, lambda: self.status_var.set("正在合并视频..."))
                temp_output = os.path.join(temp_dir, "output.mp4")  # 英文临时路径
                
                merge_cmd = [
                    self.ffmpeg_path,
                    '-i', os.path.normpath(temp_video),
                    '-i', os.path.normpath(input_path),
                    '-filter_complex', '[0:v][1:v]concat=n=2:v=1[outv]',
                    '-map', '[outv]',
                    '-map', '1:a?',  # 可选音频流
                    '-c:v', 'copy',  # 直接复制视频流
                    '-c:a', 'copy',  # 直接复制音频流
                    '-movflags', '+faststart',  # 确保快速播放
                    '-y',
                    os.path.normpath(temp_output)
                ]
                run_command(merge_cmd, "merge_videos")

                # 移动最终文件
                if os.path.exists(temp_output):
                    shutil.move(temp_output, os.path.normpath(output_path))
                    self.root.after(0, lambda: self.status_var.set("处理完成"))
                    self.root.after(0, lambda: messagebox.showinfo("成功", "视频处理完成"))
                else:
                    raise Exception("输出文件未生成，请检查日志")

            except subprocess.CalledProcessError as e:
                error_log = os.path.join(log_dir, "error.log")
                with open(error_log, 'w') as f:
                    f.write(f"错误详情:\n{str(e)}\n")
                    # 获取FFmpeg版本信息
                    f.write("\nFFmpeg版本信息:\n")
                    subprocess.run([self.ffmpeg_path, '-version'], stdout=f, stderr=subprocess.STDOUT)
                
                error_msg = (
                    f"视频处理失败，详细错误已记录到:\n{error_log}\n"
                    "常见原因和解决方案:\n"
                    "1. 输入视频格式不支持 - 请尝试转换为MP4格式\n"
                    "2. 输出路径无写入权限 - 请选择其他输出目录\n"
                    "3. FFmpeg版本不兼容 - 请更新到最新版本\n"
                    "4. 系统资源不足 - 关闭其他程序重试"
                )
                self.root.after(0, lambda: self.show_error(error_msg))

            except Exception as e:
                self.root.after(0, lambda: self.show_error(f"发生意外错误: {str(e)}"))

            finally:
                # 清理临时目录
                if os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir, ignore_errors=True)
                self.root.after(0, lambda: self.process_btn.config(state=tk.NORMAL))

        except Exception as e:
            self.root.after(0, lambda: self.show_error(f"初始化处理失败: {str(e)}"))

if __name__ == "__main__":
    root = tk.Tk()
    app = VideoProcessorGUI(root)
    root.mainloop()
