import re
from io import StringIO

def fix_indentation(source_code):
    """
    Fixes indentation in Python source code while preserving:
    - Multi-line strings
    - Comments
    - Original code structure
    """
    lines = source_code.splitlines()
    output = StringIO()
    indent_level = 0
    in_multiline_string = False
    multiline_string_indent = 0
    
    for line in lines:
        stripped = line.strip()
        
        # Skip empty lines
        if not stripped:
            output.write('\n')
            continue
            
        # Handle multi-line strings
        if in_multiline_string:
            if stripped.endswith('"""') or stripped.endswith("'''"):
                in_multiline_string = False
                output.write(line + '\n')
                continue
            output.write(line + '\n')
            continue
            
        if stripped.startswith('"""') or stripped.startswith("'''"):
            in_multiline_string = True
            output.write(line + '\n')
            continue
            
        # <PERSON>le comments
        if stripped.startswith('#'):
            output.write(' ' * (indent_level * 4) + stripped + '\n')
            continue
            
        # Handle block endings
        if stripped.startswith(('elif ', 'else:', 'except ', 'finally:')):
            indent_level -= 1
            
        # Write line with proper indentation
        output.write(' ' * (indent_level * 4) + stripped + '\n')
        
        # Handle block starts
        if stripped.endswith((':', '\\')) and not stripped.startswith(('#', "'", '"')):
            indent_level += 1
            
        # Handle block endings
        if stripped.startswith(('return ', 'break', 'continue', 'pass', 'raise ')):
            indent_level -= 1
            
    return output.getvalue()

if __name__ == '__main__':
    import sys
    if len(sys.argv) != 2:
        print("Usage: python indent_fixer.py &lt;input_file&gt;")
        sys.exit(1)
        
    with open(sys.argv[1], 'r', encoding='utf-8') as f:
        code = f.read()
        
    fixed_code = fix_indentation(code)
    
    with open(sys.argv[1], 'w', encoding='utf-8') as f:
        f.write(fixed_code)
