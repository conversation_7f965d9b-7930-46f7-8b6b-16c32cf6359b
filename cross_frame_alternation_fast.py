import os
import subprocess
import tempfile
import shutil
import random
import string
import time
import concurrent.futures
import threading
import glob

def generate_random_string(length=8):
    """生成随机字符串作为临时文件名"""
    letters = string.ascii_lowercase
    return ''.join(random.choice(letters) for i in range(length))

class CacheManager:
    """缓存管理器，负责一边处理一边清理临时文件"""
    
    def __init__(self, temp_dir):
        self.temp_dir = temp_dir
        self.temp_files = []
        self.lock = threading.Lock()
        self.cleanup_thread = None
        self.stop_cleanup = False
        
    def add_temp_file(self, file_path):
        """添加临时文件到管理列表"""
        with self.lock:
            self.temp_files.append(file_path)
    
    def start_cleanup_thread(self):
        """启动清理线程"""
        self.cleanup_thread = threading.Thread(target=self._cleanup_worker)
        self.cleanup_thread.daemon = True
        self.cleanup_thread.start()
    
    def _cleanup_worker(self):
        """清理工作线程"""
        while not self.stop_cleanup:
            time.sleep(5)  # 每5秒检查一次
            with self.lock:
                files_to_remove = []
                for file_path in self.temp_files:
                    if os.path.exists(file_path):
                        try:
                            # 检查文件是否正在被使用
                            if self._is_file_ready_for_cleanup(file_path):
                                os.remove(file_path)
                                files_to_remove.append(file_path)
                                print(f"已清理临时文件: {os.path.basename(file_path)}")
                        except Exception as e:
                            print(f"清理文件失败 {file_path}: {str(e)}")
                
                # 从列表中移除已清理的文件
                for file_path in files_to_remove:
                    self.temp_files.remove(file_path)
    
    def _is_file_ready_for_cleanup(self, file_path):
        """检查文件是否可以安全清理"""
        try:
            # 尝试重命名文件来检查是否被占用
            temp_name = file_path + ".tmp_check"
            os.rename(file_path, temp_name)
            os.rename(temp_name, file_path)
            return True
        except:
            return False
    
    def stop_cleanup_thread(self):
        """停止清理线程"""
        self.stop_cleanup = True
        if self.cleanup_thread:
            self.cleanup_thread.join(timeout=2)
    
    def cleanup_all(self):
        """清理所有临时文件"""
        self.stop_cleanup_thread()
        with self.lock:
            for file_path in self.temp_files:
                if os.path.exists(file_path):
                    try:
                        os.remove(file_path)
                        print(f"最终清理: {os.path.basename(file_path)}")
                    except Exception as e:
                        print(f"最终清理失败 {file_path}: {str(e)}")
            self.temp_files.clear()
        
        # 清理临时目录
        if os.path.exists(self.temp_dir):
            try:
                shutil.rmtree(self.temp_dir, ignore_errors=True)
                print(f"已清理临时目录: {self.temp_dir}")
            except Exception as e:
                print(f"清理临时目录失败: {str(e)}")

def create_cross_alternating_video_fast(main_video_path, aux_video_path, output_path, ffmpeg_path="ffmpeg", speed_priority=False):
    """
    创建交叉帧替换视频，奇数帧使用主视频，偶数帧使用辅助视频 - 优化版本
    一边合成一边清理缓存
    
    参数:
        main_video_path: 主视频路径
        aux_video_path: 辅助视频路径
        output_path: 输出文件路径
        ffmpeg_path: FFmpeg可执行文件路径
        speed_priority: 是否优先考虑速度（会降低一些质量）
    
    返回:
        成功返回(True, "", temp_dir)，失败返回(False, 错误信息, temp_dir)
        temp_dir是创建的临时目录路径，用于后续清理
    """
    try:
        # 在当前目录创建临时目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        temp_dir_name = f"temp_{int(time.time())}"
        temp_dir = os.path.join(current_dir, temp_dir_name)
        
        # 确保临时目录存在
        if not os.path.exists(temp_dir):
            os.makedirs(temp_dir)
        
        # 初始化缓存管理器
        cache_manager = CacheManager(temp_dir)
        cache_manager.start_cleanup_thread()
        
        try:
            # 准备临时文件路径
            temp_main_120fps = os.path.join(temp_dir, "main_120fps.mp4")
            temp_aux_120fps = os.path.join(temp_dir, "aux_120fps.mp4")
            temp_audio = os.path.join(temp_dir, "audio.aac")
            temp_output_no_audio = os.path.join(temp_dir, "output_no_audio.mp4")
            
            # 添加到缓存管理器
            cache_manager.add_temp_file(temp_main_120fps)
            cache_manager.add_temp_file(temp_aux_120fps)
            cache_manager.add_temp_file(temp_audio)
            cache_manager.add_temp_file(temp_output_no_audio)
            
            # 设置编码参数
            if speed_priority:
                # 速度优先的编码参数
                video_codec_params = [
                    "-c:v", "libx264", 
                    "-preset", "ultrafast", 
                    "-tune", "fastdecode",
                    "-profile:v", "baseline",
                    "-level", "3.0",
                    "-x264opts", "cabac=0:ref=1:bframes=0:weightp=0:subme=0:me=dia:trellis=0:8x8dct=0",
                    "-pix_fmt", "yuv420p"
                ]
            else:
                # 质量优先的编码参数
                video_codec_params = [
                    "-c:v", "libx264", 
                    "-preset", "medium", 
                    "-profile:v", "high",
                    "-pix_fmt", "yuv420p"
                ]
            
            # 并行处理主视频和辅助视频转换为120fps
            print("开始并行处理视频...")
            
            with concurrent.futures.ThreadPoolExecutor(max_workers=2) as executor:
                # 提交主视频处理任务
                main_future = executor.submit(
                    convert_video_to_120fps, 
                    main_video_path, 
                    temp_main_120fps, 
                    ffmpeg_path, 
                    video_codec_params,
                    speed_priority
                )
                
                # 提交辅助视频处理任务
                aux_future = executor.submit(
                    convert_video_to_120fps, 
                    aux_video_path, 
                    temp_aux_120fps, 
                    ffmpeg_path, 
                    video_codec_params,
                    speed_priority
                )
                
                # 等待两个任务完成
                main_result = main_future.result()
                aux_result = aux_future.result()
                
                if not main_result[0]:
                    return False, f"处理主视频失败: {main_result[1]}", temp_dir
                
                if not aux_result[0]:
                    return False, f"处理辅助视频失败: {aux_result[1]}", temp_dir
            
            print("视频转换为120fps完成")
            
            # 提取主视频音频
            print("提取主视频音频...")
            extract_audio_cmd = [
                ffmpeg_path, "-y",
                "-i", main_video_path,
                "-vn", "-acodec", "copy",
                temp_audio
            ]
            subprocess.run(extract_audio_cmd, check=True)
            
            # 使用流式处理创建交叉帧视频
            print("创建交叉帧视频...")
            
            # 使用更高效的滤镜链
            filter_complex = (
                "[0:v]select='not(mod(n,2))',setpts=N/FR/TB[main_odd];"
                "[1:v]select='mod(n,2)',setpts=N/FR/TB[aux_even];"
                "[main_odd][aux_even]interleave=nb_inputs=2[outv]"
            )
            
            interleave_cmd = [
                ffmpeg_path, "-y",
                "-i", temp_main_120fps,
                "-i", temp_aux_120fps,
                "-filter_complex", filter_complex,
                "-map", "[outv]"
            ]
            
            # 添加编码参数
            interleave_cmd.extend(video_codec_params)
            
            # 添加码率参数
            interleave_cmd.extend(["-b:v", "20000k"])
            
            # 添加输出文件
            interleave_cmd.append(temp_output_no_audio)
            
            # 执行命令
            subprocess.run(interleave_cmd, check=True)
            
            # 合并视频和音频
            print("合并视频和音频...")
            merge_cmd = [
                ffmpeg_path, "-y",
                "-i", temp_output_no_audio,
                "-i", temp_audio,
                "-c:v", "copy",
                "-c:a", "aac", "-b:a", "192k",
                "-shortest",
                output_path
            ]
            subprocess.run(merge_cmd, check=True)
            
            print(f"视频生成完成: {output_path}")
            
            # 停止缓存管理器并清理
            cache_manager.cleanup_all()
            
            return True, "", temp_dir
            
        finally:
            # 确保缓存管理器被正确清理
            cache_manager.cleanup_all()
    
    except Exception as e:
        return False, str(e), temp_dir

def convert_video_to_120fps(input_path, output_path, ffmpeg_path, codec_params, speed_priority):
    """将视频转换为120fps"""
    try:
        # 构建命令
        cmd = [ffmpeg_path, "-y", "-i", input_path]
        
        # 添加滤镜
        if speed_priority:
            # 速度优先模式使用更快的插帧方法
            cmd.extend(["-filter:v", "fps=120"])
        else:
            # 质量优先模式使用更好的插帧方法
            cmd.extend(["-filter:v", "minterpolate=fps=120:mi_mode=mci:mc_mode=aobmc:me_mode=bidir:vsbmc=1"])
        
        # 添加编码参数
        cmd.extend(codec_params)
        
        # 不包含音频
        cmd.extend(["-an"])
        
        # 添加输出文件
        cmd.append(output_path)
        
        # 执行命令
        subprocess.run(cmd, check=True)
        
        return True, ""
    except Exception as e:
        return False, str(e)

if __name__ == "__main__":
    # 测试代码
    main_video = "main.mp4"
    aux_video = "aux.mp4"
    output_video = "output.mp4"
    
    success, error, temp_dir = create_cross_alternating_video_fast(main_video, aux_video, output_video, speed_priority=True)
    if success:
        print("处理成功！")
    else:
        print(f"处理失败: {error}")
