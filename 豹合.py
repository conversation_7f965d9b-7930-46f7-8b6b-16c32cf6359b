import sys
import os
import re
import time
import json
import subprocess
from PyQt5.QtWidgets import (QApplication, QMainWindow, QPushButton, QVBoxLayout, 
                           QHBoxLayout, QWidget, QFileDialog, QTextEdit, QCheckBox, 
                           QLabel, QMessageBox, QProgressBar, QListWidget, QAbstractItemView, QMenu)
from PyQt5.QtCore import QThread, pyqtSignal, Qt, QLocale, QLibraryInfo, QEvent
from PyQt5.QtGui import QPixmap, QGuiApplication
from PyQt5.Qt import QTranslator
class VideoFusionWorker(QThread):
    """视频豹合工作线程"""
    progress_updated = pyqtSignal(int)
    log_message = pyqtSignal(str)
    process_finished = pyqtSignal(bool, str, str)

    def __init__(self, main_videos, aux_videos, save_dir, use_cpu=False, use_quality=True):
        super().__init__()
        self.main_videos = main_videos
        self.aux_videos = aux_videos
        self.save_dir = save_dir
        self.process = None
        self.stopped = False
        self.video_duration = 0
        self.use_cuda = not use_cpu and self.check_cuda_support()
        self.current_index = 0
        self.total_pairs = min(len(main_videos), len(aux_videos))
        self.current_output_path = None
        self.use_quality = use_quality

    def check_cuda_support(self):
        """检查系统是否支持CUDA"""
        try:
            result = subprocess.run(['ffmpeg', '-hwaccels'], capture_output=True, text=True)
            if 'cuda' in result.stdout.lower():
                self.log_message.emit('检测到CUDA支持，将使用硬件加速')
                return True
            self.log_message.emit('未检测到CUDA支持，将使用软件编码')
            return False
        except Exception as e:
            self.log_message.emit(f'CUDA检测失败: {str(e)}，将使用软件编码')
            return False

    def run(self):
        try:
            for i in range(self.total_pairs):
                if self.stopped:
                    break
                self.current_index = i
                aux_video_path = self.main_videos[i]  # 交换处理顺序
                main_video_path = self.aux_videos[i]  # 交换处理顺序
                self.log_message.emit(f'开始处理第 {i + 1}/{self.total_pairs} 对视频')
                aux_video_name = os.path.splitext(os.path.basename(aux_video_path))[0]
                base_name = f'{aux_video_name}_快手'
                output_name = f'{base_name}.mp4'
                output_path = os.path.join(self.save_dir, output_name)
                counter = 1
                while os.path.exists(output_path):
                    output_name = f'{base_name}_{counter}.mp4'
                    output_path = os.path.join(self.save_dir, output_name)
                    counter += 1
                self.current_output_path = output_path
                probe_cmd = ['ffprobe', '-v', 'error', '-select_streams', 'v:0', '-show_entries', 'stream=duration', '-of', 'json', str(aux_video_path)]
                if sys.platform.startswith('win'):
                    startupinfo = subprocess.STARTUPINFO()
                    startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                    startupinfo.wShowWindow = subprocess.SW_HIDE
                    probe_result = subprocess.run(probe_cmd, capture_output=True, text=True, encoding='utf-8', errors='replace', startupinfo=startupinfo)
                else:
                    probe_result = subprocess.run(probe_cmd, capture_output=True, text=True, encoding='utf-8', errors='replace')
                video_info = json.loads(probe_result.stdout)
                if 'streams' not in video_info or not video_info['streams']:
                    self.log_message.emit('错误：无法获取主视频的视频流信息')
                    self.process_finished.emit(False, '无法获取主视频的视频流信息', '')
                    continue

                try:
                    duration = float(video_info['streams'][0]['duration'])
                    self.log_message.emit(f'视频时长: {duration:.2f} 秒')
                except (KeyError, ValueError, TypeError):
                    pass
                else:
                    self.video_duration = duration
                    self.log_message.emit(f'根据第一个视频时长（{duration:.2f}秒）显示时长进度')
                    probe_cmd = ['ffprobe', '-v', 'error', '-select_streams', 'v:0', '-show_entries', 'stream=width,height', '-of', 'json', str(main_video_path)]
                    if sys.platform.startswith('win'):
                        startupinfo = subprocess.STARTUPINFO()
                        startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                        startupinfo.wShowWindow = subprocess.SW_HIDE
                        probe_result = subprocess.run(probe_cmd, capture_output=True, text=True, encoding='utf-8', errors='replace', startupinfo=startupinfo)
                    else:
                        probe_result = subprocess.run(probe_cmd, capture_output=True, text=True, encoding='utf-8', errors='replace')
                    video_info = json.loads(probe_result.stdout)
                    width = int(video_info['streams'][0]['width'])
                    height = int(video_info['streams'][0]['height'])
                    self.log_message.emit(f'左边视频尺寸: {width}x{height}')
                    ffmpeg_cmd = ['ffmpeg', '-hwaccel', 'cuda' if self.use_cuda else 'none', '-i', aux_video_path, '-i', main_video_path, '-map', '0:v', '-map', '1:v', '-map', '0:a', '-map', '1:a', '-s', f'{width}x{height}', '-map_metadata', '-1']
                    if self.use_cuda:
                        if self.use_quality:
                            ffmpeg_cmd.extend(['-c:v:0', 'h264_nvenc', '-b:v:0', '4000k', '-maxrate:v:0', '4000k', '-bufsize:v:0', '4000k', '-preset', 'p4', '-c:v:1', 'h264_nvenc', '-b:v:1', '12000k', '-maxrate:v:1', '12000k', '-bufsize:v:1', '12000k', '-preset', 'p4'])
                        else:
                            ffmpeg_cmd.extend(['-c:v:0', 'h264_nvenc', '-b:v:0', '2000k', '-maxrate:v:0', '2000k', '-bufsize:v:0', '2000k', '-preset', 'p4', '-c:v:1', 'h264_nvenc', '-b:v:1', '6000k', '-maxrate:v:1', '6000k', '-bufsize:v:1', '6000k', '-preset', 'p4'])
                    else:
                        if self.use_quality:
                            ffmpeg_cmd.extend(['-c:v:0', 'libx264', '-b:v:0', '4000k', '-maxrate:v:0', '4000k', '-bufsize:v:0', '4000k', '-x264opts', 'nal-hrd=cbr', '-c:v:1', 'libx264', '-b:v:1', '12000k', '-maxrate:v:1', '12000k', '-bufsize:v:1', '12000k', '-x264opts', 'nal-hrd=cbr'])
                        else:
                            ffmpeg_cmd.extend(['-c:v:0', 'libx264', '-b:v:0', '2000k', '-maxrate:v:0', '2000k', '-bufsize:v:0', '2000k', '-x264opts', 'nal-hrd=cbr', '-c:v:1', 'libx264', '-b:v:1', '6000k', '-maxrate:v:1', '6000k', '-bufsize:v:1', '6000k', '-x264opts', 'nal-hrd=cbr'])
                    ffmpeg_cmd.extend(['-c:a:0', 'aac', '-b:a:0', '180k', '-c:a:1', 'aac', '-b:a:1', '280k', '-strict', 'experimental', '-f', 'mp4', '-y', self.current_output_path])
                    if sys.platform.startswith('win'):
                        startupinfo = subprocess.STARTUPINFO()
                        startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                        startupinfo.wShowWindow = subprocess.SW_HIDE
                        self.process = subprocess.Popen(ffmpeg_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True, encoding='utf-8', errors='replace', startupinfo=startupinfo)
                    else:
                        self.process = subprocess.Popen(ffmpeg_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True, encoding='utf-8', errors='replace')
                    start_time = time.time()
                    last_progress = 0
                    last_update_time = start_time
                    last_log_progress = 0
                    while True:
                        if self.stopped:
                            self.process.terminate()
                            break
                        line = self.process.stderr.readline()
                        if not line and self.process.poll() is not None:
                            break
                        time_match = re.search('time=(\\d+):(\\d+):(\\d+)\\.(\\d+)', line)
                        if time_match:
                            hours, minutes, seconds, centiseconds = map(int, time_match.groups())
                            current_time = hours * 3600 + minutes * 60 + seconds + centiseconds / 100
                            progress = min(99, max(0, int(current_time / self.video_duration * 100)))
                            current_time_stamp = time.time()
                            if progress > last_progress and (current_time_stamp - last_update_time >= 0.5 or progress - last_progress >= 5):
                                last_progress = progress
                                last_update_time = current_time_stamp
                                self.progress_updated.emit(progress)
                                if progress >= last_log_progress + 10:
                                    last_log_progress = progress - progress % 10
                                    elapsed = current_time_stamp - start_time
                                    if progress > 0:
                                        estimated_total = elapsed / progress * 100
                                        remaining = max(0, estimated_total - elapsed)
                                        self.log_message.emit(f'KS视频进度: {progress}% (处理到: {int(current_time)}秒/{self.video_duration:.1f}秒, 预计剩余: {int(remaining)}秒)')
                    stdout, stderr = self.process.communicate()
                    self.progress_updated.emit(100)
                    total_time = time.time() - start_time
                    self.log_message.emit(f'视频处理完成，总耗时: {int(total_time)}秒')
                    if self.process.returncode == 0:
                        self.log_message.emit('KS视频处理完成')
                        self.log_message.emit(f'视频已保存至: {self.current_output_path}')
                        self.process_finished.emit(True, '', self.current_output_path)
                    else:
                        error_msg = f'KS视频失败，错误代码：{self.process.returncode}\n'
                        if stderr:
                            error_msg += f'错误信息：{stderr}'
                        self.log_message.emit(error_msg)
                        self.process_finished.emit(False, error_msg, '')
            self.log_message.emit('所有视频对处理完成')
            self.progress_updated.emit(100)
        except Exception as e:
            duration = 60.0
            self.log_message.emit(f'无法获取视频时长，使用默认值: {duration} 秒')
            error_msg = f'KS视频过程中出错: {str(e)}'
            self.log_message.emit(error_msg)
            self.process_finished.emit(False, error_msg, '')

    def stop(self):
        """停止处理"""
        self.stopped = True
        if self.process:
            self.process.terminate()

class VideoProcessor(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("快手视频处理工具")
        self.setGeometry(100, 100, 1000, 800)

        self.main_videos = []
        self.aux_videos = []
        self.save_dir = ""
        # 设置默认路径为桌面或脚本所在目录
        self.default_dir = os.path.join(os.path.expanduser("~"), "Desktop")
        if hasattr(sys, '_MEIPASS'):
            self.default_dir = os.path.dirname(os.path.abspath(sys.executable))
        elif __file__:
            self.default_dir = os.path.dirname(os.path.abspath(__file__))

        self.init_ui()

    def init_ui(self):
        # 设置应用程序样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QWidget {
                font-family: 'Microsoft YaHei';
                font-size: 14px;
            }
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QListWidget {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
            }
            QListWidget::item {
                padding: 6px;
                border-bottom: 1px solid #eee;
            }
            QListWidget::item:hover {
                background-color: #f0f0f0;
            }
            QListWidget::item:selected {
                background-color: #e6f7ff;
                color: #1890ff;
            }
            QTextEdit {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 4px;
            }
            QProgressBar {
                border: 1px solid #ddd;
                border-radius: 4px;
                text-align: center;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                width: 10px;
            }
            QLabel {
                color: #333;
            }
            QCheckBox {
                spacing: 5px;
            }
        """)

        main_layout = QVBoxLayout()
        
        # 创建左右目录布局
        dir_layout = QHBoxLayout()
        
        # 左侧主视频目录
        self.main_video_list = QListWidget()
        self.main_video_list.setStyleSheet("""
            QListWidget {
                padding: 10px;
                border: 1px solid #ddd;
                border-radius: 5px;
                background-color: white;
                font-size: 14px;
            }
            QListWidget::item {
                padding: 6px;
                border-bottom: 1px solid #eee;
            }
            QListWidget::item:selected {
                background-color: #e6f7ff;
                color: #1890ff;
            }
            QListWidget::item:hover {
                background-color: #f5f5f5;
            }
        """)
        self.main_video_list.setAlternatingRowColors(True)
        self.main_video_list.setContextMenuPolicy(Qt.CustomContextMenu)
        self.main_video_list.customContextMenuRequested.connect(self.show_video_list_context_menu)
        self.main_video_label = QLabel("辅助视频列表 (下方显示选择的视频文件)")
        self.select_main_videos_btn = QPushButton("📁 选择辅助视频文件夹")
        self.select_main_videos_btn.setStyleSheet("QPushButton { padding: 8px; }")
        left_dir_layout = QVBoxLayout()
        left_dir_layout.addWidget(self.main_video_label)
        left_dir_layout.addWidget(self.select_main_videos_btn)
        left_dir_layout.addWidget(self.main_video_list)
        
        # 右侧辅助视频目录
        self.aux_video_list = QListWidget()
        self.aux_video_list.setStyleSheet("""
            QListWidget {
                padding: 10px;
                border: 1px solid #ddd;
                border-radius: 5px;
                background-color: white;
                font-size: 14px;
            }
            QListWidget::item {
                padding: 6px;
                border-bottom: 1px solid #eee;
            }
            QListWidget::item:selected {
                background-color: #e6f7ff;
                color: #1890ff;
            }
            QListWidget::item:hover {
                background-color: #f5f5f5;
            }
        """)
        self.aux_video_list.setAlternatingRowColors(True)
        self.aux_video_list.setContextMenuPolicy(Qt.CustomContextMenu)
        self.aux_video_list.customContextMenuRequested.connect(self.show_video_list_context_menu)
        self.aux_video_label = QLabel("主视频列表 (下方显示选择的视频文件)")
        self.select_aux_videos_btn = QPushButton("📁 选择主视频文件夹")
        self.select_aux_videos_btn.setStyleSheet("QPushButton { padding: 8px; }")
        right_dir_layout = QVBoxLayout()
        right_dir_layout.addWidget(self.aux_video_label)
        right_dir_layout.addWidget(self.select_aux_videos_btn)
        right_dir_layout.addWidget(self.aux_video_list)
        
        dir_layout.addLayout(left_dir_layout)
        dir_layout.addLayout(right_dir_layout)
        main_layout.addLayout(dir_layout)
        
        # 控制区域
        control_layout = QHBoxLayout()
        
        # 保存目录选择
        self.select_save_dir_btn = QPushButton("💾 选择保存目录")
        self.select_save_dir_btn.setStyleSheet("QPushButton { padding: 8px; }")
        control_layout.addWidget(self.select_save_dir_btn)
        
        # 处理选项
        option_layout = QHBoxLayout()
        self.use_cpu_checkbox = QCheckBox("🖥️ 使用CPU（不使用CUDA）")
        self.use_quality_checkbox = QCheckBox("✨ 高质量编码")
        self.use_quality_checkbox.setChecked(True)
        option_layout.addWidget(self.use_cpu_checkbox)
        option_layout.addWidget(self.use_quality_checkbox)
        control_layout.addLayout(option_layout)
        
        main_layout.addLayout(control_layout)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setTextVisible(True)
        main_layout.addWidget(self.progress_bar)

        # 操作按钮
        btn_layout = QHBoxLayout()
        self.start_btn = QPushButton("🚀 开始处理")
        self.start_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; padding: 10px; }")
        btn_layout.addWidget(self.start_btn)

        self.stop_btn = QPushButton("🛑 停止处理")
        self.stop_btn.setStyleSheet("QPushButton { background-color: #f44336; color: white; padding: 10px; }")
        self.stop_btn.setEnabled(False)
        btn_layout.addWidget(self.stop_btn)

        main_layout.addLayout(btn_layout)

        # 日志区域
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setStyleSheet("QTextEdit { background-color: #f8f9fa; }")
        main_layout.addWidget(self.log_text)

        # 状态栏
        self.status_label = QLabel("就绪")
        main_layout.addWidget(self.status_label)

        container = QWidget()
        container.setLayout(main_layout)
        container.setStyleSheet("QWidget { border: 1px solid #1890ff; }")
        self.setCentralWidget(container)

        # 连接信号
        self.select_main_videos_btn.clicked.connect(lambda: self.select_videos(True))
        self.select_aux_videos_btn.clicked.connect(lambda: self.select_videos(False))
        self.select_save_dir_btn.clicked.connect(self.select_save_dir)
        self.start_btn.clicked.connect(self.start_fusion)
        self.stop_btn.clicked.connect(self.stop_fusion)

    def select_videos(self, is_main=True):
        """选择视频文件夹"""
        options = QFileDialog.Options()
        try:
            QLocale.setDefault(QLocale(QLocale.Chinese, QLocale.China))
            folder = QFileDialog.getExistingDirectory(
                self, 
                "选择主视频文件夹" if is_main else "选择辅助视频文件夹",
                directory=self.default_dir,
                options=options
            )
            if folder:
                folder = os.path.normpath(folder)
                video_files = []
                for file in os.listdir(folder):
                    file_path = os.path.join(folder, file)
                    if os.path.isfile(file_path) and file.lower().endswith(('.mp4', '.avi', '.mkv')):
                        video_files.append(file_path)
                
                if is_main:
                    self.main_videos = video_files
                    self.main_video_list.clear()
                    for video in video_files:
                        self.main_video_list.addItem(os.path.basename(video))
                    self.log_text.append(f"已选择 {len(video_files)} 个主视频")
                else:
                    self.aux_videos = video_files
                    self.aux_video_list.clear()
                    for video in video_files:
                        self.aux_video_list.addItem(os.path.basename(video))
                    self.log_text.append(f"已选择 {len(video_files)} 个辅助视频")
        except Exception as e:
            self.log_text.append(f"选择视频文件夹出错: {str(e)}")

    def select_save_dir(self):
        options = QFileDialog.Options()
        try:
            # 强制设置文件对话框语言为中文
            QLocale.setDefault(QLocale(QLocale.Chinese, QLocale.China))
            self.save_dir = QFileDialog.getExistingDirectory(
                self, 
                "选择保存目录", 
                directory=self.default_dir,
                options=options
            )
            if self.save_dir:
                self.save_dir = os.path.normpath(self.save_dir)
                self.log_text.append(f"选择了保存目录: {self.save_dir}")
        except Exception as e:
            self.log_text.append(f"选择保存目录出错: {str(e)}")

    def start_fusion(self):
        if not self.main_videos:
            self.log_text.append("请先选择主视频文件夹")
            return
        if not self.aux_videos:
            self.log_text.append("请先选择辅助视频文件夹")
            return
        if not self.save_dir:
            self.log_text.append("请先选择保存目录")
            return

        self.worker = VideoFusionWorker(self.main_videos, self.aux_videos, self.save_dir, self.use_cpu_checkbox.isChecked(), self.use_quality_checkbox.isChecked())
        self.worker.progress_updated.connect(self.update_progress)
        self.worker.log_message.connect(self.log_message)
        self.worker.process_finished.connect(self.process_finished)
        self.worker.start()

    def stop_fusion(self):
        if hasattr(self, 'worker') and self.worker is not None:
            self.worker.stop()

    def update_progress(self, progress):
        self.log_text.append(f"进度: {progress}%")

    def log_message(self, message):
        self.log_text.append(message)

    def process_finished(self, success, error_msg, output_path):
        if success:
            self.log_text.append(f"视频合完成，保存路径: {output_path}")
        else:
            self.log_text.append(f"视频合失败: {error_msg}")

    def show_video_list_context_menu(self, pos):
        """显示视频列表的右键菜单"""
        menu = QMenu()
        delete_action = menu.addAction("删除")
        action = menu.exec_(self.sender().mapToGlobal(pos))
        
        if action == delete_action:
            current_list = self.sender()
            current_item = current_list.currentItem()
            if current_item:
                row = current_list.row(current_item)
                if current_list == self.main_video_list:
                    del self.main_videos[row]
                    self.main_video_list.takeItem(row)
                    self.log_text.append(f"已删除主视频: {current_item.text()}")
                else:
                    del self.aux_videos[row]
                    self.aux_video_list.takeItem(row)
                    self.log_text.append(f"已删除辅助视频: {current_item.text()}")

def main():
    app = QApplication(sys.argv)
    
    # 设置中文语言环境
    app.setApplicationName("视频合成工具")
    QGuiApplication.setApplicationDisplayName("视频合成工具")
    
    # 设置系统语言环境为中文
    locale = QLocale(QLocale.Chinese, QLocale.China)
    QLocale.setDefault(locale)
    app.setLayoutDirection(locale.textDirection())
    
    # 静默设置文件对话框标签文本
    try:
        temp_dialog = QFileDialog()
        temp_dialog.setLabelText(QFileDialog.FileName, "文件名")
        temp_dialog.setLabelText(QFileDialog.Accept, "确定")
        temp_dialog.setLabelText(QFileDialog.Reject, "取消")
        temp_dialog.setLabelText(QFileDialog.FileType, "文件类型")
        temp_dialog.setLabelText(QFileDialog.LookIn, "查看")
    except Exception:
        pass
    
    window = VideoProcessor()
    window.show()
    sys.exit(app.exec())

if __name__ == '__main__':
    main()