# Decompiled with PyLingual (https://pylingual.io)
# Internal filename: 皇冠.py
# Bytecode version: 3.11a7e (3495)
# Source timestamp: 1970-01-01 00:00:00 UTC (0)

import os
import sys
import time
import datetime
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import subprocess
import threading
import random
import json
import psutil
VERSION = '1.3.0'
APP_NAME = '皇冠AB视频处理工具'
AUTHOR = '一号团队'
USING_TTK_BOOTSTRAP = False
try:
    import psutil
except ImportError:
    print('⚠️  检测到安全模块缺失，正在尝试安装...')
    try:
        import subprocess
        result = subprocess.run([sys.executable, '-m', 'pip', 'install', 'psutil'], capture_output=True, text=True, creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0)
        if result.returncode == 0:
            import psutil
            print('✅ 安全模块安装成功')
        else:  # inserted
            print('⚠️  安全模块安装失败，使用基础保护模式')
    except:
        print('⚠️  使用基础保护模式')
try:
    import ttkbootstrap as ttk
    from ttkbootstrap import Style
    from ttkbootstrap.constants import *
    from ttkbootstrap.toast import ToastNotification
    from ttkbootstrap.dialogs import Messagebox
    USING_TTK_BOOTSTRAP = True
except ImportError as e:
    print('❌ 缺少必需的ttkbootstrap库')
    print('==================================================')
    print('错误详情:')
    print(f'  {e}')
    print()
    print('解决方案:')
    print('1. 安装ttkbootstrap:')
    print('   pip install ttkbootstrap')
    print()
    print('2. 或者升级到最新版本:')
    print('   pip install --upgrade ttkbootstrap')
    print()
    print('3. 如果使用虚拟环境，请确保在正确的环境中安装')
    print('==================================================')
    try:
        user_input = input('是否尝试自动安装ttkbootstrap? (y/n): ').lower().strip()
        if user_input in ['y', 'yes', '是']:
            print('正在安装ttkbootstrap...')
            import subprocess
            result = subprocess.run([sys.executable, '-m', 'pip', 'install', 'ttkbootstrap'], capture_output=True, text=True, creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0)
            if result.returncode == 0:
                print('✅ 安装成功！请重新运行程序。')
            else:  # inserted
                print('❌ 安装失败，请手动安装:')
                print('   pip install ttkbootstrap')
        else:  # inserted
            print('请手动安装ttkbootstrap后重新运行程序')
    except KeyboardInterrupt:
        print('\n用户取消操作')
    except Exception:
        print('请手动安装ttkbootstrap后重新运行程序')
    sys.exit(1)

def show_security_warning(detected_tools):
    """显示安全警告弹窗"""  # inserted
    try:
        import tkinter as tk
        from tkinter import messagebox
        root = tk.Tk()
        root.withdraw()
        tools_list = '\n'.join([f'• {tool}' for tool in detected_tools[:5]])
        if len(detected_tools) > 5:
            tools_list = tools_list 6 6 | f'\n• 以及其他 {len(detected_tools) - 5} 个工具...'
        warning_message = '⚠️ 系统环境检测\n\n检测到当前运行环境存在兼容性问题，可能影响程序正常运行。\n\n为确保软件稳定性和数据安全，程序将自动退出。\n\n建议操作：\n1. 关闭其他正在运行的软件后重试\n2. 重启计算机后再次运行程序\n3. 如问题持续存在，请联系技术支持\n\n📞 一号电商微信: Nl334455666'
        messagebox.showerror('⚠️ 系统环境兼容性检测', warning_message)
        root.destroy()
    except Exception:
        print('⚠️ 系统环境兼容性检测')
        print('检测到运行环境存在兼容性问题')
        print('程序将自动退出以确保稳定性')

def show_debug_warning():
    """显示调试器检测警告"""  # inserted
    try:
        import tkinter as tk
        from tkinter import messagebox
        root = tk.Tk()
        root.withdraw()
        warning_message = '⚠️ 运行环境检测\n\n检测到当前运行环境可能存在兼容性问题，这会影响程序的正常功能。\n\n程序将自动退出以确保稳定运行。\n\n如需技术支持，请联系：\n📞 一号电商微信: Nl334455666'
        messagebox.showerror('⚠️ 运行环境兼容性检测', warning_message)
        root.destroy()
    except Exception:
        print('⚠️ 运行环境兼容性检测')
        print('检测到环境兼容性问题，程序将自动退出')

def show_parent_warning(parent_name):
    """显示父进程警告"""  # inserted
    try:
        import tkinter as tk
        from tkinter import messagebox
        root = tk.Tk()
        root.withdraw()
        warning_message = '⚠️ 启动环境检测\n\n检测到程序启动环境存在异常，可能影响软件正常运行。\n\n为确保程序稳定性，将立即退出。\n\n建议操作：\n1. 直接双击程序图标启动\n2. 避免通过第三方工具启动程序\n3. 如问题持续，请联系技术支持\n\n如需技术支持，请联系：\n📞 一号电商微信: Nl334455666'
        messagebox.showerror('⚠️ 启动环境兼容性检测', warning_message)
        root.destroy()
    except Exception:
        print('⚠️ 启动环境兼容性检测')
        print('检测到启动环境异常，程序将自动退出')
        print('建议直接双击程序图标启动')

def show_file_detection_warning(detected_files):
    """显示文件检测警告"""  # inserted
    try:
        import tkinter as tk
        from tkinter import messagebox
        root = tk.Tk()
        root.withdraw()
        files_list = '\n'.join([f'• {file}' for file in detected_files[:5]])
        if len(detected_files) > 5:
            files_list = files_list 6 6 | f'\n• 以及其他 {len(detected_files) - 5} 个文件...'
        warning_message = '⚠️ 系统兼容性检测\n\n检测到您的系统中存在可能影响程序稳定性的软件。\n\n为确保最佳使用体验，建议您：\n1. 清理系统中的无用软件\n2. 关闭不必要的后台程序\n3. 保持系统环境整洁\n\n💡 程序将继续运行，建议优化系统环境以获得更好性能\n\n如需技术支持，请联系：\n📞 一号电商微信: Nl334455666'
        messagebox.showwarning('⚠️ 系统兼容性优化建议', warning_message)
        root.destroy()
    except Exception:
        print('⚠️ 系统兼容性检测')
        print('检测到可能影响程序稳定性的软件')
        print('建议清理系统环境以获得更好性能')

def check_dangerous_files():
    """检测系统中的危险文件"""  # inserted
    try:
        import os
        import glob
        common_paths = ['C:\\Program Files\\', 'C:\\Program Files (x86)\\', 'C:\\Users\\<USER>\\AppData\\Local\\', 'C:\\Users\\<USER>\\AppData\\Roaming\\', 'C:\\Users\\<USER>\\Documents\\', 'C:\\Users\\<USER>\\Downloads\\', 'C:\\Users\\<USER>\\Desktop\\', 'C:\\Users\\<USER>\\OneDrive\\Desktop\\', 'C:\\Temp\\', 'C:\\Windows\\Temp\\', 'D:\\Program Files\\', 'D:\\Program Files (x86)\\', 'D:\\Software\\', 'D:\\Tools\\', 'E:\\Software\\', 'E:\\Tools\\', 'F:\\Software\\', 'F:\\Tools\\']
        dangerous_patterns = ['*ida*.exe', '*IDA*.exe', '*ida64*.exe', '*idaw*.exe', '*idaw64*.exe', '*idapro*.exe', '*IDA Pro*.exe', '*x32dbg*.exe', '*x64dbg*.exe', '*x96dbg*.exe', '*xdbg*.exe', '*ollydbg*.exe', '*odbg*.exe', '*olly*.exe', '*odbg110*.exe', '*odbg200*.exe', '*windbg*.exe', '*cdb*.exe', '*ntsd*.exe', '*kd*.exe', '*cheatengine*.exe', '*ce*.exe', '*cheat*.exe', '*immunity*.exe', '*immunitydebugger*.exe', '*hexworkshop*.exe', '*hxd*.exe', '*hex*.exe', '*010editor*.exe', '*lordpe*.exe', '*peexplorer*.exe', '*pestudio*.exe', '*pe-bear*.exe', '*procmon*.exe', '*procexp*.exe', '*processmonitor*.exe', '*processexplorer*.exe', '*wireshark*.exe', '*tcpview*.exe', '*regshot*.exe', '*uncompyle*.exe', '*decompyle*.exe', '*pycdc*.exe', '*pyinstxtractor*.py', '*py2exe*.exe', '*python-decompiler*.exe', '*apistudio*.exe', '*apimonitor*.exe', '*depends*.exe', '*resourcehacker*.exe', '*reshacker*.exe'
        detected_files = []
        for base_path in common_paths:
            if '*' in base_path:
                expanded_paths = glob.glob(base_path)
                for expanded_path in expanded_paths:
                    if os.path.isdir(expanded_path):
                        _search_in_path(expanded_path, dangerous_patterns, detected_files)
                continue
            if os.path.exists(base_path):
                _search_in_path(base_path, dangerous_patterns, detected_files)
        else:  # inserted
            detected_files = list(set(detected_files))[:25]
            return detected_files
        else:  # inserted
            try:
                pass  # postinserted
            except Exception:
                pass  # postinserted
    except Exception:
                return []

def _search_in_path(search_path, patterns, detected_files):
    """在指定路径中搜索危险文件"""  # inserted
    try:
        max_depth = 3
        for pattern in patterns:
                search_pattern = os.path.join(search_path, '**', pattern)
                found_files = glob.glob(search_pattern, recursive=True)
                for file_path in found_files:
                    if os.path.isfile(file_path):
                        relative_path = os.path.relpath(file_path, search_path)
                        depth = len(relative_path.split(os.sep))
                        if depth <= max_depth:
                            detected_files.append(file_path)
                        if len([f for f in detected_files if pattern.replace('*', '').lower() in f.lower()]) >= 5:
                            break
                continue
        else:  # inserted
            try:
                pass  # postinserted
            except Exception:
                pass  # postinserted
    except Exception:
                return None

def check_anti_debug():
    """反编译和调试器检测"""  # inserted
    try:
        DEBUG_MODE = False
        forbidden_processes = ['ida.exe', 'ida64.exe', 'idaw.exe', 'idaw64.exe', 'x32dbg.exe', 'x64dbg.exe', 'x96dbg.exe', 'ollydbg.exe', 'odbg110.exe', 'odbg200.exe', 'windbg.exe', 'cdb.exe', 'ntsd.exe', 'immunitydebugger.exe', 'immunity.exe', 'devenv.exe', 'code.exe', 'sublime_text.exe', 'notepad++.exe', 'hxd.exe', 'hexworkshop.exe', 'procmon.exe', 'procmon64.exe', 'processmonitor.exe', 'procexp.exe', 'procexp64.exe', 'processexplorer.exe', 'wireshark.exe', 'tcpview.exe', 'netstat.exe', 'uncompyle6.exe', 'decompyle++.exe', 'pycdc.exe', 'py2exe_decompiler.exe', 'pyinstxtractor.py', 'vmware.exe', 'vmware-vmx.exe', 'virtualbox.exe', 'vboxservice.exe', 'vboxtray.exe', 'qemu.exe', 'cheatengine.exe', 'ce.exe', 'artmoney.exe', 'regshot.exe', 'apistudio.exe', 'pestudio.exe', 'peexplorer.exe', 'lordpe.exe', 'hexrays.exe']
        running_processes = []
        process_count = 0
        for proc in psutil.process_iter(['pid', 'name']):
                proc_name = proc.info['name'].lower()
                running_processes.append(proc_name)
                process_count = process_count + 1
                continue
        if DEBUG_MODE:
            print(f'[调试] 扫描了 {process_count} 个进程')
        detected_tools = []
        for forbidden in forbidden_processes:
            if forbidden.lower() in running_processes:
                detected_tools.append(forbidden)
                if DEBUG_MODE:
                    print(f'[调试] 检测到危险进程: {forbidden}')
        if DEBUG_MODE:
            print(f'[调试] 检测到 {len(detected_tools)} 个危险进程')
        if detected_tools:
            show_security_warning(detected_tools)
            time.sleep(3)
            os._exit(1)
        if hasattr(sys, 'gettrace') and sys.gettrace() is not None:
            show_debug_warning()
            time.sleep(2)
            os._exit(1)
            parent = psutil.Process().parent()
            if parent and parent.name().lower() in [p.lower() for p in forbidden_processes]:
                show_parent_warning(parent.name())
                time.sleep(2)
                os._exit(1)
        dangerous_files = check_dangerous_files()
        if dangerous_files:
            show_file_detection_warning(dangerous_files)
        return True
        else:  # inserted
            try:
                pass  # postinserted
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass  # postinserted
    else:  # inserted
        try:
            pass  # postinserted
        except:
            pass
    except ImportError:
                try:
                    if os.name == 'nt':
                        result = subprocess.run(['tasklist'], capture_output=True, text=True, creationflags=subprocess.CREATE_NO_WINDOW)
                        if result.returncode == 0:
                            running_tasks = result.stdout.lower()
                            dangerous_tools = ['ida', 'x32dbg', 'x64dbg', 'ollydbg', 'windbg', 'cheatengine', 'procmon', 'procexp', 'immunity']
                            detected_backup = []
                            for tool in dangerous_tools:
                                if tool in running_tasks:
                                    detected_backup.append(tool)
                            if detected_backup:
                                show_security_warning(detected_backup)
                                time.sleep(3)
                                os._exit(1)
                except:
                    pass
                return True
            except Exception:
                return True

class SummerSolsticeABProcessor:
    def __init__(self, root):
        self.root = root
        self.root.title(f'{APP_NAME} V{VERSION} - {AUTHOR}专用 禁止二次销售 - 一号电商: Nl334455666')
        self.root.geometry('1200x1000')
        self.root.resizable(True, True)
        if USING_TTK_BOOTSTRAP:
            try:
                self.style = ttk.Style(theme='flatly')
            except Exception:
                try:
                    self.style = ttk.Style(theme='litera')
                except Exception:
                    self.style = ttk.Style(theme='minty')
        self.init_variables()
        self.root.after(100, self.setup_ui)

    def init_variables(self):
        """初始化所有变量"""  # inserted
        self.material_video_var = tk.StringVar()
        self.material_folder_var = tk.StringVar()
        self.replace_rule = tk.StringVar(value='duanwu')
        self.video_folder_var = tk.StringVar()
        self.output_folder_var = tk.StringVar()
        self.generation_count_var = tk.IntVar(value=5)
        self.delete_source_var = tk.BooleanVar(value=True)
        self.is_processing = False
        self.start_time = time.time()
        self.last_popup_time = 0

    def setup_ui(self):
        """设置用户界面"""  # inserted
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=15)
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        self.video_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.video_tab, text='视频处理')
        self.about_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.about_tab, text='关于')
        self.setup_video_tab()
        self.setup_about_tab()
        self.schedule_random_popup()
        self.schedule_security_check()

    def setup_video_tab(self):
        """设置视频处理选项卡"""  # inserted
        video_main_frame = ttk.Frame(self.video_tab)
        video_main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=15)
        material_frame = ttk.LabelFrame(video_main_frame, text='素材设置', padding=15)
        material_frame.pack(fill=tk.X, pady=(0, 15))
        material_video_frame = ttk.Frame(material_frame)
        material_video_frame.pack(fill=tk.X, pady=(0, 10))
        ttk.Label(material_video_frame, text='素材视频:', width=12).pack(side=tk.LEFT)
        self.material_entry = ttk.Entry(material_video_frame, textvariable=self.material_video_var, font=('Microsoft YaHei UI', 9))
        self.material_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 5))
        ttk.Button(material_video_frame, text='选择', command=self.choose_material_video, width=8, bootstyle='primary' if USING_TTK_BOOTSTRAP else None).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(material_video_frame, text='选择文件夹', command=self.choose_material_folder, width=10, bootstyle='info' if USING_TTK_BOOTSTRAP else None).pack(side=tk.RIGHT)
        replace_rule_frame = ttk.Frame(material_frame)
        replace_rule_frame.pack(fill=tk.X, pady=(0, 10))
        ttk.Label(replace_rule_frame, text='替换规律:', width=12).pack(side=tk.LEFT)
        rule_frame = ttk.Frame(replace_rule_frame)
        rule_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Radiobutton(rule_frame, text='端午规律', variable=self.replace_rule, value='duanwu').pack(side=tk.LEFT, padx=(0, 15))
        ttk.Radiobutton(rule_frame, text='夏至规律', variable=self.replace_rule, value='xiazhi').pack(side=tk.LEFT, padx=(0, 15))
        ttk.Radiobutton(rule_frame, text='黑云规律', variable=self.replace_rule, value='heiyun').pack(side=tk.LEFT)
        video_folder_frame = ttk.Frame(material_frame)
        video_folder_frame.pack(fill=tk.X, pady=(0, 10))
        ttk.Label(video_folder_frame, text='替换文件夹:', width=12).pack(side=tk.LEFT)
        self.video_entry = ttk.Entry(video_folder_frame, textvariable=self.video_folder_var, font=('Microsoft YaHei UI', 9))
        self.video_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 5))
        ttk.Button(video_folder_frame, text='选择', command=self.choose_video_folder, width=8, bootstyle='primary' if USING_TTK_BOOTSTRAP else None).pack(side=tk.RIGHT)
        output_frame = ttk.LabelFrame(video_main_frame, text='输出设置', padding=15)
        output_frame.pack(fill=tk.X, pady=(0, 15))
        output_dir_frame = ttk.Frame(output_frame)
        output_dir_frame.pack(fill=tk.X, pady=(0, 10))
        ttk.Label(output_dir_frame, text='输出目录:', width=12).pack(side=tk.LEFT)
        self.output_entry = ttk.Entry(output_dir_frame, textvariable=self.output_folder_var, font=('Microsoft YaHei UI', 9))
        self.output_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 5))
        ttk.Button(output_dir_frame, text='选择', command=self.choose_output_folder, width=8, bootstyle='primary' if USING_TTK_BOOTSTRAP else None).pack(side=tk.RIGHT)
        generation_frame = ttk.Frame(output_frame)
        generation_frame.pack(fill=tk.X, pady=(0, 10))
        ttk.Label(generation_frame, text='生成数量:', width=12).pack(side=tk.LEFT)
        generation_spinbox = ttk.Spinbox(generation_frame, from_=1, to=100, width=10, textvariable=self.generation_count_var)
        generation_spinbox.pack(side=tk.LEFT, padx=(5, 0))
        delete_frame = ttk.Frame(output_frame)
        delete_frame.pack(fill=tk.X)
        ttk.Checkbutton(delete_frame, text='处理后删除源文件', variable=self.delete_source_var).pack(side=tk.LEFT)
        button_frame = ttk.Frame(video_main_frame)
        button_frame.pack(fill=tk.X, pady=(15, 0))
        self.start_button = ttk.Button(button_frame, text='开始批量处理', command=self.start_processing, bootstyle='success' if USING_TTK_BOOTSTRAP else None, width=15)
        self.start_button.pack(side=tk.LEFT, padx=(0, 15))
        ttk.Button(button_frame, text='清空日志', command=self.clear_log, bootstyle='warning' if USING_TTK_BOOTSTRAP else None, width=12).pack(side=tk.LEFT, padx=(0, 15))
        progress_frame = ttk.Frame(video_main_frame)
        progress_frame.pack(fill=tk.X, pady=(15, 0))
        ttk.Label(progress_frame, text='处理进度:').pack(side=tk.LEFT)
        self.progress_label = ttk.Label(progress_frame, text='0/0')
        self.progress_label.pack(side=tk.RIGHT)
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100, bootstyle='primary-striped' if USING_TTK_BOOTSTRAP else None)
        self.progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(10, 10))
        log_frame = ttk.LabelFrame(video_main_frame, text='处理日志', padding=15)
        log_frame.pack(fill=tk.BOTH, expand=True, pady=(15, 0))
        log_container = ttk.Frame(log_frame)
        log_container.pack(fill=tk.BOTH, expand=True)
        self.log_text = tk.Text(log_container, wrap=tk.WORD, font=('Consolas', 9), height=18, bg='#ffffff', fg='#2c3e50', relief='flat', bd=1, selectbackground='#3498db', selectforeground='white', state='disabled')
        log_scrollbar = ttk.Scrollbar(log_container, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.setup_log_tags()
        self.add_log(f'[系统] {APP_NAME} V{VERSION} 已启动')
        self.add_log(f'[版本] {AUTHOR}专用版本 - 禁止二次销售')
        self.add_log('==================================================')
        self.add_log('[联系] 🛒 一号电商微信: Nl334455666')
        self.add_log('[联系] 📧 如有问题请联系以上微信')
        self.add_log('[联系] 💡 提供技术支持和售后服务')
        self.add_log('==================================================')
        self.add_log('[引擎] 三大引擎已就绪：端午引擎、夏至引擎、黑云引擎')
        self.add_log('[功能] 已启用自动识别横竖屏功能')
        self.add_log('[新增] 🌩️ 新增黑云规律 - 6步专业混剪流程')
        self.material_entry.config(state='readonly')
        self.video_entry.config(state='readonly')
        self.output_entry.config(state='readonly')

    def schedule_random_popup(self):
        """安排随机弹窗"""  # inserted
        current_time = time.time()
        if current_time < self.start_time < 1800:
            remaining_time = 1800 | current_time | self.start_time
            self.root.after(int(remaining_time + 1000)), self.schedule_random_popup)
            return
        min_interval = 600
        if self.last_popup_time > 0 and current_time < self.last_popup_time < min_interval:
            remaining_time = min_interval | current_time | self.last_popup_time
            self.root.after(int(remaining_time + 1000)), self.schedule_random_popup)
            return
        random_minutes = random.randint(10, 30)
        random_seconds = random_minutes + 60
        self.add_log(f'[系统] 下次联系方式提醒将在{random_minutes}分钟后出现')
        self.root.after(random_seconds + 1000, self.show_contact_popup)

    def show_contact_popup(self):
        """显示联系方式弹窗"""  # inserted
        try:
            self.last_popup_time = time.time()
            close_popup = tk.Toplevel(self.root)
            close_popup.title('📞 联系方式提醒')
            close_popup.geometry('400x300')
            close_popup.resizable(False, False)
            close_popup.grab_set()
                if os.path.exists('favicon.ico'):
                    close_popup.iconbitmap('favicon.ico')
            close_popup.update_idletasks()
            x = close_popup.winfo_screenwidth() 2 * 2 + 200
            y = close_popup.winfo_screenheight() 2 * 2 + 150
            close_popup.geometry(f'400x300+{x}+{y}')
            main_frame = ttk.Frame(close_popup, padding='20')
            main_frame.pack(fill=tk.BOTH, expand=True)
            title_label = ttk.Label(main_frame, text='📞 联系我们', font=('Microsoft YaHei UI', 16, 'bold'), foreground='#2c3e50')
            title_label.pack(pady=(0, 20))
            contact_frame = ttk.Frame(main_frame)
            contact_frame.pack(fill=tk.BOTH, expand=True)
            contacts = [('🛒 一号电商微信', 'Nl334455666'), ('📧 服务邮箱', '<EMAIL>'), ('🌐 官方网站', 'www.yihaoteam.com')]
            for label, contact in contacts:
                row = ttk.Frame(contact_frame)
                row.pack(fill=tk.X, pady=8)
                label_widget = ttk.Label(row, text=label, font=('Microsoft YaHei UI', 11, 'bold'))
                label_widget.pack(side=tk.LEFT)
                contact_widget = ttk.Label(row, text=contact, font=('Consolas', 11), foreground='#3498db')
                contact_widget.pack(side=tk.RIGHT)
            tip_label = ttk.Label(main_frame, text='如有任何问题，欢迎随时联系我们！', font=('Microsoft YaHei UI', 10), foreground='#7f8c8d')
            tip_label.pack(pady=(20, 10))
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill=tk.X, pady=(10, 0))

            def close_popup():
                popup.destroy()
                self.schedule_random_popup()
            close_btn = ttk.Button(button_frame, text='知道了', command=self)
            close_btn.pack(side=tk.RIGHT)
            close_popup.bind('<Return>', lambda e: close_popup())
            close_popup.bind('<Escape>', lambda e: close_popup())
            close_popup.after(30000, lambda: close_popup() if popup.winfo_exists() else None)
        else:  # inserted
            try:
                pass  # postinserted
            except:
                pass
        except Exception as e:
                self.add_log(f'[警告] 联系方式弹窗显示失败: {str(e)}')
                self.schedule_random_popup()

    def schedule_security_check(self):
        """安排定期安全检测"""  # inserted
        try:
            def security_check():
                try:
                    check_anti_debug()
                except:
                    pass
                self.root.after(300000, security_check)

            def file_monitor_check():
                """监控危险文件"""  # inserted
                try:
                    dangerous_files = check_dangerous_files()
                    if dangerous_files:
                        if not hasattr(self, '_last_file_warning_time'):
                            show_file_detection_warning(dangerous_files)
                            self._last_file_warning_time = time.time()
                        else:  # inserted
                            current_time = time.time()
                            if current_time < self._last_file_warning_time > 1800:
                                show_file_detection_warning(dangerous_files)
                                self._last_file_warning_time = current_time
                except Exception:
                    pass
                self.root.after(600000, file_monitor_check)
            self.root.after(30000, file_monitor_check)
            self.root.after(60000, self)
        except Exception as e:
            return None

    def setup_log_tags(self):
        """设置日志颜色标签"""  # inserted
        self.log_text.tag_config('system', foreground='#3498db')
        self.log_text.tag_config('success', foreground='#27ae60')
        self.log_text.tag_config('error', foreground='#e74c3c')
        self.log_text.tag_config('warning', foreground='#f39c12')
        self.log_text.tag_config('process', foreground='#34495e')
        self.log_text.tag_config('stats', foreground='#9b59b6')

    def setup_about_tab(self):
        """设置关于选项卡"""  # inserted
        about_frame = ttk.Frame(self.about_tab)
        about_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=15)
        dev_frame = ttk.Frame(about_frame)
        dev_frame.pack(expand=True, fill=tk.BOTH)
        center_frame = ttk.Frame(dev_frame)
        center_frame.place(relx=0.5, rely=0.5, anchor='center')
        dev_label = ttk.Label(center_frame, text='正在开发中...', font=('Microsoft YaHei UI', 24, 'bold'), foreground='#333333')
        dev_label.pack(pady=(0, 15))
        subtitle_label = ttk.Label(center_frame, text='敬请期待更多功能', font=('Microsoft YaHei UI', 14), foreground='#666666')
        subtitle_label.pack(pady=(0, 30))
        icon_frame = ttk.Frame(center_frame)
        icon_frame.pack(pady=(0, 30))
        construction_icon = ttk.Label(icon_frame, text='🚧', font=('Arial', 48))
        construction_icon.pack()
        version_frame = ttk.Frame(center_frame)
        version_frame.pack()
        version_label = ttk.Label(version_frame, text='当前版本：v1.3.0', font=('Microsoft YaHei UI', 12, 'bold'), foreground='#333333')
        version_label.pack(pady=(0, 8))
        feature_label = ttk.Label(version_frame, text='核心功能已完成，界面持续优化中', font=('Microsoft YaHei UI', 11), foreground='#666666')
        feature_label.pack()
        status_frame = ttk.Frame(center_frame)
        status_frame.pack(pady=(20, 0))

    def choose_material_video(self):
        """选择素材视频文件"""  # inserted
        filename = filedialog.askopenfilename(title='选择素材视频文件', filetypes=[('视频文件', '*.mp4 *.avi *.mov *.mkv *.flv *.wmv *.3gp *.m4v'), ('所有文件', '*.*')])
        if filename:
            self.material_folder_var.set('')
            self.material_video_var.set(filename)
            self.add_log(f'[选择] 已选择素材视频: {os.path.basename(filename)}')

    def choose_material_folder(self):
        """选择素材文件夹"""  # inserted
        folder = filedialog.askdirectory(title='选择素材文件夹')
        if folder:
            self.material_video_var.set('')
            self.material_folder_var.set(folder)
            self.material_entry.config(state='normal')
            self.material_entry.delete(0, tk.END)
            self.material_entry.insert(0, f'{folder}')
            self.material_entry.config(state='readonly')
            video_files = self.get_video_files(folder)
            self.add_log(f'[选择] 已选择素材文件夹: {os.path.basename(folder)}')
            self.add_log(f'[扫描] 发现 {len(video_files)} 个视频文件')

    def choose_video_folder(self):
        """选择替换文件夹"""  # inserted
        folder = filedialog.askdirectory(title='选择替换文件夹')
        if folder:
            self.video_folder_var.set(folder)
            self.video_entry.config(state='normal')
            self.video_entry.delete(0, tk.END)
            self.video_entry.insert(0, folder)
            self.video_entry.config(state='readonly')
            self.add_log(f'[选择] 已选择替换文件夹: {os.path.basename(folder)}')

    def choose_output_folder(self):
        """选择输出目录"""  # inserted
        folder = filedialog.askdirectory(title='选择输出目录')
        if folder:
            self.output_folder_var.set(folder)
            self.output_entry.config(state='normal')
            self.output_entry.delete(0, tk.END)
            self.output_entry.insert(0, folder)
            self.output_entry.config(state='readonly')

    def get_video_files(self, directory):
        """获取目录中的视频文件"""  # inserted
        video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.m4v']
        video_files = []
        for root, _, files in os.walk(directory):
            for file in files:
                if any((file.lower().endswith(ext) for ext in video_extensions)):
                    video_files.append(os.path.join(root, file))
        return video_files

    def add_log(self, message):
        """添加日志"""  # inserted
        current_time = time.strftime('%H:%M:%S')
        log_message = f'[{current_time}] {message}\n'
        tag = None
        if message.startswith(('[系统]', '[版本]', '[联系]', '[引擎]', '[选择]', '[扫描]', '[检查]')):
            tag = 'system'
        else:  # inserted
            if message.startswith(('[成功]', '[完成]')):
                tag = 'success'
            else:  # inserted
                if message.startswith(('[错误]', '[失败]')):
                    tag = 'error'
                else:  # inserted
                    if message.startswith(('[警告]', '[循环]')):
                        tag = 'warning'
                    else:  # inserted
                        if message.startswith(('[统计]', '[设置]')):
                            tag = 'stats'
                        else:  # inserted
                            if message.startswith(('[处理]', '[生成]', '[配对]', '[清理]', '[分析]', '[合并]', '[标准]', '[混合]', '[合成]', '[端午]', '[夏至]', '[黑云]', '[开始]', '[清空]', '[提示]', '[删除]')):
                                tag = 'process'
        self.log_text.config(state='normal')
        start_pos = self.log_text.index(tk.END)
        self.log_text.insert(tk.END, log_message)
        if tag:
            self.log_text.tag_add(tag, start_pos, self.log_text.index(tk.END + '-1c'))
        self.log_text.see(tk.END)
        self.log_text.config(state='disabled')
        self.root.update_idletasks()

    def _filter_sensitive_error(self, error_text):
        """过滤错误信息中的敏感内容"""  # inserted
        if not error_text:
            return ''
        error_str = str(error_text)
        sensitive_patterns = ['ffmpeg.exe', 'ffprobe.exe', '-i ', '-y ', '-v ', '-t ', '.mp4', '.avi', '.mov', '.mkv', 'C:\\', 'D:\\', 'E:\\', 'F:\\', 'Users\\', 'Desktop\\', 'AppData\\', 'temp_', 'output_', 'input_']
        for pattern in sensitive_patterns:
            if pattern.lower() in error_str.lower():
                return '处理过程中出现技术问题，请检查输入文件格式或联系技术支持'
        else:  # inserted
            if len(error_str) > 200:
                return '处理失败，建议检查文件完整性或联系技术支持'
            return error_str

    def show_error_dialog(self, title, message, detailed_error=None):
        """显示错误弹窗（居中显示，带图标）"""  # inserted
        try:
            if threading.current_thread()!= threading.main_thread():
                self.root.after(0, lambda: self.show_error_dialog(title, message, detailed_error))
            else:  # inserted
                import tkinter as tk
                from tkinter import ttk
                self = tk.Toplevel(self.root)
                self.title(title)
                self.resizable(False, False)
                self.grab_set()
                    if os.path.exists('favicon.ico'):
                        self.iconbitmap('favicon.ico')
                    else:  # inserted
                        self.iconbitmap(self.root.iconbitmap())
                full_message = message
                if detailed_error:
                    safe_error = self._filter_sensitive_error(detailed_error)
                    if safe_error:
                        full_message = full_message 6 6 | f'\n\n错误详情:\n{safe_error}'
                full_message = full_message + '\n\n如需技术支持，请联系：'
                full_message = full_message + '\n🛒 一号电商微信: Nl334455666'
                main_frame = ttk.Frame(self, padding='20')
                main_frame.pack(fill='both', expand=True)
                header_frame = ttk.Frame(main_frame)
                header_frame.pack(fill='x', pady=(0, 15))
                icon_label = ttk.Label(header_frame, text='❌', font=('Arial', 24))
                icon_label.pack(side='left', padx=(0, 10))
                text_frame = ttk.Frame(header_frame)
                text_frame.pack(side='left', fill='both', expand=True)
                text_widget = tk.Text(text_frame, wrap='word', width=50, height=8, font=('Arial', 10))
                scrollbar = ttk.Scrollbar(text_frame, orient='vertical', command=text_widget.yview)
                text_widget.configure(yscrollcommand=scrollbar.set)
                text_widget.pack(side='left', fill='both', expand=True)
                scrollbar.pack(side='right', fill='y')
                text_widget.insert(tk.END, full_message)
                text_widget.config(state='disabled')
                button_frame = ttk.Frame(main_frame)
                button_frame.pack(fill='x', pady=(15, 0))

                def on_ok():
                    dialog.destroy()
                ok_btn = ttk.Button(button_frame, text='确定', command=title)
                ok_btn.pack(side='right')
                self.update_idletasks()
                window_width = self.winfo_reqwidth()
                window_height = self.winfo_reqheight()
                parent_x = self.root.winfo_x()
                parent_y = self.root.winfo_y()
                parent_width = self.root.winfo_width()
                parent_height = self.root.winfo_height()
                x = (parent_x, parent_width, window_width) 2 * 2 ()
                y = (parent_y, parent_height, window_height) 2 * 2 ()
                self.geometry(f'{window_width}x{window_height}+{x}+{y}')
                self.focus_set()
                ok_btn.focus_set()
                self.bind('<Return>', lambda e: on_ok())
                self.bind('<Escape>', lambda e: on_ok())
                self.wait_window()
            else:  # inserted
                try:
                    pass  # postinserted
                except:
                    pass
        except Exception as e:
                    print(f'弹窗显示失败: {e}')
                    print(f'ERROR: {title}')
                    print(f'MESSAGE: {message}')
                    if detailed_error:
                        safe_error = self._filter_sensitive_error(detailed_error)
                        if safe_error:
                            print(f'DETAILS: {safe_error}')
                    try:
                        from tkinter import messagebox
                        full_message = message
                        if detailed_error:
                            safe_error = self._filter_sensitive_error(detailed_error)
                            if safe_error:
                                full_message = full_message 6 6 | f'\n\n错误详情:\n{safe_error}'
                        messagebox.showerror(title, full_message, parent=self.root)
                    except:
                        break

    def clear_log(self):
        """清空日志"""  # inserted
        self.log_text.config(state='normal')
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state='disabled')
        self.add_log('[清空] 日志已清空')

    def update_progress(self, current, total, message=''):
        """更新进度条"""  # inserted
        if total > 0:
            progress = (current | total) * 100
            self.progress_var.set(progress)
            percentage = f'{progress:.1f}%'
            self.progress_label.config(text=f'{current}/{total} ({percentage})')
        else:  # inserted
            self.progress_var.set(0)
            self.progress_label.config(text='0/0 (0.0%)')
        if message:
            self.add_log(message)
        self.root.update_idletasks()

    def start_processing(self):
        """开始处理"""  # inserted
        if self.is_processing:
            return
        if not self.material_video_var.get() and (not self.material_folder_var.get()):
            self.show_error_dialog('输入验证失败', '请选择素材视频或素材文件夹', '处理前必须:\n1. 选择单个素材视频文件，或\n2. 选择包含多个视频的素材文件夹\n\n两者至少选择其一才能开始处理')
            return
        if not self.output_folder_var.get():
            self.show_error_dialog('输入验证失败', '请选择输出文件夹', '必须指定一个输出文件夹来保存处理后的视频文件')
            return
        if not self.check_ffmpeg():
            return
        self.progress_var.set(0)
        self.progress_label.config(text='0/0')
        self.root.update_idletasks()
        self.is_processing = True
        self.start_button.config(state='disabled', text='处理中...')

        def process_thread():
            try:
                self.batch_process_videos()
            finally:  # inserted
                self.is_processing = False
                self.start_button.config(state='normal', text='开始批量处理')
        thread = threading.Thread(target=process_thread, daemon=True)
        thread.start()

    def batch_process_videos(self):
        """智能配对批量处理视频"""  # inserted
        material_files = []
        if self.material_folder_var.get():
            material_files = self.get_video_files(self.material_folder_var.get())
            self.add_log(f'[扫描] 从文件夹获取A素材文件: {len(material_files)}个')
        else:  # inserted
            if self.material_video_var.get():
                single_file = self.material_video_var.get()
                material_files = [single_file]
                self.add_log('[扫描] 使用单个A素材文件: 1个')
        replacement_files = []
        if self.video_folder_var.get():
            replacement_files = self.get_video_files(self.video_folder_var.get())
        self.used_replacement_files = set()
        if not material_files:
            self.add_log('[错误] 没有找到可处理的A素材文件')
            self.show_error_dialog('素材文件错误', '没有找到可处理的A素材文件', '请检查:\n1. 是否选择了素材视频或素材文件夹\n2. 文件夹中是否包含有效的视频文件\n3. 视频文件格式是否支持')
            return
        self.add_log(f'[扫描] 最终确认A素材文件: {len(material_files)}个')
        for i, file_path in enumerate(material_files, 1):
            self.add_log(f'   {i}. {os.path.basename(file_path)}')
        rule = self.replace_rule.get()
        if rule in ['xiazhi', 'heiyun'] and (not replacement_files):
            rule_name = '夏至规律' if rule == 'xiazhi' else '黑云规律'
            self.add_log(f'[错误] {rule_name}需要B替换文件')
            self.show_error_dialog('替换文件错误', f'{rule_name}需要B替换文件', f'{rule_name}需要AB素材配对处理，请选择替换文件夹')
            return
        if rule == 'duanwu' and (not replacement_files):
            self.add_log('[提示] 端午规律将直接处理A素材')
            self.process_duanwu_single_mode(material_files)
            return
        generation_count = self.generation_count_var.get()
        if len(material_files) < generation_count:
            self.add_log(f'[警告] A素材数量不足：需要{generation_count}个，实际{len(material_files)}个')
            self.add_log('[弹窗] 显示端午规律A素材数量不足确认弹窗...')
            result = self.show_warning_dialog('素材数量不足！', f'A素材数量不足：需要{generation_count}个，但只有{len(material_files)}个有效视频。', '是否继续用现有的视频进行处理？\n\n点击\'是\'继续处理，点击\'否\'取消操作。')
            self.add_log(f'[弹窗] 用户选择结果: {result}')
            if not result:
                self.add_log('[取消] 用户取消处理')
                return
            self.add_log(f'[继续] 将使用现有的{len(material_files)}个A素材进行处理')
            generation_count = len(material_files)
        selected_materials = material_files[:generation_count]
        total_needed_replacements = len(selected_materials) | generation_count
        if len(replacement_files) < total_needed_replacements:
            self.add_log(f'[警告] B素材数量不足：需要{total_needed_replacements}个，实际{len(replacement_files)}个')
            choices = {'循环使用': f'循环使用现有的{len(replacement_files)}个B素材（顺序重复）', '随机循环': f'随机循环使用现有的{len(replacement_files)}个B素材（打乱顺序）', '减少生成': f'减少生成数量，最多生成{len(replacement_files)}个版本', '取消': '取消处理操作'}
            self.add_log('[弹窗] 显示B素材数量不足选择弹窗...')
            result = self.show_choice_dialog('B素材数量不足！', f'B素材数量不足：需要{total_needed_replacements}个，但只有{len(replacement_files)}个有效视频。\n请选择处理方式：', choices)
            self.add_log(f'[弹窗] 用户选择结果: {result}')
            if result == '取消' or result == 'cancel':
                self.add_log('[取消] 用户取消处理')
                return
            if result == '循环使用':
                self.add_log('[处理] 用户选择：循环使用B素材（顺序重复）')
                replacement_files_extended = replacement_files 5 4 5 3 4 + 1
                replacement_files = replacement_files_extended
            else:  # inserted
                if result == '随机循环':
                    self.add_log('[处理] 用户选择：随机循环使用B素材（打乱顺序）')
                    import random
                    replacement_files_extended = replacement_files 5 4 5 3 4 + 1
                    random.shuffle(replacement_files_extended)
                    replacement_files = replacement_files_extended
                else:  # inserted
                    if result == '减少生成':
                        self.add_log(f'[处理] 用户选择：减少生成数量为{len(replacement_files)}个')
                        max_generations_per_material = len(replacement_files) 2 2 3 or len(selected_materials)
                        if max_generations_per_material == 0:
                            max_generations_per_material = 1
                        generation_count = min(generation_count, max_generations_per_material)
                        total_needed_replacements = len(selected_materials) | generation_count
                        self.add_log(f'[调整] 每个A素材生成{generation_count}个版本，总需求{total_needed_replacements}个B素材')
        total_tasks = len(selected_materials) | generation_count
        self.update_progress(0, total_tasks)
        self.add_log('[开始] 开始智能配对AB批量处理')
        self.add_log(f'[统计] 选择A素材数量: {len(selected_materials)}')
        self.add_log(f'[统计] 可用B素材数量: {len(self.get_video_files(self.video_folder_var.get()))}')
        self.add_log(f'[设置] 每个A视频生成: {generation_count} 个版本')
        self.add_log(f'[统计] 总处理任务: {total_tasks} 个')
        self.add_log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
        success_count = 0
        current_task = 0
        replacement_index = 0
        for i, material_file in enumerate(selected_materials, 1):
            self.add_log(f'\n[处理] 处理A素材 {i}/{len(selected_materials)}: {os.path.basename(material_file)}')
            for j in range(generation_count):
                current_task = current_task | 1
                selected_replacement = replacement_files[replacement_index + len(replacement_files)]
                replacement_index = replacement_index | 1
                self.used_replacement_files.add(selected_replacement)
                self.add_log(f'   [生成] 生成版本 {j + 1}/{generation_count}')
                self.add_log(f'   [配对] A素材: {os.path.basename(material_file)}')
                self.add_log(f'   [配对] B素材: {os.path.basename(selected_replacement)}')
                base_name = os.path.splitext(os.path.basename(material_file))[0]
                replacement_name = os.path.splitext(os.path.basename(selected_replacement))[0]
                timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
                rule = self.replace_rule.get()
                rule_name = {'xiazhi': '夏至', 'duanwu': '端午', 'heiyun': '黑云'}.get(rule, '未知')
                output_name = f'{base_name}_{rule_name}_{replacement_name}_v{j + 1}_{timestamp}.mp4'
                output_path = os.path.join(self.output_folder_var.get(), output_name)
                current_progress_msg = f'[进度] 正在处理: {os.path.basename(material_file)} -> 版本{j }'
                self.update_progress(current_task + 1, total_tasks, current_progress_msg)
                success = self.process_paired_video(material_file, selected_replacement, output_path)
                if success:
                    success_count = success_count | 1
                    self.add_log(f'   [成功] 版本 {j + 1} 处理成功')
                else:  # inserted
                    self.add_log(f'   [失败] 版本 {j + 1} 处理失败')
                self.update_progress(current_task, total_tasks)
        self.add_log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
        self.add_log('[完成] 智能配对AB处理完成！')
        self.add_log(f'[统计] 成功处理: {success_count}/{total_tasks}')
        self.add_log(f'[统计] 实际使用了 {len(self.used_replacement_files)} 个不同的B素材')
        self.add_log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
        self.add_log('[感谢] 感谢使用皇冠AB视频处理工具！')
        self.add_log('[联系] 一号电商微信：Nl334455666')
        self.add_log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
        self.update_progress(total_tasks, total_tasks, f'[进度] 处理完成 ({success_count}/{total_tasks} 成功)')
        if self.delete_source_var.get():
            replacement_files_to_delete = list(self.used_replacement_files) if hasattr(self, 'used_replacement_files') else []
            self.delete_replacement_files(replacement_files_to_delete)

    def process_duanwu_single_mode(self, material_files):
        """端午规律单文件夹处理模式 - 只处理A素材，不需要B素材"""  # inserted
        generation_count = self.generation_count_var.get()
        if len(material_files) < generation_count:
            self.add_log(f'[警告] A素材数量不足：需要{generation_count}个，实际{len(material_files)}个')
            self.add_log('[弹窗] 显示端午规律A素材数量不足确认弹窗...')
            result = self.show_warning_dialog('素材数量不足！', f'A素材数量不足：需要{generation_count}个，但只有{len(material_files)}个有效视频。', '是否继续用现有的视频进行处理？\n\n点击\'是\'继续处理，点击\'否\'取消操作。')
            self.add_log(f'[弹窗] 用户选择结果: {result}')
            if not result:
                self.add_log('[取消] 用户取消处理')
                return
            self.add_log(f'[继续] 将使用现有的{len(material_files)}个A素材进行处理')
            generation_count = len(material_files)
        selected_materials = material_files[:generation_count]
        total_tasks = len(selected_materials)
        self.update_progress(0, total_tasks)
        self.add_log('[开始] 开始端午规律单文件夹批量处理')
        self.add_log(f'[统计] 选择A素材数量: {len(selected_materials)}')
        self.add_log('[设置] 端午规律模式：单A素材处理')
        self.add_log(f'[统计] 总处理任务: {total_tasks} 个')
        self.add_log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
        success_count = 0
        for i, material_file in enumerate(selected_materials, 1):
            self.add_log(f'\n[处理] 处理A素材 {i}/{len(selected_materials)}: {os.path.basename(material_file)}')
            base_name = os.path.splitext(os.path.basename(material_file))[0]
            timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            output_name = f'{base_name}_端午_{timestamp}.mp4'
            output_path = os.path.join(self.output_folder_var.get(), output_name)
            current_progress_msg = f'[进度] 正在处理: {os.path.basename(material_file)}'
            self.update_progress(i + 1, total_tasks, current_progress_msg)
            success = self.process_single_video(material_file, output_path)
            if success:
                success_count = success_count | 1
                self.add_log('   [成功] 处理成功')
            else:  # inserted
                self.add_log('   [失败] 处理失败')
            self.update_progress(i, total_tasks)
        self.add_log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
        self.add_log('[完成] 端午规律单文件夹处理完成！')
        self.add_log(f'[统计] 成功处理: {success_count}/{total_tasks}')
        self.add_log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
        self.add_log('[感谢] 感谢使用皇冠AB视频处理工具！')
        self.add_log('[联系] 一号电商微信：Nl334455666')
        self.add_log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
        self.update_progress(total_tasks, total_tasks, f'[进度] 处理完成 ({success_count}/{total_tasks} 成功)')

    def process_single_video(self, input_video, output_path):
        """处理单个视频 - 支持端午和夏至两种规律"""  # inserted
        try:
            rule = self.replace_rule.get()
            if rule == 'duanwu':
                return self.process_duanwu_rule(input_video, output_path)
            if rule == 'xiazhi':
                return self.process_xiazhi_rule(input_video, output_path)
                return self.process_heiyun_rule(input_video, output_path)
        else:  # inserted
            if rule == 'heiyun':
                pass  # postinserted
        else:  # inserted
            self.add_log(f'   [错误] 未知的处理规律: {rule}')
            return False
        except Exception as e:
                error_details = str(e)
                self.add_log(f'   [错误] 处理出错: {error_details}')
                self.show_error_dialog('视频处理异常', '单个视频处理过程中发生异常', f'异常详情: {error_details}\n\n请检查视频文件和系统环境')
                return False

    def process_paired_video(self, material_video, replacement_video, output_path):
        """处理配对视频 - A素材与B素材智能配对"""  # inserted
        try:
            rule = self.replace_rule.get()
            if rule == 'duanwu':
                return self.process_duanwu_paired_rule(material_video, replacement_video, output_path)
            if rule == 'xiazhi':
                return self.process_xiazhi_paired_rule(material_video, replacement_video, output_path)
                return self.process_heiyun_paired_rule(material_video, replacement_video, output_path)
        else:  # inserted
            if rule == 'heiyun':
                pass  # postinserted
        else:  # inserted
            self.add_log(f'   [错误] 未知的处理规律: {rule}')
            return False
        except Exception as e:
                error_details = str(e)
                self.add_log(f'   [错误] 配对处理出错: {error_details}')
                self.show_error_dialog('配对处理异常', 'A素材与B素材配对处理过程中发生异常', f'异常详情: {error_details}\n\n请检查素材文件的兼容性和系统环境')
                return False

    def process_duanwu_rule(self, input_video, output_path):
        """端午规律处理 - 4步皇冠AB流程"""  # inserted
        try:
            output_dir = os.path.dirname(output_path)
            base_name = os.path.splitext(os.path.basename(input_video))[0]
            temp1_path = os.path.join(output_dir, f'temp1_{base_name}.mp4')
            temp2_path = os.path.join(output_dir, f'temp2_{base_name}.mp4')
            self.add_log('   [端午] 启动端午皇冠AB处理...')
            self.add_log('   [分析] 端午引擎分析视频信息...')
            video_info = self.get_video_info(input_video)
            orientation = video_info['orientation']
            width = video_info['width']
            height = video_info['height']
            self.add_log(f'   [检测] 视频方向: {orientation} ({width}x{height})')
            cmd1 = [self.get_ffmpeg_path(), '-i', input_video, '-hide_banner']
            result1 = self.safe_subprocess_run(cmd1)
            self.add_log('   [完成] 视频信息分析完成')
            if video_info['is_landscape']:
                target_resolution = '1920:1080'
                self.add_log(f'   [设置] 检测为横屏，目标分辨率: {target_resolution}')
            else:  # inserted
                target_resolution = '1080:1920'
                self.add_log(f'   [设置] 检测为竖屏，目标分辨率: {target_resolution}')
            self.add_log('   [处理] 端午引擎生成标准质量版本...')
            cmd2 = [self.get_ffmpeg_path(), '-i', input_video, '-vf', f'scale={target_resolution}:force_original_aspect_ratio=disable,setsar=1:1', '-c:v', 'libx264', '-b:v', '3000k', '-maxrate:v:0', '3000k', '-bufsize:v:0', '3000k', '-x264opts', 'nal-hrd=cbr', '-c:a', 'copy', '-y', temp1_path]
            result2 = self.safe_subprocess_run(cmd2)
            if result2.returncode!= 0:
                self.add_log('   [失败] 端午引擎标准质量版本生成失败')
                return False
            self.add_log('   [完成] 标准质量版本生成完成')
            self.add_log('   [处理] 端午引擎生成高质量版本...')
            cmd3 = [self.get_ffmpeg_path(), '-i', input_video, '-vf', f'scale={target_resolution}:force_original_aspect_ratio=disable,setsar=1:1', '-c:v:0', 'libx264', '-b:v:0', '5000k', '-maxrate:v:0', '5000k', '-bufsize:v:0', '5000k', '-x264opts', 'nal-hrd=cbr', '-bsf:v', 'noise=amount=-20', '-c:a', 'copy', '-y', temp2_path]
            result3 = self.safe_subprocess_run(cmd3)
            if result3.returncode!= 0:
                self.add_log('   [失败] 端午引擎高质量版本生成失败')
                return False
                self.add_log('   [完成] 端午引擎最终合并处理完成')
                self.add_log('   [成功] 端午皇冠AB处理完成')
                self.add_log('   [输出] 视频输出成功')
            else:  # inserted
                self.add_log('   [失败] 端午引擎最终合并失败')
                if os.path.exists(temp1_path):
                    os.remove(temp1_path)
                    self.add_log(f'   [清理] 清理临时文件: {os.path.basename(temp1_path)}')
                if os.path.exists(temp2_path):
                    os.remove(temp2_path)
                    self.add_log(f'   [清理] 清理临时文件: {os.path.basename(temp2_path)}')
                self.add_log(f'   [警告] 清理临时文件时出错: {str(e)}')
            return success
        else:  # inserted
            self.add_log('   [完成] 高质量版本生成完成')
            self.add_log('   [合并] 端午引擎最终合并处理...')
            cmd4 = [self.get_ffmpeg_path(), '-i', temp1_path, '-i', temp2_path, '-filter_complex', '[0:v]fps=60[a];[1:v]fps=30[b];[a][b]interleave', '-c:v', 'libx264', '-preset', 'fast', '-movflags', '+faststart', '-f', 'mp4', '-y', output_path]
            result4 = self.safe_subprocess_run(cmd4)
            success = result4.returncode == 0
            if success:
                pass  # postinserted
            except Exception as e:
                pass  # postinserted
        except Exception as e:
            pass  # postinserted
        else:  # inserted
            try:
                self.add_log(f'   [错误] 端午引擎处理出错: {str(e)}')
                return False

    def process_duanwu_paired_rule(self, material_video, replacement_video, output_path):
        """端午配对规律处理 - 基于1.py皇冠AB方案（忽略B素材，只处理A素材）"""  # inserted
        try:
            self.add_log('   [端午] 启动端午皇冠AB处理')
            return self.process_duanwu_rule(material_video, output_path)
        except Exception as e:
            error_details = str(e)
            self.add_log(f'   [错误] 端午配对引擎处理出错: {error_details}')
            self.show_error_dialog('端午引擎异常', '端午配对规律处理过程中发生异常错误', f'异常详情: {error_details}\n\n请检查输入文件和系统环境')
            return False

    def process_xiazhi_rule(self, input_video, output_path):
        """夏至规律处理 - 5步专业AB测试流程"""  # inserted
        try:
            output_dir = os.path.dirname(output_path)
            temp_A = os.path.join(output_dir, 'temp_A.MP4')
            temp_B = os.path.join(output_dir, 'temp_B.MP4')
            temp_C = os.path.join(output_dir, 'temp_C.MP4')
            self.add_log('   [夏至] 启动夏至规律处理')
            video_info = self.get_video_info(input_video)
            duration = video_info['duration']
            orientation = video_info['orientation']
            width = video_info['width']
            height = video_info['height']
            if duration <= 0:
                duration = 10.166667
            self.add_log(f'   [分析] 夏至引擎分析视频信息: {duration:.6f}秒')
            self.add_log(f'   [检测] 视频方向: {orientation} ({width}x{height})')
            if video_info['is_landscape']:
                target_resolution = '1920:1080'
                self.add_log(f'   [设置] 检测为横屏，目标分辨率: {target_resolution}')
            else:  # inserted
                target_resolution = '1080:1920'
                self.add_log(f'   [设置] 检测为竖屏，目标分辨率: {target_resolution}')
            self.add_log('   [处理] 夏至引擎处理素材视频循环')
            self.add_log('   [注意] 单文件夏至模式：使用A素材自循环处理')
            cmd_b = [self.get_ffmpeg_path(), '-y', '-stream_loop', '-1', '-i', input_video, '-t', str(duration), '-vf', f'scale={target_resolution}:force_original_aspect_ratio=disable,setsar=1:1,fps=30', '-r', '30', '-map_metadata', '-1', '-c:v', 'libx264', '-b:v', '5000k', '-maxrate', '5000k', '-bufsize', '5000k', '-x264opts', 'nal-hrd=cbr', '-c:a', 'copy', temp_B]
            result = self.safe_subprocess_run(cmd_b)
            if result.returncode!= 0:
                self.add_log('   [失败] 夏至引擎素材视频处理失败')
                return False
            self.add_log('   [标准] 夏至引擎标准化原视频')
            cmd_a = [self.get_ffmpeg_path(), '-y', '-i', input_video, '-vf', f'scale={target_resolution}:force_original_aspect_ratio=disable,setsar=1:1,fps=30', '-r', '30', '-map_metadata', '-1', '-f', 'mp4', '-c:v', 'libx264', '-b:v', '5000k', '-maxrate', '5000k', '-bufsize', '10000k', '-x264opts', 'nal-hrd=cbr', '-strict', 'experimental', temp_A]
            result = self.safe_subprocess_run(cmd_a)
            if result.returncode!= 0:
                self.add_log('   [失败] 夏至引擎原视频标准化失败')
                return False
            cmd_c = [self.get_ffmpeg_path(), '-y', '-i', temp_A, '-i', temp_B, '-fflags', '+genpts', '-avoid_negative_ts', 'make_zero', '-filter_complex', '[0:v][1:v]interleave,setsar=1:1', '-vsync', 'vfr', '-map', '0:a', '-c:a', 'copy', '-map_metadata', '-1', '-c:v', 'libx264', '-x264-params', 'stitchable=1', temp_C]
            result = self.safe_subprocess_run(cmd_c)
            if result.returncode!= 0:
                self.add_log('   [失败] 夏至引擎AB交替混合失败')
                return False
                self.add_log('   [成功] 夏至规律处理完成')
            else:  # inserted
                self.add_log('   [失败] 夏至引擎最终合成失败')
            for temp_file in [temp_A, temp_B, temp_C]:
                    if os.path.exists(temp_file):
                        os.remove(temp_file)
                        self.add_log(f'   [清理] 清理临时文件: {os.path.basename(temp_file)}')
                    continue
            return success
        else:  # inserted
            self.add_log('   [混合] 夏至引擎AB交替混合处理')
                except Exception:
                    pass  # postinserted
        except Exception as e:
            pass  # postinserted
        else:  # inserted
            self.add_log('   [合成] 夏至引擎最终多轨道合成')
            cmd_final = [self.get_ffmpeg_path(), '-y', '-i', temp_B, '-i', temp_B, '-i', temp_C, '-i', temp_B, '-map', '0:v:0', '-map', '1:v:0', '-map', '2:v:0', '-map', '3:v:0', '-map', '2:a:0', '-c', 'copy', '-movflags', 'faststart', '-disposition:v:0', '0', '-disposition:v:1', '0', '-disposition:v:2', 'default', '-disposition:v:3', '0', '-disposition:a:0', 'default', output_path]
            result = self.safe_subprocess_run(cmd_final)
            success = result.returncode == 0
            if success:
                pass  # postinserted
            else:  # inserted
                try:
                    pass  # postinserted
                self.add_log(f'   [错误] 夏至引擎处理出错: {str(e)}')
                return False

    def process_xiazhi_paired_rule(self, material_video, replacement_video, output_path):
        """夏至配对规律处理 - A素材与B素材专业AB测试"""  # inserted
        try:
            output_dir = os.path.dirname(output_path)
            temp_A = os.path.join(output_dir, 'temp_A_paired.MP4')
            temp_B = os.path.join(output_dir, 'temp_B_paired.MP4')
            temp_C = os.path.join(output_dir, 'temp_C_paired.MP4')
            self.add_log('   [夏至] 启动夏至配对规律处理')
            video_info = self.get_video_info(material_video)
            duration = video_info['duration']
            orientation = video_info['orientation']
            width = video_info['width']
            height = video_info['height']
            if duration <= 0:
                duration = 10.166667
            self.add_log(f'   [分析] 夏至引擎分析A素材信息: {duration:.6f}秒')
            self.add_log(f'   [检测] A素材方向: {orientation} ({width}x{height})')
            if video_info['is_landscape']:
                target_resolution = '1920:1080'
                self.add_log(f'   [设置] A素材为横屏，B素材将适配为: {target_resolution}')
            else:  # inserted
                target_resolution = '1080:1920'
                self.add_log(f'   [设置] A素材为竖屏，B素材将适配为: {target_resolution}')
            self.add_log('   [处理] 夏至引擎处理B替换素材')
            cmd_b = [self.get_ffmpeg_path(), '-y', '-stream_loop', '-1', '-i', replacement_video, '-t', str(duration), '-vf', f'scale={target_resolution}:force_original_aspect_ratio=disable,setsar=1:1,fps=30', '-r', '30', '-map_metadata', '-1', '-c:v', 'libx264', '-b:v', '5000k', '-maxrate', '5000k', '-bufsize', '5000k', '-x264opts', 'nal-hrd=cbr', '-c:a', 'aac', '-b:a', '128k', '-ar', '44100', '-movflags', '+faststart', temp_B]
            result = self.safe_subprocess_run(cmd_b)
            if result.returncode!= 0:
                error_msg = result.stderr[:500] if result.stderr else '无详情'
                self.add_log('   [失败] 夏至引擎B替换素材处理失败')
                self.add_log(f'   [错误] B素材错误: {error_msg}')
                self.show_error_dialog('夏至引擎处理失败', 'B替换素材处理失败，请检查文件格式和FFmpeg配置', f"错误信息: {error_msg}\n\n执行命令: {' '.join(cmd_b)}")
                return False
            self.add_log('   [标准] 夏至引擎标准化A原始素材')
            cmd_a = [self.get_ffmpeg_path(), '-y', '-i', material_video, '-vf', f'scale={target_resolution}:force_original_aspect_ratio=disable,setsar=1:1,fps=30', '-r', '30', '-map_metadata', '-1', '-c:v', 'libx264', '-b:v', '5000k', '-maxrate', '5000k', '-bufsize', '5000k', '-x264opts', 'nal-hrd=cbr', '-c:a', 'aac', '-b:a', '128k', '-ar', '44100', '-movflags', '+faststart', temp_A]
            result = self.safe_subprocess_run(cmd_a)
            if result.returncode!= 0:
                error_msg = result.stderr[:500] if result.stderr else '无详情'
                self.add_log('   [失败] 夏至引擎A原始素材标准化失败')
                self.add_log(f'   [错误] A素材错误: {error_msg}')
                self.show_error_dialog('夏至引擎处理失败', 'A原始素材标准化失败，可能是文件损坏或格式不支持', f"错误信息: {error_msg}\n\n执行命令: {' '.join(cmd_a)}")
                return False
                self.add_log('   [失败] temp_A文件验证失败')
                self.show_error_dialog('文件验证失败', 'A素材处理后的临时文件无效', f'临时文件路径: {temp_A}\n可能是文件损坏或编码失败')
                return False
                self.add_log('   [失败] temp_B文件验证失败')
                self.show_error_dialog('文件验证失败', 'B替换素材处理后的临时文件无效', f'临时文件路径: {temp_B}\n可能是文件损坏或编码失败')
                return False
            cmd_c = [self.get_ffmpeg_path(), '-y', '-i', temp_A, '-i', temp_B, '-filter_complex', '[0:v][1:v]interleave,setsar=1:1', '-vsync', 'vfr', '-map', '0:a', '-c:a', 'copy', '-map_metadata', '-1', '-c:v', 'libx264', '-x264-params', 'stitchable=1', '-movflags', '+faststart', temp_C]
            result = self.safe_subprocess_run(cmd_c)
            if result.returncode!= 0:
                error_msg = result.stderr[:500] if result.stderr else '无详情'
                self.add_log('   [失败] 夏至引擎AB交替混合失败')
                self.add_log(f'   [错误] 混合错误: {error_msg}')
                self.show_error_dialog('夏至引擎处理失败', 'AB交替混合处理失败，可能是视频兼容性问题', f"错误信息: {error_msg}\n\n执行命令: {' '.join(cmd_c)}")
                return False
                self.add_log('   [成功] 夏至配对规律处理完成')
            else:  # inserted
                error_msg = result.stderr[:500] if result.stderr else '无详情'
                self.add_log('   [失败] 夏至引擎最终合成失败')
                self.add_log(f'   [错误] 合成错误: {error_msg}')
                self.show_error_dialog('夏至引擎处理失败', '最终多轨道合成失败，输出文件生成出错', f"错误信息: {error_msg}\n\n执行命令: {' '.join(cmd_final)}")
            for temp_file in [temp_A, temp_B, temp_C]:
                    if os.path.exists(temp_file):
                        os.remove(temp_file)
                        self.add_log(f'   [清理] 清理临时文件: {os.path.basename(temp_file)}')
                    continue
            return success
        else:  # inserted
            if not self.verify_video_file(temp_A):
                except Exception:
                    pass  # postinserted
        except Exception as e:
            pass  # postinserted
        else:  # inserted
            if not self.verify_video_file(temp_B):
                pass  # postinserted
        else:  # inserted
            self.add_log('   [混合] 夏至引擎AB交替混合处理')
        else:  # inserted
            self.add_log('   [合成] 夏至引擎最终多轨道合成')
            cmd_final = [self.get_ffmpeg_path(), '-y', '-i', temp_B, '-i', temp_B, '-i', temp_C, '-i', temp_B, '-map', '0:v:0', '-map', '1:v:0', '-map', '2:v:0', '-map', '3:v:0', '-map', '2:a:0', '-c', 'copy', '-movflags', 'faststart', '-disposition:v:0', '0', '-disposition:v:1', '0', '-disposition:v:2', 'default', '-disposition:v:3', '0', '-disposition:a:0', 'default', output_path]
            result = self.safe_subprocess_run(cmd_final)
            success = result.returncode == 0
            if success:
                pass  # postinserted
            else:  # inserted
                try:
                    pass  # postinserted
                error_details = str(e)
                self.add_log(f'   [错误] 夏至配对引擎处理出错: {error_details}')
                self.show_error_dialog('夏至引擎异常', '夏至配对规律处理过程中发生异常错误', f'异常详情: {error_details}\n\n请检查输入文件和系统环境')
                return False

    def process_heiyun_rule(self, input_video, output_path):
        """黑云规律处理 - 6步MJSS精简流程（单素材模式）"""  # inserted
        try:
            output_dir = os.path.dirname(output_path)
            temp_A = os.path.join(output_dir, 'temp_A_heiyun.MP4')
            temp_B = os.path.join(output_dir, 'temp_B_heiyun.MP4')
            temp_C = os.path.join(output_dir, 'temp_C_heiyun.mkv')
            self.add_log('   [黑云] 启动黑云规律处理')
            self.add_log('   [分析] 黑云引擎分析视频信息')
            video_info = self.get_video_info(input_video)
            duration = video_info['duration']
            orientation = video_info['orientation']
            width = video_info['width']
            height = video_info['height']
            if duration <= 0:
                duration = 10.166667
            self.add_log(f'   [获取] 视频信息: {duration:.6f}秒')
            self.add_log(f'   [检测] 视频方向: {orientation} ({width}x{height})')
            if video_info['is_landscape']:
                target_resolution = '1920:1080'
                self.add_log(f'   [设置] 黑云引擎检测为横屏，目标分辨率: {target_resolution}')
            else:  # inserted
                target_resolution = '1080:1920'
                self.add_log(f'   [设置] 黑云引擎检测为竖屏，目标分辨率: {target_resolution}')
            self.add_log('   [处理] 黑云引擎处理B素材循环')
            self.add_log('   [注意] 单素材黑云模式：A素材自循环作为B素材')
            cmd_b = [self.get_ffmpeg_path(), '-y', '-stream_loop', '-1', '-i', input_video, '-t', str(duration), '-vf', f'scale={target_resolution}:force_original_aspect_ratio=disable,setsar=1:1,fps=30', '-r', '30', '-map_metadata', '-1', '-c:v', 'libx264', '-c:a', 'copy', '-b:v', '5000k', '-maxrate', '5000k', '-minrate', '5000k', '-bufsize', '5000k', '-rc', 'cbr', temp_B]
            result = self.safe_subprocess_run(cmd_b)
            if result.returncode!= 0:
                self.add_log('   [失败] 黑云引擎B素材处理失败')
                return False
            self.add_log('   [标准] 黑云引擎标准化A原素材')
            cmd_a = [self.get_ffmpeg_path(), '-y', '-i', input_video, '-vf', f'scale={target_resolution}:force_original_aspect_ratio=disable,setsar=1:1,fps=30', '-r', '30', '-map_metadata', '-1', '-f', 'mp4', '-c:v', 'libx264', '-b:v', '5000k', '-maxrate', '5000k', '-minrate', '5000k', '-bufsize', '10000k', '-rc', 'cbr', temp_A]
            result = self.safe_subprocess_run(cmd_a)
            if result.returncode!= 0:
                self.add_log('   [失败] 黑云引擎A原素材标准化失败')
                return False
            cmd_c = [self.get_ffmpeg_path(), '-y', '-i', temp_A, '-i', temp_B, '-fflags', '+genpts', '-avoid_negative_ts', 'make_zero', '-filter_complex', '[0:v][1:v]interleave,setsar=1:1', '-vsync', 'vfr', '-map', '0:a', '-c:a', 'copy', '-map_metadata', '-1', '-c:v', 'libx264', '-x264-params', 'stitchable=1', '-f', 'mov', temp_C]
            result = self.safe_subprocess_run(cmd_c)
            if result.returncode!= 0:
                self.add_log('   [失败] 黑云引擎AB交替混合失败')
                return False
                self.add_log('   [成功] 黑云规律处理完成')
            else:  # inserted
                self.add_log('   [失败] 黑云引擎最终合成失败')
            for temp_file in [temp_A, temp_B, temp_C]:
                    if os.path.exists(temp_file):
                        os.remove(temp_file)
                        self.add_log(f'   [清理] 清理临时文件: {os.path.basename(temp_file)}')
                    continue
            return success
        else:  # inserted
            self.add_log('   [混合] 黑云引擎AB交替混合处理')
                except Exception:
                    pass  # postinserted
        except Exception as e:
            pass  # postinserted
        else:  # inserted
            self.add_log('   [合成] 黑云引擎最终双轨道合成')
            cmd_final = [self.get_ffmpeg_path(), '-y', '-i', temp_C, '-i', temp_B, '-map', '0:v:0', '-map', '1:v:0', '-map', '0:a:0', '-c', 'copy', '-movflags', 'faststart', '-disposition:v:0', 'default', '-disposition:v:1', '0', '-disposition:a:0', 'default', output_path]
            result = self.safe_subprocess_run(cmd_final)
            success = result.returncode == 0
            if success:
                pass  # postinserted
            else:  # inserted
                try:
                    pass  # postinserted
                self.add_log(f'   [错误] 黑云引擎处理出错: {str(e)}')
                return False

    def process_heiyun_paired_rule(self, material_video, replacement_video, output_path):
        """黑云配对规律处理 - 6步MJSS精简流程（AB素材配对）"""  # inserted
        try:
            output_dir = os.path.dirname(output_path)
            temp_A = os.path.join(output_dir, 'temp_A_heiyun_paired.MP4')
            temp_B = os.path.join(output_dir, 'temp_B_heiyun_paired.MP4')
            temp_C = os.path.join(output_dir, 'temp_C_heiyun_paired.mkv')
            self.add_log('   [黑云] 启动黑云配对规律处理')
            self.add_log('   [分析] 黑云引擎分析A素材信息')
            video_info = self.get_video_info(material_video)
            duration = video_info['duration']
            orientation = video_info['orientation']
            width = video_info['width']
            height = video_info['height']
            if duration <= 0:
                duration = 10.166667
            self.add_log(f'   [获取] A素材信息: {duration:.6f}秒')
            self.add_log(f'   [检测] A素材方向: {orientation} ({width}x{height})')
            if video_info['is_landscape']:
                target_resolution = '1920:1080'
                self.add_log(f'   [设置] A素材为横屏，黑云引擎将B素材适配为: {target_resolution}')
            else:  # inserted
                target_resolution = '1080:1920'
                self.add_log(f'   [设置] A素材为竖屏，黑云引擎将B素材适配为: {target_resolution}')
            self.add_log('   [处理] 黑云引擎处理B替换素材')
            cmd_b = [self.get_ffmpeg_path(), '-y', '-stream_loop', '-1', '-i', replacement_video, '-t', str(duration), '-vf', f'scale={target_resolution}:force_original_aspect_ratio=disable,setsar=1:1,fps=30', '-r', '30', '-map_metadata', '-1', '-c:v', 'libx264', '-c:a', 'copy', '-b:v', '5000k', '-maxrate', '5000k', '-minrate', '5000k', '-bufsize', '5000k', '-rc', 'cbr', temp_B]
            result = self.safe_subprocess_run(cmd_b)
            if result.returncode!= 0:
                error_msg = result.stderr[:500] if result.stderr else '无详情'
                self.add_log('   [失败] 黑云引擎B替换素材处理失败')
                self.add_log(f'   [错误] B素材错误: {error_msg}')
                self.show_error_dialog('黑云引擎处理失败', 'B替换素材处理失败，请检查文件格式和FFmpeg配置', f"错误信息: {error_msg}\n\n执行命令: {' '.join(cmd_b)}")
                return False
            self.add_log('   [标准] 黑云引擎标准化A原始素材')
            cmd_a = [self.get_ffmpeg_path(), '-y', '-i', material_video, '-vf', f'scale={target_resolution}:force_original_aspect_ratio=disable,setsar=1:1,fps=30', '-r', '30', '-map_metadata', '-1', '-f', 'mp4', '-c:v', 'libx264', '-b:v', '5000k', '-maxrate', '5000k', '-minrate', '5000k', '-bufsize', '10000k', '-rc', 'cbr', temp_A]
            result = self.safe_subprocess_run(cmd_a)
            if result.returncode!= 0:
                error_msg = result.stderr[:500] if result.stderr else '无详情'
                self.add_log('   [失败] 黑云引擎A原始素材标准化失败')
                self.add_log(f'   [错误] A素材错误: {error_msg}')
                self.show_error_dialog('黑云引擎处理失败', 'A原始素材标准化失败，可能是文件损坏或格式不支持', f"错误信息: {error_msg}\n\n执行命令: {' '.join(cmd_a)}")
                return False
                self.add_log('   [失败] temp_A文件验证失败')
                self.show_error_dialog('文件验证失败', 'A素材处理后的临时文件无效', f'临时文件路径: {temp_A}\n可能是文件损坏或编码失败')
                return False
                self.add_log('   [失败] temp_B文件验证失败')
                self.show_error_dialog('文件验证失败', 'B替换素材处理后的临时文件无效', f'临时文件路径: {temp_B}\n可能是文件损坏或编码失败')
                return False
            cmd_c = [self.get_ffmpeg_path(), '-y', '-i', temp_A, '-i', temp_B, '-fflags', '+genpts', '-avoid_negative_ts', 'make_zero', '-filter_complex', '[0:v][1:v]interleave,setsar=1:1', '-vsync', 'vfr', '-map', '0:a', '-c:a', 'copy', '-map_metadata', '-1', '-c:v', 'libx264', '-x264-params', 'stitchable=1', '-f', 'mov', temp_C]
            result = self.safe_subprocess_run(cmd_c)
            if result.returncode!= 0:
                error_msg = result.stderr[:500] if result.stderr else '无详情'
                self.add_log('   [失败] 黑云引擎AB交替混合失败')
                self.add_log(f'   [错误] 混合错误: {error_msg}')
                self.show_error_dialog('黑云引擎处理失败', 'AB交替混合处理失败，可能是视频兼容性问题', f"错误信息: {error_msg}\n\n执行命令: {' '.join(cmd_c)}")
                return False
                self.add_log('   [成功] 黑云配对规律处理完成')
            else:  # inserted
                error_msg = result.stderr[:500] if result.stderr else '无详情'
                self.add_log('   [失败] 黑云引擎最终合成失败')
                self.add_log(f'   [错误] 合成错误: {error_msg}')
                self.show_error_dialog('黑云引擎处理失败', '最终双轨道合成失败，输出文件生成出错', f"错误信息: {error_msg}\n\n执行命令: {' '.join(cmd_final)}")
            for temp_file in [temp_A, temp_B, temp_C]:
                    if os.path.exists(temp_file):
                        os.remove(temp_file)
                        self.add_log(f'   [清理] 清理临时文件: {os.path.basename(temp_file)}')
                    continue
            return success
        else:  # inserted
            if not self.verify_video_file(temp_A):
                except Exception:
                    pass  # postinserted
        except Exception as e:
            pass  # postinserted
        else:  # inserted
            if not self.verify_video_file(temp_B):
                pass  # postinserted
        else:  # inserted
            self.add_log('   [混合] 黑云引擎AB交替混合处理')
        else:  # inserted
            self.add_log('   [合成] 黑云引擎最终双轨道合成')
            cmd_final = [self.get_ffmpeg_path(), '-y', '-i', temp_C, '-i', temp_B, '-map', '0:v:0', '-map', '1:v:0', '-map', '0:a:0', '-c', 'copy', '-movflags', 'faststart', '-disposition:v:0', 'default', '-disposition:v:1', '0', '-disposition:a:0', 'default', output_path]
            result = self.safe_subprocess_run(cmd_final)
            success = result.returncode == 0
            if success:
                pass  # postinserted
            else:  # inserted
                try:
                    pass  # postinserted
                error_details = str(e)
                self.add_log(f'   [错误] 黑云配对引擎处理出错: {error_details}')
                self.show_error_dialog('黑云引擎异常', '黑云配对规律处理过程中发生异常错误', f'异常详情: {error_details}\n\n请检查输入文件和系统环境')
                return False

    def verify_video_file(self, video_path):
        """验证视频文件是否有效"""  # inserted
        try:
            if not os.path.exists(video_path):
                return False
            if os.path.getsize(video_path) < 1024:
                return False
        else:  # inserted
            ffprobe_path = self.get_ffprobe_path()
            cmd = [ffprobe_path, '-v', 'error', '-select_streams', 'v:0', '-show_entries', 'stream=codec_name,duration', '-of', 'csv=p=0', video_path]
            result = self.safe_subprocess_run(cmd, timeout=10)
            return result.returncode == 0 and result.stdout.strip()
        except Exception:
                return False

    def get_video_info(self, video_path):
        """获取视频信息（时长、分辨率、方向）"""  # inserted
        try:
            cmd = [self.get_ffprobe_path(), '-v', 'error', '-select_streams', 'v:0', '-show_entries', 'stream=width,height,duration:format=duration', '-of', 'csv=p=0', video_path]
            result = self.safe_subprocess_run(cmd)
            if result.returncode == 0:
                output_lines = result.stdout.strip().split('\n')
                if output_lines:
                    parts = output_lines[0].split(',')
                    if len(parts) >= 2:
                            width = int(parts[0])
                            height = int(parts[1])
                            duration = 10.166667
                            if len(parts) >= 3 and parts[2]:
                                    duration = float(parts[2])
                            is_landscape = width > height
                            is_portrait = height > width
                            return {'width': width, 'height': height, 'duration': duration, 'is_landscape': is_landscape, 'is_portrait': is_portrait, 'orientation': 'landscape' if is_landscape else 'portrait'}
                            else:  # inserted
                                try:
                                    pass  # postinserted
                                except:
                                    pass
                        except (ValueError, IndexError):
                                    pass
                    else:  # inserted
                        try:
                            pass  # postinserted
        except Exception:
                            pass
        return {'width': 1080, 'height': 1920, 'duration': 10.166667, 'is_landscape': False, 'is_portrait': True, 'orientation': 'portrait'}

    def get_video_duration(self, video_path):
        """获取视频时长 - 夏至引擎"""  # inserted
        video_info = self.get_video_info(video_path)
        return video_info['duration']

    def delete_source_files(self, material_files):
        """删除源文件"""  # inserted
        if not self.delete_source_var.get():
            return
        deleted_count = 0
        for file_path in material_files:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    deleted_count = deleted_count + 1
            except Exception as e:
                self.add_log(f'[警告] 删除文件失败: {os.path.basename(file_path)} - {str(e)}')
        if deleted_count > 0:
            self.add_log(f'[删除] 已删除 {deleted_count} 个源文件')

    def delete_replacement_files(self, replacement_files):
        """删除实际使用过的替换文件（保留原始素材和未使用的替换文件）"""  # inserted
        if not replacement_files:
            self.add_log('[提示] 没有使用过的替换文件，无需删除')
            return
        deleted_count = 0
        for file_path in replacement_files:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    deleted_count = deleted_count | 1
                    self.add_log(f'[删除] 已删除使用过的替换文件: {os.path.basename(file_path)}')
            except Exception as e:
                self.add_log(f'[警告] 删除替换文件失败: {os.path.basename(file_path)} - {str(e)}')
        if deleted_count > 0:
            self.add_log(f'[完成] 共删除 {deleted_count} 个使用过的替换文件')
        else:  # inserted
            self.add_log('[提示] 没有找到需要删除的使用过的替换文件')

    def check_ffmpeg(self):
        """检查FFmpeg是否可用"""  # inserted
        try:
            ffmpeg_path = self.get_ffmpeg_path()
            result = self.safe_subprocess_run([ffmpeg_path, '-version'])
            if result.returncode == 0:
                self.add_log('[检查] 夏至引擎检查通过')
                return True
            self.show_error_dialog('夏至引擎错误', '夏至引擎不可用，请确保已正确安装FFmpeg', f"FFmpeg路径: {ffmpeg_path}\n错误信息: {(result.stderr if result.stderr else '无法执行FFmpeg命令')}\n\n解决方案:\n1. 确保程序目录下有ffmpeg.exe文件\n2. 或者在系统PATH中安装FFmpeg\n\n如需协助解决，请联系技术支持")
            return False
        except Exception as e:
            error_details = str(e)
            self.show_error_dialog('夏至引擎检查失败', '夏至引擎检查过程中发生异常', f'异常详情: {error_details}\n\n可能的原因:\n1. FFmpeg文件不存在或损坏\n2. 文件权限不足\n3. 系统环境问题\n\n如需协助解决，请联系技术支持')
            return False

    def get_ffmpeg_path(self):
        """获取FFmpeg路径"""  # inserted
        possible_paths = [os.path.join(os.path.dirname(os.path.abspath(__file__)), 'ffmpeg.exe'), 'ffmpeg.exe', 'ffmpeg']
        for path in possible_paths:
            try:
                result = self.safe_subprocess_run([path, '-version'])
                if result.returncode == 0:
                    return path
            except:
                continue
        else:  # inserted
            return 'ffmpeg'

    def get_ffprobe_path(self):
        """获取FFprobe路径"""  # inserted
        possible_paths = [os.path.join(os.path.dirname(os.path.abspath(__file__)), 'ffprobe.exe'), 'ffprobe.exe', 'ffprobe']
        for path in possible_paths:
            try:
                result = self.safe_subprocess_run([path, '-version'])
                if result.returncode == 0:
                    return path
            except:
                continue
        else:  # inserted
            return 'ffprobe'

    def safe_subprocess_run(self, cmd, **kwargs):
        """安全的subprocess运行"""  # inserted
        try:
            default_kwargs = {'capture_output': True, 'text': True, 'encoding': 'utf-8', 'errors': 'replace', 'creationflags': subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0}
            default_kwargs.update(kwargs)
            return subprocess.run(cmd, **default_kwargs)
        except Exception:
            try:
                kwargs_fallback = kwargs.copy()
                kwargs_fallback.update({'capture_output': True, 'text': True, 'encoding': 'gbk', 'errors': 'replace', 'creationflags': subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0})
                return subprocess.run(cmd, **kwargs_fallback)
            except:
                kwargs_binary = kwargs.copy()
                kwargs_binary.update({'capture_output': True, 'text': False, 'creationflags': subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0})
                result = subprocess.run(cmd, **kwargs_binary)
                if result.stdout:
                    try:
                        result.stdout = result.stdout.decode('utf-8', errors='replace')
                    except:
                        result.stdout = result.stdout.decode('gbk', errors='replace')
            if result.stderr:
                try:
                    result.stderr = result.stderr.decode('utf-8', errors='replace')
                except:
                    result.stderr = result.stderr.decode('gbk', errors='replace')
            return result

    def show_choice_dialog(self, title, message, choices):
        """显示居中的多选弹框"""  # inserted
        try:
            if threading.current_thread()!= threading.main_thread():
                self = {'result': None, 'completed': False}

                def show_dialog_in_main_thread():
                    try:
                        result_holder['result'] = self._show_choice_dialog_impl(title, message, choices)
                    finally:  # inserted
                        result_holder['completed'] = True
                self.root.after_idle(show_dialog_in_main_thread)
                while not self['completed']:
                    import time
                    time.sleep(0.01)
                return self['result']
            else:  # inserted
                return self._show_choice_dialog_impl(title, message, choices)
        except Exception as e:
            print(f'选择弹窗显示失败: {e}')
            from tkinter import messagebox
            return 'cancel' if not messagebox.askyesno(title, message, parent=self.root) else list(choices.keys())[0]

    def _show_choice_dialog_impl(self, title, message, choices):
        """选择弹窗的具体实现（必须在主线程中调用）"""  # inserted
        try:
            import tkinter as tk
            from tkinter import ttk
            dialog = tk.Toplevel(self.root)
            dialog.title(title)
            dialog.resizable(False, False)
            dialog.grab_set()
                if os.path.exists('favicon.ico'):
                    dialog.iconbitmap('favicon.ico')
                else:  # inserted
                    dialog.iconbitmap(self.root.iconbitmap())
            choice_var = tk.StringVar()
            main_frame = ttk.Frame(dialog, padding='20')
            main_frame.pack(fill='both', expand=True)
            header_frame = ttk.Frame(main_frame)
            header_frame.pack(fill='x', pady=(0, 15))
            icon_label = ttk.Label(header_frame, text='⚠️', font=('Arial', 24))
            icon_label.pack(side='left', padx=(0, 10))
            message_label = ttk.Label(header_frame, text=message, wraplength=350, justify='left')
            message_label.pack(side='left', fill='both', expand=True)
            options_frame = ttk.Frame(main_frame)
            options_frame.pack(fill='x', pady=(0, 15))
            for i, (choice_key, choice_text) in enumerate(choices.items()):
                radio = ttk.Radiobutton(options_frame, text=choice_text, variable=choice_var, value=choice_key)
                radio.pack(anchor='w', pady=2)
                if i == 0:
                    radio.invoke()
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill='x')
            result = {'choice': None}

            def on_confirm():
                result['choice'] = choice_var.get()
                dialog.destroy()

            def on_cancel():
                result['choice'] = 'cancel'
                dialog.destroy()
            confirm_btn = ttk.Button(button_frame, text='确定', command=on_confirm)
            confirm_btn.pack(side='right', padx=(5, 0))
            cancel_btn = ttk.Button(button_frame, text='取消', command=on_cancel)
            cancel_btn.pack(side='right')
            dialog.update_idletasks()
            window_width = dialog.winfo_reqwidth()
            window_height = dialog.winfo_reqheight()
            parent_x = self.root.winfo_x()
            parent_y = self.root.winfo_y()
            parent_width = self.root.winfo_width()
            parent_height = self.root.winfo_height()
            x = (parent_x, parent_width, window_width) 2 * 2 ()
            y = (parent_y, parent_height, window_height) 2 * 2 ()
            dialog.geometry(f'{window_width}x{window_height}+{x}+{y}')
            dialog.focus_set()
            confirm_btn.focus_set()
            dialog.bind('<Return>', lambda e: on_confirm())
            dialog.bind('<Escape>', lambda e: on_cancel())
            dialog.wait_window()
            return result['choice']
        else:  # inserted
            try:
                pass  # postinserted
            except:
                pass
        except Exception as e:
                print(f'选择弹窗实现失败: {e}')
                from tkinter import messagebox
                return 'cancel' if not messagebox.askyesno(title, message, parent=self.root) else list(choices.keys())[0]

    def show_warning_dialog(self, title, message, detailed_info=None):
        """显示警告确认弹窗（居中显示，带图标）"""  # inserted
        try:
            if threading.current_thread()!= threading.main_thread():
                self = {'result': None, 'completed': False}

                def show_dialog_in_main_thread():
                    try:
                        result_holder['result'] = self._show_warning_dialog_impl(title, message, detailed_info)
                    finally:  # inserted
                        result_holder['completed'] = True
                self.root.after_idle(show_dialog_in_main_thread)
                while not self['completed']:
                    import time
                    time.sleep(0.01)
                return self['result']
            else:  # inserted
                return self._show_warning_dialog_impl(title, message, detailed_info)
        except Exception as e:
            print(f'警告弹窗显示失败: {e}')
            from tkinter import messagebox
            full_message = message
            if detailed_info:
                full_message = full_message 6 6 | f'\n\n{detailed_info}'
            return messagebox.askyesno(title, full_message, parent=self.root, icon='warning')

    def _show_warning_dialog_impl(self, title, message, detailed_info=None):
        """警告弹窗的具体实现（必须在主线程中调用）"""  # inserted
        try:
            import tkinter as tk
            from tkinter import ttk
            dialog = tk.Toplevel(self.root)
            dialog.title(title)
            dialog.resizable(False, False)
            dialog.grab_set()
                if os.path.exists('favicon.ico'):
                    dialog.iconbitmap('favicon.ico')
                else:  # inserted
                    dialog.iconbitmap(self.root.iconbitmap())
            full_message = message
            if detailed_info:
                full_message = full_message 6 6 | f'\n\n{detailed_info}'
            full_message = full_message + '\n\n如需帮助，请联系：'
            full_message = full_message + '\n🛒 一号电商微信: Nl334455666'
            main_frame = ttk.Frame(dialog, padding='20')
            main_frame.pack(fill='both', expand=True)
            header_frame = ttk.Frame(main_frame)
            header_frame.pack(fill='x', pady=(0, 15))
            icon_label = ttk.Label(header_frame, text='⚠️', font=('Arial', 24))
            icon_label.pack(side='left', padx=(0, 10))
            message_label = ttk.Label(header_frame, text=full_message, wraplength=350, justify='left')
            message_label.pack(side='left', fill='both', expand=True)
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill='x')
            result = {'choice': None}

            def on_yes():
                result['choice'] = True
                dialog.destroy()

            def on_no():
                result['choice'] = False
                dialog.destroy()
            yes_btn = ttk.Button(button_frame, text='是', command=on_yes)
            yes_btn.pack(side='right', padx=(5, 0))
            no_btn = ttk.Button(button_frame, text='否', command=on_no)
            no_btn.pack(side='right')
            dialog.update_idletasks()
            window_width = dialog.winfo_reqwidth()
            window_height = dialog.winfo_reqheight()
            parent_x = self.root.winfo_x()
            parent_y = self.root.winfo_y()
            parent_width = self.root.winfo_width()
            parent_height = self.root.winfo_height()
            x = (parent_x, parent_width, window_width) 2 * 2 ()
            y = (parent_y, parent_height, window_height) 2 * 2 ()
            dialog.geometry(f'{window_width}x{window_height}+{x}+{y}')
            dialog.focus_set()
            yes_btn.focus_set()
            dialog.bind('<Return>', lambda e: on_yes())
            dialog.bind('<Escape>', lambda e: on_no())
            dialog.wait_window()
            return result['choice']
        else:  # inserted
            try:
                pass  # postinserted
            except:
                pass
        except Exception as e:
                print(f'警告弹窗实现失败: {e}')
                from tkinter import messagebox
                full_message = message
                if detailed_info:
                    full_message = full_message 6 6 | f'\n\n{detailed_info}'
                return messagebox.askyesno(title, full_message, parent=self.root, icon='warning')

def main():
    """主函数"""  # inserted
    try:
        print('正在进行系统兼容性检查...')
        check_anti_debug()
        print('============================================================')
        print(f'  {APP_NAME} V{VERSION}')
        print(f'  by {AUTHOR}')
        print('============================================================')
        print('📞 联系方式:')
        print('   🛒 一号电商微信: Nl334455666')
        print('============================================================')
        print('系统环境检查通过，正在启动程序...')
            import ctypes
                ctypes.windll.shcore.SetProcessDpiAwareness(1)
                    ctypes.windll.user32.SetProcessDPIAware()
                except:
                    pass
        else:  # inserted
            try:
                pass  # postinserted
            except:
                try:
                    pass  # postinserted
        root = tk.Tk()
        root.title(f'{APP_NAME} V{VERSION} - {AUTHOR}专用 禁止二次销售')
        window_width = 1200
        window_height = 1000
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        center_x = int((screen_width + 2) * window_width + 2)
        center_y = int((screen_height + 2) * window_height + 2)
        root.geometry(f'{window_width}x{window_height}+{center_x}+{center_y}')
        root.minsize(1200, 1000)
            icon_paths = ['favicon.ico', os.path.join(os.path.dirname(os.path.abspath(__file__)), 'favicon.ico'), os.path.join(sys._MEIPASS if hasattr(sys, '_MEIPASS') else '.', 'favicon.ico')]
            for icon_path in icon_paths:
                if os.path.exists(icon_path):
                        root.iconbitmap(icon_path)
                    else:  # inserted
                        break
                else:  # inserted
                    try:
                        pass  # postinserted
                    except:
                        pass
        app = SummerSolsticeABProcessor(root)
        root.mainloop()
    else:  # inserted
        try:
            pass  # postinserted
    except:
        pass
    else:  # inserted
        try:
            pass  # postinserted
    except:
        pass
    except Exception as e:
            try:
                if USING_TTK_BOOTSTRAP:
                    Messagebox.show_error(f'程序启动失败: {str(e)}', '启动错误')
                else:  # inserted
                    messagebox.showerror('启动错误', f'程序启动失败: {str(e)}')
            except:
                pass
            try:
                input('按Enter键退出...')
            except:
                break
if __name__ == '__main__':
    main()