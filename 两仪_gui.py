import os
import subprocess
import json
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget,
                           QPushButton, QLabel, QFileDialog, QTextEdit,
                           QLineEdit, QHBoxLayout)
from PyQt5.QtCore import QThread, pyqtSignal

class VideoProcessorThread(QThread):
    progress_signal = pyqtSignal(str)
    finished_signal = pyqtSignal()

    def __init__(self, main_dir, aux_dir, output_dir):
        super().__init__()
        self.main_dir = main_dir
        self.aux_dir = aux_dir
        self.output_dir = output_dir

    def run(self):
        main_videos = sorted([os.path.join(self.main_dir, f) 
                            for f in os.listdir(self.main_dir) 
                            if f.endswith('.mp4')])
        aux_videos = sorted([os.path.join(self.aux_dir, f) 
                           for f in os.listdir(self.aux_dir) 
                           if f.endswith('.mp4')])

        if len(main_videos) != len(aux_videos):
            self.progress_signal.emit("警告: 主视频和辅助视频数量不匹配")

        total_pairs = len(main_videos)
        success_count = 0
        
        for idx, (main_video, aux_video) in enumerate(zip(main_videos, aux_videos), 1):
            self.progress_signal.emit(f"\n=== 正在处理第 {idx}/{total_pairs} 对视频 ===")
            if self.process_video_pair(main_video, aux_video):
                success_count += 1
                self.progress_signal.emit(f"=== 第 {idx} 对视频处理成功 ===")
            else:
                self.progress_signal.emit(f"!!! 第 {idx} 对视频处理失败 !!!")

        self.progress_signal.emit(f"\n处理完成! 成功处理 {success_count}/{total_pairs} 对视频")
        self.finished_signal.emit()

    def process_video_pair(self, main_video, aux_video):
        try:
            temp_dir = os.path.join(self.output_dir, "temp")
            os.makedirs(temp_dir, exist_ok=True)
            
            output_name = os.path.basename(main_video).split('.')[0] + "_processed.mp4"
            output_path = os.path.join(self.output_dir, output_name)

            self.progress_signal.emit(f"\n处理视频对: {os.path.basename(main_video)} 和 {os.path.basename(aux_video)}")

            # 获取主视频信息
            probe_cmd = f'ffprobe -v error -show_entries format=duration -show_entries stream=width,height -of json "{main_video}"'
            probe_result = subprocess.run(probe_cmd, shell=True, capture_output=True, text=True, creationflags=subprocess.CREATE_NO_WINDOW)
            video_info = json.loads(probe_result.stdout)
            
            duration = float(video_info['format']['duration'])
            width = int(video_info['streams'][0]['width'])
            height = int(video_info['streams'][0]['height'])

            self.progress_signal.emit(f"主视频信息: 时长={duration:.2f}s, 分辨率={width}x{height}")

            # 检查并转换分辨率
            target_width = 1080
            target_height = 1920
            processed_main = main_video
            processed_aux = aux_video

            if width != target_width or height != target_height:
                self.progress_signal.emit("分辨率不符合要求，正在转换...")
                processed_main = os.path.join(temp_dir, "converted_main.mp4")
                cmd_convert = f'ffmpeg -y -i "{main_video}" -vf "scale={target_width}:{target_height}" -c:v libx264 -crf 20 -c:a copy "{processed_main}"'
                subprocess.run(cmd_convert, shell=True, check=True, creationflags=subprocess.CREATE_NO_WINDOW)

            # 检查辅助视频分辨率
            probe_cmd_aux = f'ffprobe -v error -show_entries stream=width,height -of json "{aux_video}"'
            probe_result_aux = subprocess.run(probe_cmd_aux, shell=True, capture_output=True, text=True, creationflags=subprocess.CREATE_NO_WINDOW)
            aux_info = json.loads(probe_result_aux.stdout)
            aux_width = int(aux_info['streams'][0]['width'])
            aux_height = int(aux_info['streams'][0]['height'])

            if aux_width != target_width or aux_height != target_height:
                self.progress_signal.emit("辅助视频分辨率不符合要求，正在转换...")
                processed_aux = os.path.join(temp_dir, "converted_aux.mp4")
                cmd_convert_aux = f'ffmpeg -y -i "{aux_video}" -vf "scale={target_width}:{target_height}" -c:v libx264 -crf 20 -c:a copy "{processed_aux}"'
                subprocess.run(cmd_convert_aux, shell=True, check=True, creationflags=subprocess.CREATE_NO_WINDOW)

            # 使用用户指定的命令格式处理
            self.progress_signal.emit("开始视频处理...")
            cmd = f'ffmpeg -y -i "{processed_main}" -stream_loop -1 -i "{processed_aux}" ' \
                  f'-filter_complex "[1:v]fps=30,trim=duration={duration}[v1p];' \
                  f'[0:v]fps=30[v0p];' \
                  f'[v1p][v0p]scale2ref[v1s][v0s];' \
                  f'[v1s]setsar=1[v1f];' \
                  f'[v0s][v1f]interleave[v]" ' \
                  f'-map [v] -map 0:a? -c:v libx264 -crf 20 -profile:v main -level 4.0 ' \
                  f'-c:a copy -metadata:s:v:0 stereo_mode=frame_seq "{output_path}"'
            subprocess.run(cmd, shell=True, check=True, creationflags=subprocess.CREATE_NO_WINDOW)

            # 清理临时转换文件
            if processed_main != main_video:
                os.remove(processed_main)
            if processed_aux != aux_video:
                os.remove(processed_aux)
                
            return True

        except Exception as e:
            self.progress_signal.emit(f"处理失败: {str(e)}")
            # 确保清理临时文件
            if 'processed_main' in locals() and processed_main != main_video and os.path.exists(processed_main):
                os.remove(processed_main)
            if 'processed_aux' in locals() and processed_aux != aux_video and os.path.exists(processed_aux):
                os.remove(processed_aux)
            return False

class VideoProcessorGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("立马扬鞭视频处理器")
        self.setGeometry(100, 100, 600, 400)
        self.init_ui()

    def init_ui(self):
        central_widget = QWidget()
        layout = QVBoxLayout()

        # 主视频目录选择
        main_layout = QHBoxLayout()
        self.main_dir_edit = QLineEdit()
        self.main_dir_edit.setPlaceholderText("主视频目录")
        main_btn = QPushButton("选择")
        main_btn.clicked.connect(lambda: self.select_dir(self.main_dir_edit))
        main_layout.addWidget(self.main_dir_edit)
        main_layout.addWidget(main_btn)
        layout.addLayout(main_layout)

        # 辅助视频目录选择
        aux_layout = QHBoxLayout()
        self.aux_dir_edit = QLineEdit()
        self.aux_dir_edit.setPlaceholderText("辅助视频目录")
        aux_btn = QPushButton("选择")
        aux_btn.clicked.connect(lambda: self.select_dir(self.aux_dir_edit))
        aux_layout.addWidget(self.aux_dir_edit)
        aux_layout.addWidget(aux_btn)
        layout.addLayout(aux_layout)

        # 输出目录选择
        output_layout = QHBoxLayout()
        self.output_dir_edit = QLineEdit()
        self.output_dir_edit.setPlaceholderText("输出目录")
        output_btn = QPushButton("选择")
        output_btn.clicked.connect(lambda: self.select_dir(self.output_dir_edit))
        output_layout.addWidget(self.output_dir_edit)
        output_layout.addWidget(output_btn)
        layout.addLayout(output_layout)

        # 处理按钮
        self.process_btn = QPushButton("开始处理")
        self.process_btn.clicked.connect(self.start_processing)
        layout.addWidget(self.process_btn)

        # 进度显示
        self.progress_display = QTextEdit()
        self.progress_display.setReadOnly(True)
        layout.addWidget(self.progress_display)

        central_widget.setLayout(layout)
        self.setCentralWidget(central_widget)

    def select_dir(self, line_edit):
        dir_path = QFileDialog.getExistingDirectory(self, "选择目录")
        if dir_path:
            line_edit.setText(dir_path)

    def start_processing(self):
        main_dir = self.main_dir_edit.text()
        aux_dir = self.aux_dir_edit.text()
        output_dir = self.output_dir_edit.text()

        if not all([main_dir, aux_dir, output_dir]):
            self.progress_display.append("请选择所有目录!")
            return

        self.process_btn.setEnabled(False)
        self.progress_display.clear()

        self.worker = VideoProcessorThread(main_dir, aux_dir, output_dir)
        self.worker.progress_signal.connect(self.progress_display.append)
        self.worker.finished_signal.connect(lambda: self.process_btn.setEnabled(True))
        self.worker.start()

if __name__ == "__main__":
    app = QApplication([])
    window = VideoProcessorGUI()
    window.show()
    app.exec_()
