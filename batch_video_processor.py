import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import subprocess
import os
import threading
from datetime import datetime

class BatchVideoProcessor:
    def __init__(self, root):
        self.root = root
        self.root.title("小鸡快跑DY版")
        
        # 初始化变量
        self.input_folder = ""
        self.output_folder = ""
        self.processing = False
        self.current_file_index = 0
        self.video_files = []
        
        # 创建UI
        self.create_ui()
        
    def create_ui(self):
        """创建用户界面"""
        # 输入文件夹选择
        tk.Label(self.root, text="输入文件夹:").grid(row=0, column=0, padx=5, pady=5)
        self.input_entry = tk.Entry(self.root, width=50)
        self.input_entry.grid(row=0, column=1, padx=5, pady=5)
        tk.Button(self.root, text="浏览", command=self.select_input_folder).grid(row=0, column=2, padx=5, pady=5)
        
        # 输出文件夹选择
        tk.Label(self.root, text="输出文件夹:").grid(row=1, column=0, padx=5, pady=5)
        self.output_entry = tk.Entry(self.root, width=50)
        self.output_entry.grid(row=1, column=1, padx=5, pady=5)
        tk.Button(self.root, text="浏览", command=self.select_output_folder).grid(row=1, column=2, padx=5, pady=5)
        
        # 处理按钮
        self.process_btn = tk.Button(self.root, text="开始处理", command=self.start_processing)
        self.process_btn.grid(row=2, column=1, pady=10)
        
        # 进度条
        self.progress = ttk.Progressbar(self.root, orient="horizontal", length=400, mode="determinate")
        self.progress.grid(row=3, column=0, columnspan=3, padx=5, pady=5)
        
        # 状态显示
        self.status_var = tk.StringVar()
        self.status_var.set("准备就绪")
        tk.Label(self.root, textvariable=self.status_var).grid(row=4, column=0, columnspan=3)
        
    def select_input_folder(self):
        """选择输入文件夹"""
        folder = filedialog.askdirectory()
        if folder:
            self.input_folder = folder
            self.input_entry.delete(0, tk.END)
            self.input_entry.insert(0, folder)
            
    def select_output_folder(self):
        """选择输出文件夹"""
        folder = filedialog.askdirectory()
        if folder:
            self.output_folder = folder
            self.output_entry.delete(0, tk.END)
            self.output_entry.insert(0, folder)
            
    def start_processing(self):
        """开始处理视频"""
        if not self.input_folder or not self.output_folder:
            messagebox.showerror("错误", "请先选择输入和输出文件夹")
            return
            
        # 获取视频文件列表
        self.video_files = [f for f in os.listdir(self.input_folder) 
                          if f.lower().endswith(('.mp4', '.avi', '.mov'))]
        
        if not self.video_files:
            messagebox.showerror("错误", "输入文件夹中没有视频文件")
            return
            
        # 初始化处理状态
        self.current_file_index = 0
        self.processing = True
        self.process_btn.config(state=tk.DISABLED)
        self.status_var.set(f"准备处理 {len(self.video_files)} 个视频...")
        self.progress["maximum"] = len(self.video_files)
        self.progress["value"] = 0
        
        # 启动处理线程
        threading.Thread(target=self.process_videos_thread, daemon=True).start()
        
    def process_videos_thread(self):
        """视频处理线程"""
        try:
            while self.current_file_index < len(self.video_files) and self.processing:
                video_file = self.video_files[self.current_file_index]
                input_path = os.path.join(self.input_folder, video_file)
                output_path = os.path.join(self.output_folder, f"processed_{video_file}")
                
                # 更新UI状态
                self.root.after(0, lambda: self.status_var.set(
                    f"正在处理 {self.current_file_index+1}/{len(self.video_files)}: {video_file}"))
                
                # 处理当前视频
                self.process_single_video(input_path, output_path)
                
                # 更新进度
                self.current_file_index += 1
                self.root.after(0, lambda: self.progress.step(1))
                
            # 处理完成
            self.root.after(0, self.processing_complete)
            
        except Exception as e:
            self.root.after(0, lambda: self.show_error(f"处理失败: {str(e)}"))
            
    def get_ffmpeg_path(self):
        """获取ffmpeg路径"""
        # 首先检查当前目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        local_path = os.path.join(current_dir, "ffmpeg.exe")
        if os.path.exists(local_path):
            return local_path
        
        # 检查系统PATH
        try:
            subprocess.run(["ffmpeg", "-version"], stdout=subprocess.PIPE, stderr=subprocess.PIPE, creationflags=subprocess.CREATE_NO_WINDOW)
            return "ffmpeg"
        except:
            raise Exception("找不到ffmpeg可执行文件")

    def get_ffprobe_path(self):
        """获取ffprobe路径"""
        # 首先检查当前目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        local_path = os.path.join(current_dir, "ffprobe.exe")
        if os.path.exists(local_path):
            return local_path
        
        # 检查系统PATH
        try:
            subprocess.run(["ffprobe", "-version"], stdout=subprocess.PIPE, stderr=subprocess.PIPE, creationflags=subprocess.CREATE_NO_WINDOW)
            return "ffprobe"
        except:
            raise Exception("找不到ffprobe可执行文件")

    def safe_subprocess_run(self, cmd):
        """安全执行命令"""
        return subprocess.run(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding='utf-8',
            errors='ignore',
            creationflags=subprocess.CREATE_NO_WINDOW
        )

    def process_single_video(self, input_video, output_path):
        """处理单个视频"""
        try:
            # 创建临时文件路径
            temp_dir = os.path.join(self.output_folder, "temp")
            os.makedirs(temp_dir, exist_ok=True)
            
            base_name = os.path.splitext(os.path.basename(input_video))[0]
            temp1_path = os.path.join(temp_dir, f"{base_name}_temp1.mp4")
            temp2_path = os.path.join(temp_dir, f"{base_name}_temp2.mp4")
            
            # 命令1: 获取视频信息
            cmd = [
                self.get_ffprobe_path(), 
                '-v', 'quiet', 
                '-print_format', 'json', 
                '-show_format', 
                '-show_streams', 
                input_video
            ]
            result = self.safe_subprocess_run(cmd)
            
            # 命令2: 第一次处理
            cmd2 = [
                self.get_ffmpeg_path(),
                '-i', input_video,
                '-c:v', 'libx264',
                '-b:v', '3000k',
                '-maxrate:v:0', '3000k',
                '-bufsize:v:0', '3000k',
                '-x264opts', 'nal-hrd=cbr',
                '-c:a', 'copy',
                '-y', temp1_path
            ]
            result2 = self.safe_subprocess_run(cmd2)
            
            # 命令3: 第二次处理
            cmd3 = [
                self.get_ffmpeg_path(),
                '-i', input_video,
                '-c:v:0', 'libx264',
                '-b:v:0', '5000k',
                '-maxrate:v:0', '5000k',
                '-bufsize:v:0', '5000k',
                '-x264opts', 'nal-hrd=cbr',
                '-bsf:v', 'noise=amount=-20',
                '-c:a', 'copy',
                '-y', temp2_path
            ]
            result3 = self.safe_subprocess_run(cmd3)
            
            # 命令4: 合并处理(按照用户要求修改)
            cmd4 = [
                self.get_ffmpeg_path(),
                '-y',
                '-i', temp1_path,  # 主视频临时文件
                '-i', temp2_path,  # 辅助视频临时文件
                '-filter_complex', '[0:v][1:v]interleave',
                '-map', '0:a',    # 保留主视频音频
                '-c:a', 'copy',   # 直接复制音频流
                '-c:v', 'libx264',
                '-b:v', '10M',    # 设置视频码率
                output_path
            ]
            result4 = self.safe_subprocess_run(cmd4)
            
            # 清理临时文件
            for temp_file in [temp1_path, temp2_path]:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
                    
        except Exception as e:
            raise Exception(f"处理视频 {input_video} 失败: {str(e)}")
            
    def run_command(self, cmd, description=""):
        """执行命令并隐藏CMD窗口"""
        try:
            result = subprocess.run(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',
                errors='ignore',
                check=True,
                creationflags=subprocess.CREATE_NO_WINDOW
            )
            
            # 记录命令执行情况
            log_msg = f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] {description} 命令执行成功"
            self.root.after(0, lambda: self.status_var.set(log_msg))
            
        except subprocess.CalledProcessError as e:
            error_msg = f"{description} 命令失败: {str(e.stderr).encode('utf-8', errors='ignore').decode('utf-8')}"
            raise Exception(error_msg)
            
    def processing_complete(self):
        """处理完成"""
        self.processing = False
        self.process_btn.config(state=tk.NORMAL)
        self.status_var.set(f"处理完成: {self.current_file_index}个视频已处理")
        messagebox.showinfo("完成", f"已处理 {self.current_file_index} 个视频")
        
    def show_error(self, message):
        """显示错误信息"""
        self.processing = False
        self.process_btn.config(state=tk.NORMAL)
        self.status_var.set("处理失败")
        messagebox.showerror("错误", message)

if __name__ == "__main__":
    root = tk.Tk()
    app = BatchVideoProcessor(root)
    root.mainloop()
