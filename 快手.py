# Decompiled with PyLingual (https://pylingual.io)
# Internal filename: 快天下新版老板多模式选择.py
# Bytecode version: 3.11a7e (3495)
# Source timestamp: 1970-01-01 00:00:00 UTC (0)

import subprocess
import json
import os
import sys
import tempfile
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QHBoxLayout, QWidget, QLabel, QTextEdit, QFileDialog, QProgressBar, QMessageBox, QFrame, QSizePolicy, QComboBox
from PyQt5.QtCore import QThread, pyqtSignal, Qt
from PyQt5.QtGui import QFont

class VideoProcessor(QThread):
    progress_signal = pyqtSignal(int, str)
    finished_signal = pyqtSignal(bool, str)
    task_finished_signal = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self.video_folder = ''
        self.image_folder = ''
        # 确保输出目录始终在exe同目录下的output文件夹
        if getattr(sys, 'frozen', False):
            base_dir = os.path.dirname(sys.executable)
        else:
            base_dir = os.path.dirname(os.path.abspath(__file__))
        self.output_dir = os.path.join(base_dir, 'output')
        self.running = False
        self.current_task = ''
        self.total_tasks = 0
        self.completed_tasks = 0
        self.processing_mode = 'normal'

    def set_processing_mode(self, mode):
        """设置处理模式：normal(普通) 或 high_quality(高质量)"""  # inserted
        self.processing_mode = mode

    def get_ffmpeg_path(self):
        """获取FFmpeg和FFprobe的路径"""  # inserted
        if getattr(sys, 'frozen', False):
            base_path = os.path.dirname(sys.executable)
            ffmpeg_path = os.path.join(base_path, 'ffmpeg/ffmpeg.exe')
            ffprobe_path = os.path.join(base_path, 'ffmpeg/ffprobe.exe')
        else:  # inserted
            ffmpeg_path = 'ffmpeg/ffmpeg.exe'
            ffprobe_path = 'ffmpeg/ffprobe.exe'
        if not os.path.exists(ffmpeg_path):
            error_msg = f'未找到FFmpeg: {ffmpeg_path}\n请确保_ffmpeg.exe在_internal目录下'
            raise FileNotFoundError(error_msg)
        if not os.path.exists(ffprobe_path):
            error_msg = f'未找到FFprobe: {ffprobe_path}\n请确保_ffprobe.exe在_internal目录下'
            raise FileNotFoundError(error_msg)
        return (ffmpeg_path, ffprobe_path)

    def get_video_metadata(self, input_file):
        """获取视频时长和分辨率"""  # inserted
        _, ffprobe_path = self.get_ffmpeg_path()
        try:
            input_file = self._ensure_unicode_path(input_file)
            cmd = [ffprobe_path, '-v', 'error', '-show_entries', 'stream=width,height,duration', '-of', 'json', input_file]
            result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True, universal_newlines=False)
            try:
                stdout = result.stdout.decode('utf-8', errors='replace')
                metadata = json.loads(stdout)
                stream = metadata['streams'][0]
                width = int(stream.get('width', 1080))
                height = int(stream.get('height', 1920))
                duration = float(stream.get('duration', 2.0))
                return {'width': width, 'height': height, 'duration': duration}
            except (json.JSONDecodeError, IndexError, KeyError, ValueError, UnicodeDecodeError) as e:
                self.progress_signal.emit(0, f'解析视频元数据失败: {str(e)}')
                return {'width': 1080, 'height': 1920, 'duration': 2.0}
        except subprocess.CalledProcessError as e:
            self.progress_signal.emit(0, f'获取视频元数据失败 (错误码 {e.returncode}): {e.stderr.decode()}')
            return {'width': 1080, 'height': 1920, 'duration': 2.0}
        except Exception as e:
            self.progress_signal.emit(0, f'获取视频元数据失败: {str(e)}')
            return {'width': 1080, 'height': 1920, 'duration': 2.0}

    def run_ffmpeg(self, cmd, progress_start, progress_end, total_frames=None):
        """运行FFmpeg命令并跟踪进度"""  # inserted
        ffmpeg_path, _ = self.get_ffmpeg_path()
        cmd[0] = ffmpeg_path
        encoded_cmd = [self._ensure_unicode_path(arg) for arg in cmd]
        self.progress_signal.emit(progress_start, f"开始执行: {' '.join(encoded_cmd[:3])}...")
        try:
            process = subprocess.Popen(encoded_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=False, creationflags=subprocess.CREATE_NO_WINDOW)
            error_output = []
            current_frame = 0
            while True:
                if not self.running:
                    process.terminate()
                    try:
                        process.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        process.kill()
                    break
                
                output = process.stderr.readline()
                if output == b'' and process.poll() is not None:
                    break
                    
                if output:
                    try:
                        decoded_output = output.decode('utf-8', errors='replace').strip()
                        error_output.append(decoded_output)
                    
                        if 'frame=' in decoded_output:
                            frame_info = decoded_output.split('frame=')[1].split('fps=')[0].strip()
                            current_frame = int(frame_info)
                            if total_frames:
                                progress = progress_start + int((progress_end - progress_start) * (current_frame / total_frames))
                                self.progress_signal.emit(progress, f'处理中: 帧 {current_frame}/{total_frames}')
                    except Exception as e:
                        self.progress_signal.emit(0, f'解码输出失败: {str(e)}')
            if process.returncode != 0:
                error_msg = '\n'.join(error_output[(-5):])
                raise subprocess.CalledProcessError(process.returncode, encoded_cmd, error_msg)
            process.stdout.close()
            process.stderr.close()
            return (True, current_frame)
        except subprocess.CalledProcessError as e:
            self.progress_signal.emit(0, f'FFmpeg执行失败 (错误码 {e.returncode}): {e.stderr.decode()}')
            return (False, 0)
        except Exception as e:
            self.progress_signal.emit(0, f'执行FFmpeg命令时出错: {str(e)}')
            return (False, 0)

    def run(self):
        """线程主函数"""  # inserted
        self.running = True
        self.completed_tasks = 0
        try:
            self._prepare_tasks()
            self.progress_signal.emit(10, f'准备处理 {self.total_tasks} 个任务，模式: {self.processing_mode}')
            for video_path, image_path in self._match_files():
                if not self.running:
                    break
                video_name = os.path.basename(video_path)
                image_name = os.path.basename(image_path)
                self.current_task = f'{video_name} + {image_name}'
                self.progress_signal.emit(0, f'\n开始处理任务: {self.current_task}')
                if not os.path.exists(video_path) or not os.path.exists(image_path):
                    raise FileNotFoundError('文件不存在')
                metadata = self.get_video_metadata(video_path)
                self.progress_signal.emit(15, f"视频分辨率: {metadata['width']}x{metadata['height']}, 时长: {metadata['duration']:.2f}秒")
                if self.processing_mode == 'high_quality':
                    self._process_task_high_quality(video_path, image_path, metadata)
                else:
                    self._process_task_normal(video_path, image_path, metadata)
                self.completed_tasks += 1
                self.task_finished_signal.emit(self.current_task)
        except Exception as e:
            self.progress_signal.emit(0, f'处理任务失败: {str(e)}')
            self.task_finished_signal.emit(f'{self.current_task} (失败)')
        finally:
            if self.completed_tasks > 0:
                self.finished_signal.emit(True, f'所有任务处理完成！共处理 {self.completed_tasks} 个任务')
            else:
                self.finished_signal.emit(False, '没有成功处理任何任务')
            self.running = False

    def _prepare_tasks(self):
        """基于文件修改时间排序后自动匹配"""  # inserted
        if not self.video_folder or not self.image_folder:
            raise ValueError('请先选择视频文件夹和图片文件夹')
        videos = self._get_timed_files(self.video_folder, is_video=True)
        images = self._get_timed_files(self.image_folder, is_video=False)
        if not videos:
            raise ValueError('视频文件夹中没有找到有效的视频文件')
        if not images:
            raise ValueError('图片文件夹中没有找到有效的图片文件')
        videos.sort(key=lambda x: x['mtime'])
        images.sort(key=lambda x: x['mtime'])
        min_count = min(len(videos), len(images))
        self._matched_files = [(videos[i]['path'], images[i]['path']) for i in range(min_count)]
        self._match_details = []
        for i, (v, img) in enumerate(self._matched_files):
            if not isinstance(v, dict) or not isinstance(img, dict):
                self.progress_signal.emit(0, f'文件信息格式错误: 视频#{i }或图片#{i }')
                continue
            if 'mtime' not in v or 'mtime' not in img:
                self.progress_signal.emit(0, f'文件时间戳缺失: 视频#{i }或图片#{i }')
                continue
            if 'path' not in v or 'path' not in img:
                self.progress_signal.emit(0, f'文件路径缺失: 视频#{i }或图片#{i }')
                continue
            v_time = self._format_time(v['mtime'])
            img_time = self._format_time(img['mtime'])
            time_diff = abs(v['mtime'] - img['mtime'])
            self._match_details.append(f"匹配 #{i + 1}: 视频 \'{os.path.basename(v['path'])}\' ({v_time}) ↔ 图片 \'{os.path.basename(img['path'])}\' ({img_time}) (时间差: {time_diff:.1f}秒)")
        self.total_tasks = min_count
        self.progress_signal.emit(5, f'找到 {self.total_tasks} 对按时间匹配的文件')
        for detail in self._match_details:
            self.progress_signal.emit(5, detail)
        output_dir = self._ensure_unicode_path(self.output_dir)
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            self.progress_signal.emit(8, f'创建输出目录: {output_dir}')

    def _get_timed_files(self, folder, is_video=False):
        """获取带修改时间的文件列表"""  # inserted
        valid_ext = ('.mp4', '.avi', '.mov') if is_video else ('.jpg', '.jpeg', '.png', '.bmp')
        files = []
        folder = self._ensure_unicode_path(folder)
        try:
            for filename in os.listdir(folder):
                filepath = os.path.join(folder, filename)
                if os.path.isfile(filepath) and filename.lower().endswith(valid_ext):
                    try:
                        mtime = os.path.getmtime(filepath)
                        files.append({'path': filepath, 'mtime': mtime, 'name': filename})
                    except Exception as e:
                        self.progress_signal.emit(0, f'获取文件时间戳失败: {filepath}, 错误: {str(e)}')
        except Exception as e:
            self.progress_signal.emit(0, f'读取文件夹失败: {folder}, 错误: {str(e)}')
        return files

    def _ensure_unicode_path(self, path):
        """确保路径为Unicode编码，支持中文和特殊符号"""  # inserted
        if isinstance(path, bytes):
            return path.decode('utf-8', errors='replace')
        return str(path)

    def _format_time(self, timestamp):
        """格式化时间戳为可读格式"""  # inserted
        import datetime
        return datetime.datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')

    def _match_files(self):
        """生成匹配的视频和图片路径对"""  # inserted
        for video_path, image_path in self._matched_files:
            yield (video_path, image_path)

    def _process_task_normal(self, video_path, image_path, metadata):
        """普通模式：处理单个视频-图片任务（带分辨率适配）"""  # inserted
        video_path = self._ensure_unicode_path(video_path)
        image_path = self._ensure_unicode_path(image_path)
        temp_dir = tempfile.mkdtemp(prefix='video_process_')
        temp_dir = self._ensure_unicode_path(temp_dir)
        try:
            video_name = os.path.splitext(os.path.basename(video_path))[0]
            image_name = os.path.splitext(os.path.basename(image_path))[0]
            output_dir = self._ensure_unicode_path(self.output_dir)
            output_file = os.path.join(output_dir, f'{video_name}_{image_name}.mp4')
            self.progress_signal.emit(20, f'开始处理: {output_file} (普通模式)')
            image_video = os.path.join(temp_dir, 'image_video.mp4')
            success, _ = self.run_ffmpeg(['ffmpeg', '-loop', '1', '-i', image_path, '-t', str(metadata['duration']), '-r', '30', '-s', f"{metadata['width']}x{metadata['height']}", '-preset', 'ultrafast', '-y', image_video], 20, 60)
            if not success:
                raise RuntimeError('图片转视频失败')
            if not self.run_ffmpeg(['ffmpeg', '-i', video_path, '-i', image_video, '-map', '0:v', '-map', '1:v', '-map', '0:a?', '-c:v', 'copy', '-y', output_file], 60, 100):
                raise RuntimeError('视频合并失败')
            self.progress_signal.emit(100, f'任务完成: {output_file}')
        finally:  # inserted
            self._cleanup_temp_dir(temp_dir)

    def _process_task_high_quality(self, video_path, image_path, metadata):
        """高质量模式：处理单个视频-图片任务（带分辨率适配）"""  # inserted
        video_path = self._ensure_unicode_path(video_path)
        image_path = self._ensure_unicode_path(image_path)
        temp_dir = tempfile.mkdtemp(prefix='video_process_')
        temp_dir = self._ensure_unicode_path(temp_dir)
        try:
            video_name = os.path.splitext(os.path.basename(video_path))[0]
            image_name = os.path.splitext(os.path.basename(image_path))[0]
            output_dir = self._ensure_unicode_path(self.output_dir)
            output_file = os.path.join(output_dir, f'{video_name}_{image_name}.mp4')
            self.progress_signal.emit(20, f'开始处理: {output_file} (高质量模式)')
            video_120fps = os.path.join(temp_dir, 'video_120fps.mp4')
            success, _ = self.run_ffmpeg(['ffmpeg', '-i', video_path, '-r', '120', '-s', f"{metadata['width']}x{metadata['height']}", '-c:v', 'libx264', '-preset', 'ultrafast', '-crf', '23', '-c:a', 'aac', '-b:a', '192k', '-y', video_120fps], 20, 40)
            if not success:
                raise RuntimeError('视频转120fps失败')
            image_video = os.path.join(temp_dir, 'image_video.mp4')
            success, _ = self.run_ffmpeg(['ffmpeg', '-loop', '1', '-i', image_path, '-t', str(metadata['duration']), '-r', '120', '-s', f"{metadata['width']}x{metadata['height']}", '-preset', 'ultrafast', '-y', image_video], 40, 60)
            if not success:
                raise RuntimeError('图片转视频失败')
            if not self.run_ffmpeg(['ffmpeg', '-i', video_120fps, '-i', image_video, '-filter_complex', f"[0:v]scale={metadata['width']}:{metadata['height']},setsar=1[v0];[1:v]scale={metadata['width']}:{metadata['height']},setsar=1[v1];[v1]select=\'gte(n,2)*not(mod(n-2,2))\'[a];[v0]select=\'lt(n,2)+gte(n,2)*mod(n-2,2)\'[b];[a][b]interleave[v]", '-map', '[v]', '-map', '0:a?', '-c:v', 'mpeg4', '-preset', 'ultrafast', '-pix_fmt', 'yuv420p', '-q:v', '6', '-r', '120', '-b:v', '8000k', '-y', output_file], 60, 100):
                raise RuntimeError('视频合并失败')
            self.progress_signal.emit(100, f'任务完成: {output_file}')
        finally:  # inserted
            self._cleanup_temp_dir(temp_dir)

    def _cleanup_temp_dir(self, temp_dir):
        """安全清理临时目录，添加重试机制"""  # inserted
        max_retries = 3
        for attempt in range(max_retries):
            try:
                temp_dir = self._ensure_unicode_path(temp_dir)
                if os.path.exists(temp_dir):
                    for root, dirs, files in os.walk(temp_dir, topdown=False):
                        for name in files:
                            file_path = os.path.join(root, name)
                            file_path = self._ensure_unicode_path(file_path)
                            os.remove(file_path)
                    os.rmdir(temp_dir)
                    return True
            except Exception as e:
                self.progress_signal.emit(0, f'清理临时文件失败 (尝试 {attempt+1}/{max_retries}): {str(e)}')
                import time
                time.sleep(1.0)
        self.progress_signal.emit(0, f'无法清理临时目录: {temp_dir}')
        return False

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('处理器')
        self.setGeometry(100, 100, 680, 420)
        self.setStyleSheet('background-color: #f8f9fa;')
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(18)
        title = QLabel('<h3 style=\'color: #2c3e50;\'>处理器</h3>')
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        mode_frame = QFrame()
        mode_frame.setStyleSheet('background: white; border-radius: 8px; padding: 10px;')
        mode_layout = QHBoxLayout(mode_frame)
        mode_layout.addWidget(QLabel('处理模式:', styleSheet='font-size: 14px;'))
        self.mode_combo = QComboBox()
        self.mode_combo.addItems(['普通模式', '高质量模式高清版'])
        self.mode_combo.setStyleSheet('\n            QComboBox {\n                border: 1px solid #ddd;\n                border-radius: 4px;\n                padding: 6px 12px;\n                font-size: 14px;\n                min-width: 150px;\n            }\n        ')
        self.mode_combo.currentTextChanged.connect(self.on_mode_changed)
        mode_layout.addWidget(self.mode_combo)
        self.mode_info = QLabel('普通模式：快速处理，保留原始帧率')
        self.mode_info.setStyleSheet('font-size: 12px; color: #666;')
        self.mode_info.setAlignment(Qt.AlignCenter)
        mode_layout.addWidget(self.mode_info, 1)
        layout.addWidget(mode_frame)
        folder_frame = QFrame()
        folder_frame.setStyleSheet('background: white; border-radius: 8px; padding: 15px;')
        folder_layout = QHBoxLayout(folder_frame)
        self.video_btn = QPushButton('选择视频文件夹')
        self.video_btn.setFixedSize(160, 40)
        self.video_btn.setStyleSheet('\n            QPushButton {\n                background-color: #3498db;\n                color: white;\n                border: none;\n                border-radius: 4px;\n                font-size: 14px;\n            }\n            QPushButton:hover { background-color: #2980b9; }\n        ')
        self.video_btn.clicked.connect(self.select_video_folder)
        self.video_path = QLabel('未选择')
        self.video_path.setStyleSheet('\n            border: 1px solid #ddd;\n            border-radius: 4px;\n            padding: 6px 12px;\n            font-size: 13px;\n            color: #333;\n        ')
        self.video_path.setMinimumHeight(40)
        self.video_path.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.image_btn = QPushButton('选择图片文件夹')
        self.image_btn.setFixedSize(160, 40)
        self.image_btn.setStyleSheet('\n            QPushButton {\n                background-color: #2ecc71;\n                color: white;\n                border: none;\n                border-radius: 4px;\n                font-size: 14px;\n            }\n            QPushButton:hover { background-color: #27ae60; }\n        ')
        self.image_btn.clicked.connect(self.select_image_folder)
        self.image_path = QLabel('未选择')
        self.image_path.setStyleSheet(self.video_path.styleSheet())
        self.image_path.setMinimumHeight(40)
        self.image_path.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        folder_layout.addWidget(self.video_btn)
        folder_layout.addWidget(self.video_path, 1)
        folder_layout.addWidget(self.image_btn)
        folder_layout.addWidget(self.image_path, 1)
        layout.addWidget(folder_frame)
        self.progress_bar = QProgressBar()
        self.progress_bar.setStyleSheet('\n            QProgressBar {\n                height: 6px;\n                border-radius: 3px;\n                background: #eee;\n            }\n            QProgressBar::chunk {\n                background-color: #3498db;\n                border-radius: 2px;\n                width: 20px;\n            }\n        ')
        self.progress_bar.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.progress_bar)
        log_frame = QFrame()
        log_frame.setStyleSheet('background: white; border-radius: 8px; padding: 12px;')
        self.log_output = QTextEdit()
        self.log_output.setReadOnly(True)
        self.log_output.setStyleSheet('\n            border: none;\n            font-size: 12px;\n            color: #555;\n        ')
        self.log_output.setMaximumHeight(120)
        log_layout = QVBoxLayout(log_frame)
        log_layout.addWidget(QLabel('<b>处理日志</b>', styleSheet='color: #333; font-size: 14px;'))
        log_layout.addWidget(self.log_output)
        layout.addWidget(log_frame)
        btn_frame = QFrame()
        btn_frame.setStyleSheet('background: white; border-radius: 8px; padding: 15px;')
        btn_layout = QHBoxLayout(btn_frame)
        self.start_btn = QPushButton('开始处理')
        self.start_btn.setFixedSize(120, 40)
        self.start_btn.setStyleSheet('\n            QPushButton {\n                background-color: #e74c3c;\n                color: white;\n                border: none;\n                border-radius: 4px;\n                font-size: 14px;\n            }\n            QPushButton:hover { background-color: #c0392b; }\n        ')
        self.start_btn.clicked.connect(self.start_processing)
        self.stop_btn = QPushButton('停止处理')
        self.stop_btn.setFixedSize(120, 40)
        self.stop_btn.setStyleSheet('\n            QPushButton {\n                background-color: #9b59b6;\n                color: white;\n                border: none;\n                border-radius: 4px;\n                font-size: 14px;\n                margin-left: 20px;\n            }\n            QPushButton:hover { background-color: #8e44ad; }\n        ')
        self.stop_btn.setEnabled(False)
        self.stop_btn.clicked.connect(self.stop_processing)
        self.clear_btn = QPushButton('清空日志')
        self.clear_btn.setFixedSize(120, 40)
        self.clear_btn.setStyleSheet('\n            QPushButton {\n                background-color: #34495e;\n                color: white;\n                border: none;\n                border-radius: 4px;\n                font-size: 14px;\n                margin-left: 20px;\n            }\n            QPushButton:hover { background-color: #2c3e50; }\n        ')
        self.clear_btn.clicked.connect(self.clear_log)
        btn_layout.addWidget(self.start_btn)
        btn_layout.addWidget(self.stop_btn)
        btn_layout.addWidget(self.clear_btn)
        layout.addWidget(btn_frame)
        self.processor = VideoProcessor()
        self.processor.progress_signal.connect(self.update_progress)
        self.processor.finished_signal.connect(self.processing_finished)
        self.processor.task_finished_signal.connect(self.task_finished)
        self.on_mode_changed(self.mode_combo.currentText())

    def on_mode_changed(self, mode_text):
        """模式变更时更新信息"""  # inserted
        if mode_text == '普通模式':
            self.processor.set_processing_mode('normal')
            self.mode_info.setText('普通模式：快速处理')
        else:  # inserted
            self.processor.set_processing_mode('high_quality')
            self.mode_info.setText('高质量模式备用方案：处理时间较长谨慎使用')

    def select_video_folder(self):
        folder_path = QFileDialog.getExistingDirectory(self, '选择视频文件夹')
        if folder_path:
            folder_path = self._ensure_unicode_path(folder_path)
            self.processor.video_folder = folder_path
            self.video_path.setText(folder_path)
            self.log_output.append(f'已选择视频文件夹: {folder_path}')

    def select_image_folder(self):
        folder_path = QFileDialog.getExistingDirectory(self, '选择图片文件夹')
        if folder_path:
            folder_path = self._ensure_unicode_path(folder_path)
            self.processor.image_folder = folder_path
            self.image_path.setText(folder_path)
            self.log_output.append(f'已选择图片文件夹: {folder_path}')

    def _ensure_unicode_path(self, path):
        """确保路径为Unicode编码，支持中文和特殊符号"""  # inserted
        if isinstance(path, bytes):
            return path.decode('utf-8', errors='replace')
        return str(path)

    def start_processing(self):
        """开始处理视频和图片"""  # inserted
        if not self.processor.video_folder:
            QMessageBox.warning(self, '警告', '请先选择视频文件夹！')
            return
        if not self.processor.image_folder:
            QMessageBox.warning(self, '警告', '请先选择图片文件夹！')
            return
        try:
            videos = self.processor._get_timed_files(self.processor.video_folder, is_video=True)
            images = self.processor._get_timed_files(self.processor.image_folder, is_video=False)
            if not videos:
                QMessageBox.warning(self, '警告', '视频文件夹中无有效视频文件！')
                return
            if not images:
                QMessageBox.warning(self, '警告', '图片文件夹中无有效图片文件！')
                return
            videos.sort(key=lambda x: x['mtime'])
            images.sort(key=lambda x: x['mtime'])
            min_count = min(len(videos), len(images))
            preview = []
            for i in range(min(min_count, 10)):
                v = videos[i]
                img = images[i]
                time_diff = abs(v['mtime'] - img['mtime'])
                preview.append(f"{i+1}. 视频: {os.path.basename(v['path'])} ↔ 图片: {os.path.basename(img['path'])} (时间差: {time_diff:.1f}秒)")
            preview_text = '\n'.join(preview)
            if min_count > 10:
                preview_text += f'\n... 共 {min_count} 对匹配文件'
            current_mode = self.mode_combo.currentText()
            reply = QMessageBox.question(self, f'确认匹配 - {current_mode}', f'即将按修改时间处理 {min_count} 对文件\n\n匹配预览:\n{preview_text}\n\n结果将保存到output文件夹', QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
            if reply == QMessageBox.Yes:
                self.log_output.clear()
                self.log_output.append(f'找到 {min_count} 对按时间匹配的文件，开始处理...')
                self.progress_bar.setValue(0)
                self.start_btn.setEnabled(False)
                self.stop_btn.setEnabled(True)
                self.processor.start()
        except Exception as e:
            QMessageBox.critical(self, '错误', f'准备处理时出错: {str(e)}')
            self.log_output.append(f'错误: {str(e)}')

    def stop_processing(self):
        """停止处理"""  # inserted
        reply = QMessageBox.question(self, '确认', '确定要停止处理吗？当前任务可能会中断。', QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.Yes:
            self.log_output.append('正在停止处理...')
            self.processor.running = False

    def update_progress(self, value, message):
        """更新进度条和日志"""  # inserted
        self.progress_bar.setValue(value)
        self.progress_bar.setFormat(f'{message}')
        self.log_output.append(message)

    def task_finished(self, task_name):
        """单个任务完成"""  # inserted
        self.log_output.append(f'任务完成: {task_name}')

    def processing_finished(self, success, message):
        """所有处理完成"""  # inserted
        self.log_output.append(message)
        self.progress_bar.setValue(100 if success else 0)
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        if success:
            QMessageBox.information(self, '完成', f'所有匹配任务处理完成！共生成 {self.processor.completed_tasks} 个文件')
        else:  # inserted
            QMessageBox.warning(self, '处理中断', message)

    def clear_log(self):
        """清空日志"""  # inserted
        self.log_output.clear()
if __name__ == '__main__':
    try:
        import locale
        locale.setlocale(locale.LC_ALL, '')
        import io
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')
    except Exception as e:
        print(f'设置系统编码失败: {str(e)}')
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())