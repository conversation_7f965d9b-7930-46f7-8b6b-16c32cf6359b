from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QPushButton, QLabel, QLineEdit, 
                            QFileDialog, QMessageBox, QProgressBar)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QIcon
import sys
import subprocess
import os

class VideoProcessorThread(QThread):
    progress_updated = pyqtSignal(str)
    processing_finished = pyqtSignal(bool, str)
    
    def __init__(self, ffmpeg_cmd):
        super().__init__()
        self.ffmpeg_cmd = ffmpeg_cmd
        
    def run(self):
        try:
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE
            
            process = subprocess.Popen(
                self.ffmpeg_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                encoding='utf-8',
                errors='ignore',
                startupinfo=startupinfo,
                creationflags=subprocess.CREATE_NO_WINDOW
            )
            
            for line in process.stdout:
                if "frame=" in line:
                    self.progress_updated.emit(line.strip())
            
            process.wait()
            self.processing_finished.emit(
                process.returncode == 0,
                "处理完成" if process.returncode == 0 else "处理失败"
            )
        except Exception as e:
            self.processing_finished.emit(False, f"处理错误: {str(e)}")

class VideoProcessorGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.processing_thread = None
        self.current_process = None
        self.ffmpeg_cmd1 = None
        self.ffmpeg_cmd2 = None
        self.ffmpeg_cmd3 = None
        self.ffmpeg_cmd4 = None
        self.current_stage = 0
        self.total_stages = 2  # 两阶段处理
        
        # 设置窗口标题和大小
        self.setWindowTitle("羊鞭视频批量处理工具")
        self.setGeometry(100, 100, 800, 600)
        
        # 设置窗口图标
        if os.path.exists("icon.png"):
            self.setWindowIcon(QIcon("icon.png"))
        
        # 应用样式表
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QLabel {
                font-size: 14px;
            }
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QLineEdit {
                padding: 5px;
                border: 1px solid #ddd;
                border-radius: 4px;
            }
            QStatusBar {
                background-color: #e0e0e0;
                color: #333;
            }
        """)
        
        # 创建主控件
        self.main_widget = QWidget()
        self.main_widget.setStyleSheet("background-color: white; border-radius: 8px; padding: 20px;")
        self.setCentralWidget(self.main_widget)
        
        # 主布局
        self.main_layout = QVBoxLayout()
        self.main_widget.setLayout(self.main_layout)
        
        # 标题
        title_label = QLabel("羊鞭3.0视频处理工具")
        title_label.setStyleSheet("""
            font-size: 24px; 
            font-weight: bold;
            color: #333;
            padding-bottom: 15px;
        """)
        title_label.setAlignment(Qt.AlignCenter)
        self.main_layout.addWidget(title_label)
        self.main_layout.addSpacing(20)
        
        # 主视频文件夹选择
        self.create_folder_selection("主视频文件夹:", "main_video_folder")
        
        # 辅助视频文件夹选择
        self.create_folder_selection("辅助视频文件夹:", "aux_video_folder")
        
        # 输出文件夹选择
        self.create_folder_selection("输出文件夹:", "output_folder", is_output=True)
        
        # 处理按钮
        process_btn = QPushButton("开始处理视频")
        process_btn.setStyleSheet("""
            font-size: 16px; 
            background-color: #2196F3;
        """)
        process_btn.clicked.connect(self.process_videos)
        self.main_layout.addSpacing(20)
        self.main_layout.addWidget(process_btn, alignment=Qt.AlignCenter)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #ddd;
                border-radius: 5px;
                text-align: center;
                height: 20px;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                width: 10px;
            }
        """)
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.main_layout.addSpacing(10)
        self.main_layout.addWidget(self.progress_bar)
        
        # 状态栏
        self.statusBar().showMessage("就绪")
    
    def create_folder_selection(self, label_text, field_name, is_output=False):
        """创建文件夹选择控件组"""
        container = QWidget()
        container.setStyleSheet("background-color: #f9f9f9; border-radius: 6px; padding: 8px;")
        layout = QHBoxLayout(container)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(10)
        
        # 标签
        label = QLabel(label_text)
        label.setFixedWidth(120)
        label.setStyleSheet("font-weight: bold; color: #555;")
        layout.addWidget(label)
        
        # 文件夹路径显示
        line_edit = QLineEdit()
        line_edit.setReadOnly(True)
        line_edit.setStyleSheet("""
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 6px;
        """)
        setattr(self, f"{field_name}_path", line_edit)
        layout.addWidget(line_edit, stretch=1)
        
        # 浏览按钮
        btn = QPushButton("浏览...")
        btn.setStyleSheet("""
            QPushButton {
                background-color: #607D8B;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                min-width: 60px;
            }
            QPushButton:hover {
                background-color: #546E7A;
            }
            QPushButton:pressed {
                background-color: #455A64;
            }
        """)
        if is_output:
            btn.clicked.connect(lambda: self.select_output_folder(line_edit))
        else:
            btn.clicked.connect(lambda: self.select_input_folder(line_edit))
        layout.addWidget(btn)
        
        # 添加间距
        self.main_layout.addSpacing(10)
        
        self.main_layout.addWidget(container)
    
    def select_input_folder(self, line_edit):
        """选择输入文件夹"""
        folder_path = QFileDialog.getExistingDirectory(self, "选择文件夹")
        if folder_path:
            line_edit.setText(folder_path)
    
    def select_output_folder(self, line_edit):
        """选择输出文件夹"""
        folder_path = QFileDialog.getExistingDirectory(self, "选择文件夹")
        if folder_path:
            line_edit.setText(folder_path)
    
    def get_video_resolution(self, video_path):
        """使用ffprobe获取视频分辨率"""
        try:
            cmd = [
                "ffprobe.exe",
                "-v", "error",
                "-select_streams", "v:0",
                "-show_entries", "stream=width,height",
                "-of", "csv=s=x:p=0",
                video_path
            ]
            result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, encoding='utf-8', errors='ignore')
            return result.stdout.strip()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"获取视频分辨率失败: {str(e)}")
            return None

    def get_video_duration(self, video_path):
        """使用ffprobe获取视频时长(秒)"""
        try:
            cmd = [
                "ffprobe.exe",
                "-v", "error",
                "-show_entries", "format=duration",
                "-of", "default=noprint_wrappers=1:nokey=1",
                video_path
            ]
            result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, encoding='utf-8', errors='ignore')
            return float(result.stdout.strip())
        except Exception as e:
            QMessageBox.critical(self, "错误", 
                f"获取视频时长失败:\n{str(e)}\n"
                "请确保:\n"
                "1. ffprobe.exe与程序放在同一目录\n"
                "2. 视频文件路径正确\n"
                "3. 有足够的文件访问权限")
            return None

    def get_video_files(self, folder_path):
        """获取文件夹中的视频文件列表"""
        if not folder_path:
            return []
            
        video_exts = ('.mp4', '.avi', '.mov')
        return sorted([
            os.path.join(folder_path, f) 
            for f in os.listdir(folder_path) 
            if f.lower().endswith(video_exts)
        ])
    

        
    def process_videos(self):
        """批量处理视频"""
        if self.processing_thread and self.processing_thread.isRunning():
            QMessageBox.warning(self, "警告", "已有处理任务在进行中!")
            return
            
        main_folder = self.main_video_folder_path.text()
        aux_folder = self.aux_video_folder_path.text()
        output_folder = self.output_folder_path.text()
        
        if not all([main_folder, aux_folder, output_folder]):
            QMessageBox.warning(self, "警告", "请先选择所有必要的文件夹!")
            return
            
        # 获取文件列表
        main_files = self.get_video_files(main_folder)
        aux_files = self.get_video_files(aux_folder)
        
        if not main_files or not aux_files:
            QMessageBox.warning(self, "警告", "文件夹中没有找到视频文件!")
            return
            
        # 创建输出文件夹
        import os
        os.makedirs(output_folder, exist_ok=True)
        
        # 处理文件对
        self.total_pairs = min(len(main_files), len(aux_files))
        self.processed_pairs = 0
        self.main_files = main_files
        self.aux_files = aux_files
        self.output_folder = output_folder
        
        self.process_next_pair()
        
    def process_next_pair(self):
        if self.processed_pairs >= self.total_pairs:
            QMessageBox.information(self, "完成", f"批量处理已完成!\n共处理了 {self.total_pairs} 对视频")
            self.statusBar().showMessage("就绪")
            self.progress_bar.setValue(100)
            self.current_stage = 0
            return
        
        # 重置进度
        self.progress_bar.setValue(0)
        self.current_stage = 0
            
        main_video = self.main_files[self.processed_pairs]
        aux_video = self.aux_files[self.processed_pairs]
        
        # 自动生成输出文件名
        import os
        main_name = os.path.splitext(os.path.basename(main_video))[0]
        aux_name = os.path.splitext(os.path.basename(aux_video))[0]
        output_file = os.path.join(
            self.output_folder, 
            f"{main_name}_+_{aux_name}.mp4"
        )
        
        self.statusBar().showMessage(f"准备处理 {self.processed_pairs+1}/{self.total_pairs}: {os.path.basename(main_video)}")
        
        try:
            # 获取主视频信息
            duration = self.get_video_duration(main_video)
            if duration is None:
                self.processed_pairs += 1
                self.process_next_pair()
                return
                
            resolution = self.get_video_resolution(main_video)
            if not resolution:
                self.processed_pairs += 1
                self.process_next_pair()
                return
            
            import tempfile
            temp_dir = tempfile.mkdtemp()
            temp_file = os.path.join(temp_dir, f"temp_3d_{os.path.basename(main_video)}")
            
            # 第一阶段：视频处理和混合
            temp_output = os.path.join(temp_dir, f"temp_3d_{os.path.basename(main_video)}")
            self.ffmpeg_cmd1 = [
                "ffmpeg.exe",
                "-y",
                "-i", main_video,
                "-stream_loop", "-1",
                "-i", aux_video,
                "-filter_complex",
                f"[1:v]fps=30,trim=duration={duration}[v1p];"
                f"[0:v]fps=30[v0p];"
                f"[v1p][v0p]scale2ref[v1s][v0s];"
                f"[v1s]setsar=1[v1f];"
                f"[v0s][v1f]interleave[v]",
                "-map", "[v]",
                "-map", "0:a?",
                "-c:v", "libx264",
                "-crf", "20",
                "-profile:v", "main",
                "-level", "4.0",
                "-c:a", "copy",
                "-metadata:s:v:0", "stereo_mode=frame_seq",
                temp_output
            ]
            
            # 第二阶段：最终输出处理
            self.ffmpeg_cmd2 = [
                "ffmpeg.exe",
                "-y",
                "-i", temp_output,
                "-i", aux_video,
                "-map", "0",
                "-map", "1:v",
                "-c", "copy",
                "-disposition:v:0", "default",
                "-disposition:v:1", "timed_thumbnails",
                output_file
            ]
            
            # 执行两阶段处理
            self.execute_stage(self.ffmpeg_cmd1, "视频混合处理",
                             lambda success, msg: self.on_stage_finished(success, msg, self.ffmpeg_cmd2, "最终输出处理", output_file, temp_dir))
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"处理过程中发生错误:\n{str(e)}")
            self.processed_pairs += 1
            self.process_next_pair()
    
    def update_progress(self, progress_info):
        """更新进度条"""
        if "frame=" in progress_info:
            try:
                frame = int(progress_info.split("frame=")[1].split()[0])
                self.progress_bar.setValue(min(frame, 100))  # 直接使用帧数作为进度
            except:
                pass
        
    def execute_stage(self, cmd, stage_name, on_finished):
        """执行处理阶段"""
        self.current_stage += 1
        self.progress_bar.setValue(int((self.current_stage-1)/self.total_stages*100))
        
        self.processing_thread = VideoProcessorThread(cmd)
        self.processing_thread.progress_updated.connect(
            lambda msg: (
                self.statusBar().showMessage(
                    f"{stage_name}中 {self.processed_pairs+1}/{self.total_pairs}: {msg}"
                ),
                self.update_progress(msg)
            )
        )
        self.processing_thread.processing_finished.connect(on_finished)
        self.processing_thread.start()
    
    def on_stage_finished(self, success, message, next_cmd, next_stage, output_file, temp_dir):
        """处理阶段完成回调"""
        if not success:
            QMessageBox.warning(self, "警告", f"{message}")
            self.processed_pairs += 1
            self.process_next_pair()
            return
            
        if next_cmd:
            self.execute_stage(next_cmd, next_stage,
                             lambda success, msg: self.on_stage_finished(
                                 success, msg, 
                                 self.ffmpeg_cmd3 if next_stage == "处理主视频" else self.ffmpeg_cmd4 if next_stage == "AB交替混合处理" else None,
                                 "AB交替混合处理" if next_stage == "处理主视频" else "双轨道合成" if next_stage == "AB交替混合处理" else None,
                                 output_file, temp_dir))
        else:
            # 所有阶段完成
            self.processed_pairs += 1
            self.process_next_pair()
    
    def on_stage2_finished(self, success, message, temp_dir):
        import shutil
        try:
            shutil.rmtree(temp_dir)  # 清理临时文件
        except Exception as e:
            print(f"清理临时文件失败: {str(e)}")
            
        if not success:
            QMessageBox.warning(self, "警告", f"第二阶段处理失败: {message}")
            
        self.processed_pairs += 1
        self.process_next_pair()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = VideoProcessorGUI()
    window.show()
    sys.exit(app.exec_())
