import tkinter as tk
from tkinter import ttk
from tkinter import filedialog, messagebox
import subprocess
import os
import shutil
import threading
import time
import sys
import traceback
import json
import math
from datetime import datetime

class MultiTrackVideoProcessor:
    # 命令执行超时时间(秒)
    TIMEOUT = 300
    
    def __init__(self, root):
        self.root = root
        self.root.title("小鸡快跑视频处理器")
        self.ffmpeg_path = self.find_ffmpeg()
        
        # UI布局
        row = 0
        
        # FFmpeg路径配置
        tk.Label(root, text="FFmpeg路径:").grid(row=row, column=0, padx=5, pady=5)
        self.ffmpeg_entry = tk.Entry(root, width=40)
        self.ffmpeg_entry.grid(row=row, column=1, padx=5, pady=5)
        self.ffmpeg_entry.insert(0, self.ffmpeg_path if self.ffmpeg_path else "未找到FFmpeg")
        tk.Button(root, text="设置路径", command=self.set_ffmpeg_path).grid(row=row, column=2, padx=5, pady=5)
        row += 1

        # 批量处理模式选择
        self.batch_mode = tk.BooleanVar(value=False)
        tk.Checkbutton(root, text="批量处理模式", variable=self.batch_mode, command=self.toggle_batch_mode).grid(row=row, column=0, columnspan=3, pady=5)
        row += 1

        # 主视频/主视频文件夹
        self.main_label = tk.Label(root, text="主视频:")
        self.main_label.grid(row=row, column=0, padx=5, pady=5)
        self.main_video_entry = tk.Entry(root, width=40)
        self.main_video_entry.grid(row=row, column=1, padx=5, pady=5)
        self.main_browse_btn = tk.Button(root, text="浏览", command=lambda: self.select_video_or_folder("main"))
        self.main_browse_btn.grid(row=row, column=2, padx=5, pady=5)
        row += 1

        # 辅助视频/辅助视频文件夹
        self.aux_label = tk.Label(root, text="辅助视频:")
        self.aux_label.grid(row=row, column=0, padx=5, pady=5)
        self.aux_video_entry = tk.Entry(root, width=40)
        self.aux_video_entry.grid(row=row, column=1, padx=5, pady=5)
        self.aux_browse_btn = tk.Button(root, text="浏览", command=lambda: self.select_video_or_folder("aux"))
        self.aux_browse_btn.grid(row=row, column=2, padx=5, pady=5)
        row += 1

        # 输出文件夹
        self.output_label = tk.Label(root, text="输出文件夹:")
        self.output_label.grid(row=row, column=0, padx=5, pady=5)
        self.output_entry = tk.Entry(root, width=40)
        self.output_entry.grid(row=row, column=1, padx=5, pady=5)
        self.output_browse_btn = tk.Button(root, text="浏览", command=self.select_output_folder)
        self.output_browse_btn.grid(row=row, column=2, padx=5, pady=5)
        row += 1

        # 进度显示
        self.progress_var = tk.StringVar()
        self.progress_var.set("准备就绪")
        tk.Label(root, textvariable=self.progress_var).grid(row=row, column=0, columnspan=3)
        row += 1

        # 进度条
        self.progress_bar = ttk.Progressbar(root, orient="horizontal", length=300, mode="determinate")
        self.progress_bar.grid(row=row, column=0, columnspan=3, pady=5)
        row += 1

        # 百分比显示
        self.percent_var = tk.StringVar()
        self.percent_var.set("0%")
        tk.Label(root, textvariable=self.percent_var).grid(row=row, column=1, pady=5)
        row += 1

        # 处理按钮
        self.process_btn = tk.Button(root, text="开始处理", command=self.process_video)
        self.process_btn.grid(row=row, column=1, pady=10)
        row += 1

        # 状态显示
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        tk.Label(root, textvariable=self.status_var).grid(row=row, column=0, columnspan=3)

    def find_ffmpeg(self):
        """查找FFmpeg和ffprobe路径，优先检查当前目录"""
        # 获取当前可执行文件所在目录
        current_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
        
        # 优先检查当前目录下的ffmpeg/ffprobe
        local_ffmpeg = os.path.join(current_dir, 'ffmpeg.exe')
        local_ffprobe = os.path.join(current_dir, 'ffprobe.exe')
        
        if os.path.exists(local_ffmpeg):
            ffmpeg_path = local_ffmpeg
            if os.path.exists(local_ffprobe):
                return ffmpeg_path, local_ffprobe
            else:
                # 当前目录只有ffmpeg没有ffprobe
                messagebox.showwarning("FFprobe缺失", 
                    f"在当前目录找到ffmpeg但未找到ffprobe:\n{local_ffmpeg}\n\n"
                    "请确保两个文件都在同一目录")
                return ffmpeg_path, None
        
        # 如果当前目录没有，检查系统PATH
        try:
            # 查找系统PATH中的ffmpeg
            result = subprocess.run(['ffmpeg', '-version'], capture_output=True, text=True)
            if result.returncode == 0:
                ffmpeg_path = 'ffmpeg'
                # 尝试查找配套的ffprobe
                try:
                    subprocess.run(['ffprobe', '-version'], capture_output=True, check=True)
                    return ffmpeg_path, 'ffprobe'
                except:
                    # 如果ffprobe不存在，尝试从ffmpeg路径推断
                    if os.path.exists(ffmpeg_path):
                        ffprobe_path = ffmpeg_path.replace('ffmpeg', 'ffprobe')
                        if os.path.exists(ffprobe_path):
                            return ffmpeg_path, ffprobe_path
                return ffmpeg_path, None
        except FileNotFoundError:
            pass
        
        # 都没有找到
        messagebox.showerror("FFmpeg缺失", 
            "未找到FFmpeg可执行文件\n\n"
            "请将ffmpeg.exe和ffprobe.exe放在:\n"
            f"1. 当前程序目录: {current_dir}\n"
            "2. 或系统PATH路径中")
        return None, None

    def set_ffmpeg_path(self):
        """设置FFmpeg和ffprobe路径"""
        filepath = filedialog.askopenfilename(title="选择FFmpeg可执行文件")
        if filepath:
            self.ffmpeg_path = filepath
            self.ffmpeg_entry.delete(0, tk.END)
            self.ffmpeg_entry.insert(0, filepath)
            
            # 自动设置ffprobe路径
            ffprobe_path = filepath.replace('ffmpeg', 'ffprobe')
            if os.path.exists(ffprobe_path):
                self.ffprobe_path = ffprobe_path
            else:
                # 如果自动推断失败，让用户手动选择
                choice = messagebox.askyesno("FFprobe路径", 
                    "未能自动找到ffprobe可执行文件，是否要手动指定？\n\n"
                    "说明：\n"
                    "1. ffmpeg用于视频处理\n"
                    "2. ffprobe用于获取视频信息(如时长)\n"
                    "3. 两个工具通常在同一目录")
                if choice:
                    ffprobe_path = filedialog.askopenfilename(title="选择ffprobe可执行文件")
                    if ffprobe_path:
                        self.ffprobe_path = ffprobe_path

    def toggle_batch_mode(self):
        """切换批量处理模式"""
        if self.batch_mode.get():
            self.main_label.config(text="主视频文件夹:")
            self.aux_label.config(text="辅助视频文件夹:")
            self.output_label.config(text="输出文件夹:")
        else:
            self.main_label.config(text="主视频:")
            self.aux_label.config(text="辅助视频:")
            self.output_label.config(text="输出视频:")

    def select_video_or_folder(self, video_type):
        """选择视频文件或文件夹"""
        if self.batch_mode.get():
            # 批量模式选择文件夹
            folderpath = filedialog.askdirectory()
            if folderpath:
                if video_type == "main":
                    self.main_video_entry.delete(0, tk.END)
                    self.main_video_entry.insert(0, folderpath)
                else:
                    self.aux_video_entry.delete(0, tk.END)
                    self.aux_video_entry.insert(0, folderpath)
        else:
            # 单文件模式选择文件
            filepath = filedialog.askopenfilename(filetypes=[("视频文件", "*.mp4 *.avi *.mov")])
            if filepath:
                if video_type == "main":
                    self.main_video_entry.delete(0, tk.END)
                    self.main_video_entry.insert(0, filepath)
                    # 自动设置输出路径
                    if not self.output_entry.get():
                        dirname = os.path.dirname(filepath)
                        filename, ext = os.path.splitext(os.path.basename(filepath))
                        output_path = os.path.join(dirname, f"{filename}_processed{ext}")
                        self.output_entry.delete(0, tk.END)
                        self.output_entry.insert(0, output_path)
                else:
                    self.aux_video_entry.delete(0, tk.END)
                    self.aux_video_entry.insert(0, filepath)

    def select_output_folder(self):
        """选择输出文件夹"""
        if self.batch_mode.get():
            folderpath = filedialog.askdirectory()
            if folderpath:
                self.output_entry.delete(0, tk.END)
                self.output_entry.insert(0, folderpath)
        else:
            filepath = filedialog.asksaveasfilename(
                defaultextension=".mp4",
                filetypes=[("MP4文件", "*.mp4")]
            )
            if filepath:
                self.output_entry.delete(0, tk.END)
                self.output_entry.insert(0, filepath)

    def check_ffmpeg(self):
        """检查FFmpeg是否可用"""
        if not self.ffmpeg_path:
            return False
            
        # 检查文件是否存在
        if not os.path.isfile(self.ffmpeg_path):
            return False
            
        # 检查是否为可执行文件
        try:
            # 验证是否为PE文件(Windows可执行文件)
            with open(self.ffmpeg_path, 'rb') as f:
                if f.read(2) != b'MZ':  # DOS头标识
                    return False
                    
            # 执行版本检查
            result = subprocess.run(
                [self.ffmpeg_path, '-version'],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                creationflags=subprocess.CREATE_NO_WINDOW
            )
            return result.returncode == 0
            
        except subprocess.CalledProcessError:
            return False
        except OSError as e:
            # 记录详细的错误信息
            error_log = os.path.join(os.path.dirname(self.ffmpeg_path), "ffmpeg_error.log")
            with open(error_log, 'a') as f:
                f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - 执行错误: {str(e)}\n")
                f.write(f"FFmpeg路径: {self.ffmpeg_path}\n")
                f.write(f"错误详情: {traceback.format_exc()}\n")
            return False
        except Exception:
            return False

    def show_error(self, message):
        """安全显示错误消息，处理各种异常情况"""
        self.status_var.set("处理失败")
        try:
            # 统一转换为字符串
            if message is None:
                error_msg = "发生未知错误"
            elif isinstance(message, Exception):
                error_msg = f"{type(message).__name__}: {str(message)}"
            else:
                # 处理元组、列表等可迭代对象
                if isinstance(message, (tuple, list)):
                    parts = []
                    for item in message:
                        try:
                            parts.append(str(item))
                        except:
                            parts.append(repr(item))
                    error_msg = " ".join(parts)
                else:
                    try:
                        error_msg = str(message)
                    except:
                        error_msg = repr(message)
            
            # 确保最终是字符串
            if not isinstance(error_msg, str):
                error_msg = str(error_msg)
                
            # 限制错误消息长度
            error_msg = error_msg[:500] + "..." if len(error_msg) > 500 else error_msg
            messagebox.showerror("错误", error_msg)
        except Exception as e:
            # 终极fallback
            messagebox.showerror("错误", "发生未知错误")
        finally:
            self.process_btn.config(state=tk.NORMAL)

    def _safe_str(self, obj):
        """安全转换为字符串"""
        try:
            return str(obj)
        except:
            return repr(obj)

    def process_video(self):
        """启动视频处理线程"""
        if not self.check_inputs():
            return
            
        self.process_btn.config(state=tk.DISABLED)
        self.status_var.set("准备处理...")
        threading.Thread(target=self._process_videos_thread, daemon=True).start()

    def check_inputs(self):
        """验证输入参数"""
        try:
            main_input = self.main_video_entry.get()
            aux_input = self.aux_video_entry.get()
            output_path = self.output_entry.get()

            if not all([main_input, aux_input, output_path]):
                self.show_error("请填写所有必填字段")
                return False
                
            # 验证主视频文件
            if not os.path.exists(main_input):
                self.show_error(f"主视频文件不存在: {main_input}")
                return False
            if not os.access(main_input, os.R_OK):
                self.show_error(f"无法读取主视频文件(权限不足): {main_input}")
                return False
                
            # 验证辅助视频文件
            if not os.path.exists(aux_input):
                self.show_error(f"辅助视频文件不存在: {aux_input}")
                return False
            if not os.access(aux_input, os.R_OK):
                self.show_error(f"无法读取辅助视频文件(权限不足): {aux_input}")
                return False
            
            # 验证FFmpeg配置
            if not self.ffmpeg_path:
                self.show_error("FFmpeg路径未配置，请先设置FFmpeg路径")
                return False
            if not os.path.exists(self.ffmpeg_path):
                self.show_error(f"FFmpeg可执行文件不存在: {self.ffmpeg_path}")
                return False
            if not os.access(self.ffmpeg_path, os.X_OK):
                self.show_error(f"FFmpeg无执行权限: {self.ffmpeg_path}")
                return False
                
            # 批量模式额外验证
            if self.batch_mode.get():
                main_files = self._get_video_files(main_input)
                aux_files = self._get_video_files(aux_input)
                
                if len(main_files) != len(aux_files):
                    self.show_error(f"主视频({len(main_files)})和辅助视频({len(aux_files)})数量不匹配")
                    return False
                    
                if not main_files:
                    self.show_error("未找到任何有效的视频文件")
                    return False
                    
                # 验证批量文件可读性
                for f in main_files + aux_files:
                    if not os.access(f, os.R_OK):
                        self.show_error(f"无法读取视频文件(权限不足): {f}")
                        return False
                
            return True
        except Exception as e:
            self.show_error(f"验证输入参数时发生错误: {str(e)}")
            return False

    def _get_video_files(self, folder_path):
        """获取文件夹中的视频文件列表"""
        video_extensions = ('.mp4', '.avi', '.mov')
        files = []
        for f in os.listdir(folder_path):
            if f.lower().endswith(video_extensions):
                files.append(os.path.join(folder_path, f))
        # 按文件名排序确保顺序一致
        files.sort()
        return files

    def _process_videos_thread(self):
        """视频处理线程（支持批量处理）"""
        try:
            main_input = self.main_video_entry.get()
            aux_input = self.aux_video_entry.get()
            output_path = self.output_entry.get()

            if self.batch_mode.get():
                # 批量处理模式
                main_files = self._get_video_files(main_input)
                aux_files = self._get_video_files(aux_input)
                total = len(main_files)
                
                for i, (main_video, aux_video) in enumerate(zip(main_files, aux_files), 1):
                    # 设置输出路径
                    filename = os.path.basename(main_video)
                    output_file = os.path.join(output_path, f"processed_{filename}")
                    
                    # 更新进度显示
                    self.root.after(0, lambda: self.progress_var.set(f"处理中: {i}/{total} - {filename}"))
                    
                    # 处理当前视频对，使用主视频文件名作为临时文件前缀
                    base_name = os.path.splitext(os.path.basename(main_video))[0]
                    self._process_single_video(main_video, aux_video, output_file, base_name)
                    
                    # 短暂暂停避免UI卡顿
                    time.sleep(0.1)
                    
                self.root.after(0, lambda: self.progress_var.set(f"批量处理完成: {total}个视频"))
            else:
                # 单文件处理模式
                self._process_single_video(main_input, aux_input, output_path)
                
        except Exception as e:
            self.root.after(0, lambda: self.show_error(f"处理失败: {str(e)}"))
        finally:
            self.root.after(0, lambda: self.process_btn.config(state=tk.NORMAL))

    def _process_single_video(self, main_video, aux_video, output_path, temp_prefix=None):
        """处理单个视频对
        Args:
            main_video: 主视频路径
            aux_video: 辅助视频路径
            output_path: 输出路径
            temp_prefix: 临时文件前缀，如果为None则使用默认值
        """
        try:
            # 创建临时目录
            temp_dir = os.path.join(os.path.dirname(output_path), "temp_video")
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
            os.makedirs(temp_dir, exist_ok=True)
            
            # 在日志中记录临时目录路径
            temp_dir_log = os.path.join(os.path.dirname(output_path), "temp_dir_location.log")
            with open(temp_dir_log, 'w') as f:
                f.write(f"临时文件存储位置: {os.path.abspath(temp_dir)}\n")
                f.write(f"临时文件将在处理完成后自动删除\n")

            # 创建日志目录
            log_dir = os.path.join(os.path.dirname(output_path), "video_processor_logs")
            os.makedirs(log_dir, exist_ok=True)
            
            # 在UI中显示临时目录路径
            self.root.after(0, lambda: self.status_var.set(f"临时文件存储在: {temp_dir}"))

            def _validate_path(self, path):
                """验证路径并返回绝对路径字符串"""
                if not isinstance(path, str):
                    path = str(path)
                path = os.path.normpath(path)
                if not os.path.exists(path):
                    raise FileNotFoundError(f"路径不存在: {path}")
                return os.path.abspath(path)

            def _build_command(self, base_cmd, *args):
                """安全构建命令参数列表"""
                cmd = [str(base_cmd)]
                for arg in args:
                    if isinstance(arg, (list, tuple)):
                        cmd.extend(str(x) for x in arg)
                    else:
                        cmd.append(str(arg))
                return cmd

            def run_command(self, cmd, log_name):
                """运行命令并记录日志"""
                try:
                    # 验证日志目录
                    log_dir = self._validate_path(os.path.dirname(log_name))
                    log_file = os.path.join(log_dir, f"{os.path.basename(log_name)}.log")
                    progress_file = os.path.join(log_dir, "progress.log")
                    
                    # 安全构建命令参数
                    full_cmd = []
                    for item in cmd:
                        if isinstance(item, (list, tuple)):
                            full_cmd.extend(str(x) for x in item)
                        else:
                            full_cmd.append(str(item))
                    if "ffmpeg" in full_cmd[0].lower():
                        full_cmd.extend([
                            '-progress', self._validate_path(progress_file),
                            '-stats_period', '0.5'
                        ])
                    
                    # 记录调试信息
                    debug_info = {
                        'timestamp': datetime.now().isoformat(),
                        'command': ' '.join(full_cmd),
                        'working_dir': os.getcwd(),
                        'environment': dict(os.environ)
                    }
                    
                    with open(log_file, 'w') as f:
                        f.write(f"调试信息:\n{json.dumps(debug_info, indent=2)}\n\n")
                        f.write(f"执行命令: {' '.join(full_cmd)}\n\n")
                    
                    # 执行命令
                    start_time = time.time()
                    process = subprocess.Popen(
                        full_cmd,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True,
                        shell=False,
                        encoding='utf-8',
                        errors='replace'
                    )
                    
                    # 获取总时长
                    total_duration = None
                    if "ffmpeg" in cmd[0].lower():
                        for arg in cmd:
                            if arg.startswith('-t '):
                                total_duration = float(arg[2:])
                                break
                        if total_duration is None:
                            total_duration = get_duration(cmd[cmd.index('-i')+1])
                    
                    while True:
                        # 检查超时
                        if time.time() - start_time > self.TIMEOUT:
                            process.kill()
                            raise Exception("处理超时")
                        
                        # 读取进度
                        if os.path.exists(progress_file):
                            with open(progress_file) as pf:
                                for line in pf:
                                    if 'out_time_ms' in line:
                                        ms = int(line.split('=')[1])
                                        seconds = ms / 1000000
                                        if total_duration:
                                            percent = min(100, int(seconds / total_duration * 100))
                                            self.root.after(0, lambda p=percent, s=seconds, td=total_duration: (
                                                self.progress_bar.config(value=p),
                                                self.percent_var.set(f"{p}%"),
                                                self.progress_var.set(f"处理进度: {s:.1f}/{td:.1f}秒")
                                            ))
                                        else:
                                            self.root.after(0, lambda: self.progress_var.set(f"处理进度: {seconds:.1f}秒"))
                        
                        # 检查进程状态
                        if process.poll() is not None:
                            if process.returncode != 0:
                                raise subprocess.CalledProcessError(process.returncode, cmd)
                            self.root.after(0, lambda: (
                                self.progress_bar.config(value=100),
                                self.percent_var.set("100%")
                            ))
                            return True
                        
                        time.sleep(0.5)
                
                except subprocess.CalledProcessError as e:
                    with open(log_file, 'a') as f:
                        f.write(f"\n命令执行失败: {str(e)}\n")
                    raise
                except Exception as e:
                    with open(log_file, 'a') as f:
                        f.write(f"\n处理异常: {str(e)}\n")
                    raise

            # 获取主视频时长(秒)
            def get_duration(filepath):
                try:
                    # 首先检查文件是否存在
                    norm_path = os.path.normpath(str(filepath))
                    if not os.path.exists(norm_path):
                        raise FileNotFoundError(f"视频文件不存在: {norm_path}")
                    
                    # 构建ffprobe命令路径
                    ffprobe_path = None
                    # 尝试从ffmpeg路径推断ffprobe路径
                    if self.ffmpeg_path:
                        ffmpeg_str = str(self.ffmpeg_path)
                        if 'ffmpeg' in ffmpeg_str.lower():
                            # 替换ffmpeg为ffprobe
                            ffprobe_path = ffmpeg_str.replace('ffmpeg', 'ffprobe')
                        else:
                            # 尝试在相同目录下查找ffprobe
                            ffprobe_path = os.path.join(os.path.dirname(ffmpeg_str), 'ffprobe.exe')
                    
                    # 验证ffprobe路径是否存在
                    if not ffprobe_path or not os.path.exists(ffprobe_path):
                        # 尝试在系统PATH中查找ffprobe
                        try:
                            subprocess.run(['ffprobe', '-version'], check=True)
                            ffprobe_path = 'ffprobe'
                        except:
                            raise FileNotFoundError(
                                "找不到ffprobe可执行文件\n"
                                "请确保ffprobe.exe与ffmpeg.exe在同一目录\n"
                                "或已添加到系统PATH环境变量中"
                            )
                    
                    # 确保所有参数都是字符串
                    cmd = [
                        str(ffprobe_path),
                        '-v', 'error',
                        '-show_entries', 'format=duration',
                        '-of', 'default=noprint_wrappers=1:nokey=1',
                        str(norm_path)
                    ]
                    
                    # 执行ffprobe命令并获取完整输出
                    result = subprocess.run(
                        cmd,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True,
                        check=True,
                        timeout=10,
                        creationflags=subprocess.CREATE_NO_WINDOW
                    )
                    
                    # 安全处理命令输出
                    try:
                        output = str(result.stdout).strip() if result.stdout else ""
                        error_output = str(result.stderr).strip() if result.stderr else ""
                        
                        # 构建安全的日志内容
                        log_content = []
                        try:
                            log_content.append(f"文件: {str(filepath)}")
                            log_content.append(f"标准输出: {str(output)}")
                            log_content.append(f"标准错误: {str(error_output)}")
                        except Exception as e:
                            log_content.append(f"日志记录错误: {str(e)}")
                        
                        # 写入日志文件
                        with open(os.path.join(log_dir, "ffprobe_output.log"), 'a') as f:
                            f.write("\n".join(log_content) + "\n\n")
                        
                        # 验证输出格式
                        if not output or not output.replace('.', '').isdigit():
                            error_parts = [
                                "ffprobe返回无效时长",
                                f"输出: {output}",
                                f"错误: {error_output}" if error_output else "无错误详情"
                            ]
                            raise ValueError("\n".join(error_parts))
                    
                    # 安全转换和验证时长
                    try:
                        duration = float(output)
                        if duration <= 0:
                            raise ValueError(f"视频时长必须大于0: {duration}秒")
                        if duration > 86400:  # 24小时
                            raise ValueError(f"视频时长过长(>24小时): {duration}秒")
                    except (ValueError, TypeError) as ve:
                        raise ValueError(f"无法解析视频时长: {str(ve)}")
                    
                    # 验证时长合理性
                    if duration <= 0:
                        raise ValueError(f"无效的视频时长(<=0): {duration}秒")
                    if duration > 86400:  # 超过24小时
                        raise ValueError(f"视频时长过长(>24小时): {duration}秒")
                        
                    return duration
                    
                except subprocess.TimeoutExpired:
                    error_msg = f"FFprobe命令超时: {filepath}"
                    with open(os.path.join(log_dir, "ffprobe_error.log"), 'a') as f:
                        f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - {error_msg}\n")
                    return 100.0
                    
                except subprocess.CalledProcessError as e:
                    error_msg = f"FFprobe命令失败: {str(e)}\n返回码: {e.returncode}\n错误输出: {e.stderr}"
                    with open(os.path.join(log_dir, "ffprobe_error.log"), 'a') as f:
                        f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - {error_msg}\n")
                    return 100.0
                    
                except ValueError as ve:
                    error_msg = f"时长解析错误: {str(ve)}"
                    with open(os.path.join(log_dir, "duration_error.log"), 'a') as f:
                        f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - {error_msg}\n")
                    return 100.0
                    
                def _log_error(self, log_file, error_msg):
                    """统一的错误日志记录方法"""
                    with open(os.path.join(log_dir, log_file), 'a') as f:
                        f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - {error_msg}\n")
                    return 100.0

                except subprocess.TimeoutExpired:
                    return self._log_error("ffprobe_error.log", f"FFprobe命令超时: {filepath}")
                except subprocess.CalledProcessError as e:
                    return self._log_error("ffprobe_error.log", 
                        f"FFprobe命令失败: {str(e)}\n返回码: {e.returncode}\n错误输出: {e.stderr}")
                except ValueError as ve:
                    return self._log_error("duration_error.log", f"时长解析错误: {str(ve)}")
                except Exception as e:
                    return self._log_error("duration_error.log", 
                        f"获取时长时发生意外错误: {type(e).__name__}: {str(e)}")
                    return 100.0

            # 获取主视频时长并确定处理模式
            duration = get_duration(main_video)
            if duration <= 100:  # 小于等于1分40秒
                duration_str = '51.733333333333334'
                crop_duration = '51.733333333333334'
            else:
                duration_str = '93.63333333333334'
                crop_duration = '93.63333333333334'

            try:
                # 严格遵循用户指定的临时文件命名规则
                base_name = os.path.splitext(os.path.basename(main_video))[0]
                aux_base = os.path.splitext(os.path.basename(aux_video))[0]
                
                # 临时文件命名完全匹配用户示例
                aux_temp1 = os.path.join(temp_dir, f"WeChat_{aux_base}_temp1.mp4")
                aux_temp2 = os.path.join(temp_dir, f"WeChat_{aux_base}_temp1.mp4")
                main_temp2 = os.path.join(temp_dir, f"{base_name}_temp2.mp4")
                intermediate_output = os.path.join(temp_dir, f"{base_name}_初级.mp4")
                final_output = os.path.join(temp_dir, f"两仪_{base_name}.mp4")

                # 第一步：处理辅助视频 (2x4裁剪) - 完全匹配用户命令格式
                self.root.after(0, lambda: self.status_var.set("第一步：处理辅助视频(2x4裁剪)..."))
                aux_cmd1 = [
                    self.ffmpeg_path,
                    '-y',
                    '-stream_loop', '-1',
                    '-i', os.path.normpath(aux_video),
                    '-vf', 'setsar=1,crop=2:4:0:0',
                    '-r', '30',
                    '-t', crop_duration,
                    '-an',
                    '-c:v', 'libx264',
                    '-pix_fmt', 'yuv420p',
                    os.path.normpath(aux_temp1)
                ]
                
                # 记录完整命令到日志
                with open(os.path.join(log_dir, "command_details.log"), 'a') as cmd_log:
                    cmd_log.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 执行命令1: {' '.join(aux_cmd1)}\n")

                # 第二步：处理辅助视频 (缩放填充)
                self.root.after(0, lambda: self.status_var.set("第二步：处理辅助视频(缩放填充)..."))
                aux_cmd2 = [
                    self.ffmpeg_path,
                    '-y',
                    '-stream_loop', '-1',
                    '-i', os.path.normpath(aux_video),
                    '-t', crop_duration,
                    '-vf', 'scale=1080:1920:force_original_aspect_ratio=decrease,pad=1080:1920:(ow-iw)/2:(oh-ih)/2:color=black,setsar=1:1,fps=30',
                    '-c:v', 'libx264',
                    '-c:a', 'copy',
                    os.path.normpath(aux_temp2)
                ]
                self.root.after(0, lambda: self.status_var.set("第三步：处理主视频(帧率转换)..."))
                if temp_prefix is None:
                    # 从主视频文件名中提取基础名
                    main_base = os.path.splitext(os.path.basename(main_video))[0]
                    main_temp2 = os.path.join(temp_dir, f"{main_base}_temp2.mp4")
                else:
                    main_temp2 = os.path.join(temp_dir, f"{temp_prefix}_main_temp2.mp4")
                main_cmd1 = [
                    self.ffmpeg_path,
                    '-y',
                    '-i', os.path.normpath(main_video),
                    '-vf', 'fps=30',
                    '-c:v', 'libx264',
                    os.path.normpath(main_temp2)
                ]
                run_command(main_cmd1, "03_main_video_fps")

                # 第四步：主视频与辅助视频交织合并 (完全匹配用户命令)
                self.root.after(0, lambda: self.status_var.set("第四步：视频交织合并..."))
                if temp_prefix is None:
                    # 使用主视频基础名+_初级.mp4
                    intermediate_output = os.path.join(temp_dir, f"{main_base}_初级.mp4")
                else:
                    intermediate_output = os.path.join(temp_dir, f"{temp_prefix}_intermediate.mp4")
                
                # 详细检查输入文件是否存在
                missing_files = []
                if not os.path.exists(main_temp2):
                    missing_files.append(f"主视频临时文件: {os.path.abspath(main_temp2)}")
                if not os.path.exists(aux_temp1):
                    missing_files.append(f"辅助视频临时文件: {os.path.abspath(aux_temp1)}")
                
                if missing_files:
                    error_msg = "以下文件未生成成功:\n" + "\n".join(missing_files)
                    error_msg += "\n可能原因:\n1. FFmpeg处理失败\n2. 磁盘空间不足\n3. 文件路径无效"
                    self.show_error(error_msg)
                    raise Exception(error_msg)

                # 严格遵循用户提供的原始命令格式
                merge_cmd1 = [
                    self.ffmpeg_path,
                    '-y',
                    '-i', os.path.normpath(main_temp2),
                    '-i', os.path.normpath(aux_temp1),
                    '-filter_complex', '[0:v][1:v]interleave',
                    '-map', '0:a',
                    '-c:a', 'copy',
                    '-c:v', 'libx264',
                    '-b:v', '10M',
                    os.path.normpath(intermediate_output)
                ]
                
                # 添加详细的命令日志
                with open(os.path.join(log_dir, "full_command.log"), 'a') as cmd_log:
                    cmd_log.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 执行命令: {' '.join(merge_cmd1)}\n")
                    cmd_log.write(f"输入文件1大小: {os.path.getsize(main_temp2)} bytes\n")
                    cmd_log.write(f"输入文件2大小: {os.path.getsize(aux_temp1)} bytes\n")
                
                # 添加输入视频格式验证
                def check_video_format(filepath):
                    try:
                        result = subprocess.run(
                            [self.ffmpeg_path, '-i', filepath],
                            stderr=subprocess.PIPE,
                            stdout=subprocess.PIPE,
                            text=True
                        )
                        return 'Video:' in result.stderr
                    except Exception:
                        return False

                if not all(check_video_format(f) for f in [main_temp2, aux_temp1]):
                    raise Exception("输入视频格式不支持")

                # 添加更详细的错误处理
                try:
                    run_command(merge_cmd1, "04_video_interleave")
                except subprocess.CalledProcessError as e:
                    # 记录完整的错误信息
                    error_log_path = os.path.join(log_dir, "ffmpeg_error.log")
                    with open(error_log_path, 'w') as f:
                        f.write(f"命令: {' '.join(merge_cmd1)}\n")
                        f.write(f"返回码: {e.returncode}\n")
                        f.write(f"标准错误:\n{e.stderr}\n")
                        f.write(f"标准输出:\n{e.stdout}\n")
                    
                    # 记录简化的错误信息
                    with open(os.path.join(log_dir, "error_details.log"), 'a') as f:
                        f.write(f"FFmpeg命令失败: {str(e)}\n")
                        f.write(f"命令: {' '.join(merge_cmd1)}\n")
                        f.write(f"返回码: {e.returncode}\n")
                        if e.stdout:
                            f.write(f"标准输出: {e.stdout}\n")
                        if e.stderr:
                            f.write(f"标准错误: {e.stderr}\n")
                    raise

                # 第五步：最终融合
                self.root.after(0, lambda: self.status_var.set("第五步：最终融合..."))
                
                try:
                    # 确定输出文件名
                    if temp_prefix is None:  # 批量处理模式
                        final_output = os.path.join(temp_dir, f"两仪_{main_base}.mp4")
                    else:  # 单文件处理模式
                        final_output = os.path.join(temp_dir, f"{temp_prefix}_final.mp4")
                    
                    # 构建最终合并命令
                    merge_cmd2 = [
                        self.ffmpeg_path,
                        '-y',
                        '-i', os.path.normpath(aux_temp1),
                        '-i', os.path.normpath(intermediate_output),
                        '-map', '0:v:0',
                        '-map', '1:v:0',
                        '-map', '1:a:0',
                        '-threads', '24',
                        '-c', 'copy',
                        '-movflags', 'faststart',
                        '-disposition:v:0', '0',
                        '-disposition:v:1', 'default',
                        '-disposition:a:0', 'default',
                        os.path.normpath(final_output)
                    ]
                    
                    # 执行最终合并命令
                    run_command(merge_cmd2, "05_final_merge")

                    # 验证并移动输出文件
                    if not os.path.exists(final_output):
                        raise Exception("输出文件未生成，请检查日志")
                    
                    shutil.move(final_output, os.path.normpath(output_path))
                    self.root.after(0, lambda: self.status_var.set("处理完成"))
                    self.root.after(0, lambda: messagebox.showinfo("成功", "视频处理完成"))
                    
                except Exception as e:
                    self.root.after(0, lambda: self.show_error(f"最终融合失败: {str(e)}"))

            except subprocess.CalledProcessError as e:
                error_log = os.path.join(log_dir, "error.log")
                with open(error_log, 'w') as f:
                    f.write(f"错误详情:\n{str(e)}\n")
                    # 获取FFmpeg版本信息
                    f.write("\nFFmpeg版本信息:\n")
                    subprocess.run([self.ffmpeg_path, '-version'], stdout=f, stderr=subprocess.STDOUT)
                
                error_msg = (
                    f"视频处理失败，详细错误已记录到:\n{error_log}\n"
                    "常见原因和解决方案:\n"
                    "1. 输入视频格式不支持 - 请尝试转换为MP4格式\n"
                    "2. 输出路径无写入权限 - 请选择其他输出目录\n"
                    "3. FFmpeg版本不兼容 - 请更新到最新版本\n"
                    "4. 系统资源不足 - 关闭其他程序重试"
                )
                self.root.after(0, lambda: self.show_error(error_msg))

            except Exception as e:
                def show_error_with_msg(msg=e):
                    self.show_error(f"发生意外错误: {str(msg)}")
                self.root.after(0, show_error_with_msg)

            finally:
                # 清理临时目录
                if os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir, ignore_errors=True)
                self.root.after(0, lambda: self.process_btn.config(state=tk.NORMAL))

        except Exception as e:
            def show_init_error(msg=e):
                self.show_error(f"初始化处理失败: {str(msg)}")
            self.root.after(0, show_init_error)

if __name__ == "__main__":
    root = tk.Tk()
    app = MultiTrackVideoProcessor(root)
    root.mainloop()
