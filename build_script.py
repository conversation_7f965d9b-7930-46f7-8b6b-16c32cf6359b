import os
import sys
from shutil import copy2

# 检查并导入PyInstaller
try:
    # 首选导入方式
    from PyInstaller import __main__ as pyi_main
except ImportError:
    try:
        # 备选导入方式
        import PyInstaller.__main__ as pyi_main
    except ImportError:
        print("错误: 无法导入PyInstaller")
        print("可能原因:")
        print("1. PyInstaller未正确安装")
        print("2. Python环境配置有问题")
        print("\n解决方案:")
        print("1. 确保使用正确的Python环境:")
        print("   python -m pip install pyinstaller")
        print("2. 检查PyInstaller是否安装:")
        print("   pip list | findstr PyInstaller (Windows)")
        print("   pip list | grep PyInstaller (Mac/Linux)")
        print("3. 尝试重新安装:")
        print("   pip uninstall pyinstaller")
        print("   pip install --no-cache-dir pyinstaller")
        sys.exit(1)

print(f"✓ PyInstaller 已安装 (版本: {pyi_main.__version__})")

# 检查必要文件是否存在
required_files = ['ffmpeg.exe', 'ffprobe.exe', 'main.pyc_Source_Patcher.py']
missing_files = [f for f in required_files if not os.path.exists(f)]
if missing_files:
    print("错误: 缺少必要文件:")
    for f in missing_files:
        print(f" - {f}")
    sys.exit(1)

# 确保输出目录存在
output_dir = "dist"
os.makedirs(output_dir, exist_ok=True)

# 复制必要的文件（如FFmpeg）
def copy_required_files():
    # 复制FFmpeg相关文件
    for exe in ['ffmpeg.exe', 'ffprobe.exe']:
        if os.path.exists(exe):
            copy2(exe, os.path.join(output_dir, exe))
    
    # 复制其他必要文件
    # ...

# PyInstaller配置
pyinstaller_args = [
    'main.pyc_Source_Patcher.py',  # 主程序入口
    '--onefile',  # 打包成单个exe
    '--windowed',  # 不显示控制台窗口
    '--icon=app.ico',  # 应用图标
    '--name=皇冠AB视频处理工具',  # 输出文件名
    '--add-data=ffmpeg.exe;.',  # 包含FFmpeg
    '--add-data=ffprobe.exe;.',  # 包含FFprobe
    '--hidden-import=ttkbootstrap',  # 显式包含ttkbootstrap
    '--hidden-import=pkg_resources.py2_warn',
    '--distpath', output_dir,
    '--workpath', 'build',
    '--specpath', 'build'
]

# 执行打包
print("开始打包...")
PyInstaller.__main__.run(pyinstaller_args)

# 复制必要文件到输出目录
print("复制依赖文件...")
copy_required_files()

print("打包完成！输出目录:", output_dir)
