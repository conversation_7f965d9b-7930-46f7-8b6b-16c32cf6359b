import tkinter as tk
from tkinter import filedialog, messagebox
import subprocess
import os
import threading
import shutil

class SimpleVideoProcessor:
    def __init__(self, root):
        self.root = root
        self.root.title("小鸡快跑AB版")
        
        # UI布局
        row = 0
        
        # 主视频文件夹选择
        main_folder_frame = tk.LabelFrame(root, text="主视频文件夹", padx=5, pady=5)
        main_folder_frame.grid(row=row, column=0, columnspan=3, sticky="ew", padx=5, pady=5)
        row += 1
        
        tk.Label(main_folder_frame, text="主视频文件夹:").grid(row=0, column=0, padx=5, pady=5)
        self.main_folder_entry = tk.Entry(main_folder_frame, width=40)
        self.main_folder_entry.grid(row=0, column=1, padx=5, pady=5)
        self.main_folder_btn = tk.Button(main_folder_frame, text="浏览", command=lambda: self.select_folder("main"))
        self.main_folder_btn.grid(row=0, column=2, padx=5, pady=5)
        
        # 辅助视频文件夹选择
        aux_folder_frame = tk.LabelFrame(root, text="辅助视频文件夹", padx=5, pady=5)
        aux_folder_frame.grid(row=row, column=0, columnspan=3, sticky="ew", padx=5, pady=5)
        row += 1
        
        tk.Label(aux_folder_frame, text="辅助视频文件夹:").grid(row=0, column=0, padx=5, pady=5)
        self.aux_folder_entry = tk.Entry(aux_folder_frame, width=40)
        self.aux_folder_entry.grid(row=0, column=1, padx=5, pady=5)
        self.aux_folder_btn = tk.Button(aux_folder_frame, text="浏览", command=lambda: self.select_folder("aux"))
        self.aux_folder_btn.grid(row=0, column=2, padx=5, pady=5)

        # 输出文件夹选择
        output_folder_frame = tk.LabelFrame(root, text="输出文件夹", padx=5, pady=5)
        output_folder_frame.grid(row=row, column=0, columnspan=3, sticky="ew", padx=5, pady=5)
        row += 1
        
        tk.Label(output_folder_frame, text="输出文件夹:").grid(row=0, column=0, padx=5, pady=5)
        self.output_folder_entry = tk.Entry(output_folder_frame, width=40)
        self.output_folder_entry.grid(row=0, column=1, padx=5, pady=5)
        self.output_folder_btn = tk.Button(output_folder_frame, text="浏览", command=lambda: self.select_folder("output"))
        self.output_folder_btn.grid(row=0, column=2, padx=5, pady=5)

        # 状态显示
        self.status_var = tk.StringVar()
        self.status_var.set("准备就绪")
        tk.Label(root, textvariable=self.status_var).grid(row=row, column=0, columnspan=3)
        row += 1

        # 处理按钮
        self.process_btn = tk.Button(root, text="开始处理", command=self.process_video)
        self.process_btn.grid(row=row, column=1, pady=10)



    def select_folder(self, folder_type):
        """选择文件夹"""
        folderpath = filedialog.askdirectory(title=f"选择{folder_type}文件夹")
        if folderpath:
            if folder_type == "main":
                self.main_folder_entry.delete(0, tk.END)
                self.main_folder_entry.insert(0, folderpath)
            elif folder_type == "aux":
                self.aux_folder_entry.delete(0, tk.END)
                self.aux_folder_entry.insert(0, folderpath)
            else:  # output
                self.output_folder_entry.delete(0, tk.END)
                self.output_folder_entry.insert(0, folderpath)





    def _folder_has_videos(self, folder_path):
        """检查文件夹中是否有MP4视频文件"""
        if not os.path.isdir(folder_path):
            return False
        return any(f.lower().endswith('.mp4') for f in os.listdir(folder_path))

    def check_inputs(self):
        """验证输入参数"""
        main_folder = self.main_folder_entry.get()
        aux_folder = self.aux_folder_entry.get()
        output_folder = self.output_folder_entry.get()
        
        if not main_folder:
            messagebox.showerror("错误", "请选择主视频文件夹")
            return False
        if not aux_folder:
            messagebox.showerror("错误", "请选择辅助视频文件夹")
            return False
        if not output_folder:
            messagebox.showerror("错误", "请选择输出文件夹")
            return False
            
        if not os.path.isdir(main_folder):
            messagebox.showerror("错误", "主视频文件夹路径无效")
            return False
        if not os.path.isdir(aux_folder):
            messagebox.showerror("错误", "辅助视频文件夹路径无效")
            return False
        if not os.path.isdir(output_folder):
            messagebox.showerror("错误", "输出文件夹路径无效")
            return False
            
        if not self._folder_has_videos(main_folder):
            messagebox.showerror("错误", "主视频文件夹中没有找到MP4视频文件")
            return False
        if not self._folder_has_videos(aux_folder):
            messagebox.showerror("错误", "辅助视频文件夹中没有找到MP4视频文件")
            return False
            
        return True


    def process_video(self):
        """启动视频处理线程"""
        if not self.check_inputs():
            return
            
        self.status_var.set("处理中...")
        threading.Thread(target=self._process_batch_videos, daemon=True).start()



    def _get_base_filename(self, filename):
        """获取不带扩展名的文件名"""
        return os.path.splitext(filename)[0]

    def _process_batch_videos(self):
        """批量处理视频"""
        main_folder = self.main_folder_entry.get()
        aux_folder = self.aux_folder_entry.get()
        output_folder = self.output_folder_entry.get()
        
        # 获取主视频和辅助视频文件列表
        main_files = sorted([f for f in os.listdir(main_folder) if f.endswith('.mp4')])
        aux_files = sorted([f for f in os.listdir(aux_folder) if f.endswith('.mp4')])
        
        if len(main_files) != len(aux_files):
            messagebox.showwarning("警告", 
                f"视频数量不匹配\n主视频数量: {len(main_files)}\n辅助视频数量: {len(aux_files)}")
            return
            
        # 按顺序处理视频对
        for i in range(len(main_files)):
            main_file = main_files[i]
            aux_file = aux_files[i]
            main_path = os.path.join(main_folder, main_file)
            aux_path = os.path.join(aux_folder, aux_file)
            output_path = os.path.join(output_folder, f"{os.path.splitext(main_file)[0]}_processed.mp4")
            
            self.status_var.set(f"处理中 ({i+1}/{len(main_files)})")
            self._process_video_pair(main_path, aux_path, output_path)
            
        self.status_var.set(f"批量处理完成，共处理 {len(main_files)} 对视频")

    def _process_video_pair(self, main_video, aux_video, output_path):
        """处理一对视频"""
        try:
            
            # 创建临时目录
            temp_dir = os.path.join(os.path.dirname(output_path), "temp_video_process")
            os.makedirs(temp_dir, exist_ok=True)
            
            # 设置不显示CMD窗口的参数
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE
            
            # 处理主视频
            processed_main = os.path.join(temp_dir, "processed_main.mp4")
            cmd = [
                "ffmpeg", "-y", "-i", main_video,
                "-vf", "fps=19.356,scale=720:1280:force_original_aspect_ratio=decrease,"
                       "pad=720:1280:(ow-iw)/2:(oh-ih)/2:color=black,setsar=1:1,"
                       "eq=brightness=-0.026:contrast=0.975",
                "-c:v", "libx264", "-preset", "fast", "-crf", "20",
                "-pix_fmt", "yuv420p", "-r", "19.356", processed_main
            ]
            subprocess.run(cmd, check=True, startupinfo=startupinfo)
            
            # 获取主视频时长
            cmd = [
                "ffprobe", "-v", "error", "-show_entries",
                "format=duration", "-of", "default=noprint_wrappers=1:nokey=1",
                processed_main
            ]
            result = subprocess.run(cmd, check=True, capture_output=True, 
                                  text=True, startupinfo=startupinfo)
            main_duration = float(result.stdout.strip())
            
            # 处理辅助视频
            processed_aux = os.path.join(temp_dir, "processed_aux.mp4")
            cmd = [
                "ffmpeg", "-y", "-stream_loop", "-1", "-i", aux_video,
                "-t", str(main_duration),
                "-vf", "fps=19.356,scale=720:1280:force_original_aspect_ratio=decrease,"
                       "pad=720:1280:(ow-iw)/2:(oh-ih)/2:color=black,setsar=1:1,"
                       "eq=saturation=0.944:gamma=1.016",
                "-c:v", "libx264", "-preset", "fast", "-crf", "20",
                "-pix_fmt", "yuv420p", "-r", "19.356", processed_aux
            ]
            subprocess.run(cmd, check=True, startupinfo=startupinfo)
            
            # 合并视频
            interleaved = os.path.join(temp_dir, "interleaved.mp4")
            cmd = [
                "ffmpeg", "-y", "-i", processed_main, "-i", processed_aux,
                "-filter_complex", "[0:v][0:v][1:v][1:v][1:v]interleave=nb_inputs=5",
                "-map", "0:a?", "-c:v", "libx264", "-preset", "fast",
                "-crf", "18", "-pix_fmt", "yuv420p", interleaved
            ]
            subprocess.run(cmd, check=True, startupinfo=startupinfo)
            
            # 最终输出处理
            cmd = [
                "ffmpeg", "-y", "-i", interleaved,
                "-c:v", "libx264", "-preset", "fast", "-crf", "19",
                "-c:a", "copy", "-movflags", "+faststart", output_path
            ]
            subprocess.run(cmd, check=True, startupinfo=startupinfo)
            
            # 清理临时文件
            shutil.rmtree(temp_dir)
            
            self.status_var.set(f"已完成处理: {os.path.basename(output_path)}")
            
        except subprocess.CalledProcessError as e:
            self.status_var.set("处理失败")
            messagebox.showerror("错误", f"FFmpeg处理失败: {str(e)}")
        except Exception as e:
            self.status_var.set("处理失败")
            messagebox.showerror("错误", f"处理过程中发生错误: {str(e)}")

if __name__ == "__main__":
    root = tk.Tk()
    app = SimpleVideoProcessor(root)
    root.mainloop()
