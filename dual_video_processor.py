import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import subprocess
import platform
import os
import shutil
import threading
from datetime import datetime
import glob

class DualVideoProcessor:
    def __init__(self, root):
        self.root = root
        self.root.title("小鸡快跑KS版本")
        self.progress_value = 0
        self.setup_ui()
        
    def setup_ui(self):
        """设置GUI界面"""
        # 主框架
        main_frame = tk.Frame(self.root, padx=10, pady=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 主视频文件夹选择
        row = 0
        tk.Label(main_frame, text="主视频文件夹:").grid(row=row, column=0, sticky="e", padx=5, pady=5)
        self.main_video_dir_entry = tk.Entry(main_frame, width=50)
        self.main_video_dir_entry.grid(row=row, column=1, padx=5, pady=5)
        tk.Button(main_frame, text="浏览", command=lambda: self.select_dir(self.main_video_dir_entry)).grid(row=row, column=2, padx=5, pady=5)
        row += 1
        
        # 辅助视频文件夹选择
        tk.Label(main_frame, text="辅助视频文件夹:").grid(row=row, column=0, sticky="e", padx=5, pady=5)
        self.aux_video_dir_entry = tk.Entry(main_frame, width=50)
        self.aux_video_dir_entry.grid(row=row, column=1, padx=5, pady=5)
        tk.Button(main_frame, text="浏览", command=lambda: self.select_dir(self.aux_video_dir_entry)).grid(row=row, column=2, padx=5, pady=5)
        row += 1
        
        # 输出目录选择
        tk.Label(main_frame, text="输出目录:").grid(row=row, column=0, sticky="e", padx=5, pady=5)
        self.output_dir_entry = tk.Entry(main_frame, width=50)
        self.output_dir_entry.grid(row=row, column=1, padx=5, pady=5)
        tk.Button(main_frame, text="浏览", command=lambda: self.select_dir(self.output_dir_entry)).grid(row=row, column=2, padx=5, pady=5)
        row += 1
        
        # 码率设置
        tk.Label(main_frame, text="视频码率(Mbps):").grid(row=row, column=0, sticky="e", padx=5, pady=5)
        self.video_bitrate = tk.StringVar(value="10")
        tk.Entry(main_frame, textvariable=self.video_bitrate, width=8).grid(row=row, column=1, sticky="w", padx=5, pady=5)
        
        tk.Label(main_frame, text="音频码率(kbps):").grid(row=row, column=1, sticky="e", padx=5, pady=5)
        self.audio_bitrate = tk.StringVar(value="128")
        tk.Entry(main_frame, textvariable=self.audio_bitrate, width=8).grid(row=row, column=2, sticky="w", padx=5, pady=5)
        row += 1

        # GPU加速选项
        self.use_gpu = tk.BooleanVar(value=False)
        tk.Checkbutton(main_frame, text="启用GPU加速", variable=self.use_gpu).grid(row=row, column=0, columnspan=3, pady=5)
        row += 1

        # 码率说明
        tk.Label(main_frame, text="提示: 提高码率可能提升质量但会增加文件大小", fg="blue").grid(row=row, column=0, columnspan=3, pady=5)
        row += 1

        # 处理按钮
        self.process_btn = tk.Button(main_frame, text="开始处理", command=self.start_processing)
        self.process_btn.grid(row=row, column=1, pady=10)
        row += 1
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, orient="horizontal", length=300, mode="determinate")
        self.progress.grid(row=row, column=0, columnspan=3, pady=5)
        row += 1
        
        # 状态显示
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        tk.Label(main_frame, textvariable=self.status_var).grid(row=row, column=0, columnspan=3, pady=5)
        
    def select_dir(self, entry_widget):
        """选择目录"""
        dirpath = filedialog.askdirectory(title="选择目录")
        if dirpath:
            entry_widget.delete(0, tk.END)
            entry_widget.insert(0, dirpath)
            
    def get_video_files(self, dir_path):
        """获取目录中的视频文件"""
        if not os.path.isdir(dir_path):
            return []
            
        video_exts = ('.mp4', '.avi', '.mov')
        return sorted([
            f for f in glob.glob(os.path.join(dir_path, '*'))
            if os.path.splitext(f)[1].lower() in video_exts
        ])
            
    def start_processing(self):
        """启动处理线程"""
        main_dir = self.main_video_dir_entry.get()
        aux_dir = self.aux_video_dir_entry.get()
        output_dir = self.output_dir_entry.get()
        
        if not all([main_dir, aux_dir, output_dir]):
            messagebox.showerror("错误", "请选择主视频文件夹、辅助视频文件夹和输出目录")
            return
            
        # 获取视频文件列表
        main_videos = self.get_video_files(main_dir)
        aux_videos = self.get_video_files(aux_dir)
        
        if not main_videos or not aux_videos:
            messagebox.showerror("错误", "指定的文件夹中没有找到视频文件")
            return
            
        # 检查数量是否匹配
        if len(main_videos) != len(aux_videos):
            messagebox.showerror("错误", "主视频和辅助视频数量不匹配")
            return
            
        self.process_btn.config(state=tk.DISABLED)
        self.status_var.set(f"准备批量处理 {len(main_videos)} 对视频，将自动连续处理...")
        
        # 在后台线程中处理
        threading.Thread(
            target=self.batch_process_videos,
            args=(main_videos, aux_videos, output_dir),
            daemon=True
        ).start()
        
    def batch_process_videos(self, main_videos, aux_videos, output_dir):
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        """批量处理视频"""
        try:
            total = len(main_videos)
            success_count = 0
            
            # 调试日志
            debug_log = os.path.join(output_dir, "batch_debug.log")
            with open(debug_log, "w") as f:
                f.write(f"开始批量处理，共 {total} 对视频\n")
                f.write(f"主视频列表: {main_videos}\n")
                f.write(f"辅助视频列表: {aux_videos}\n")
                f.write(f"输出目录: {output_dir}\n")
            
            # 重置进度条
            self.root.after(0, lambda: self.progress.configure(value=0))
            
            for i, (main_video, aux_video) in enumerate(zip(main_videos, aux_videos)):
                # 生成唯一输出文件名
                video_name = f"merged_{i+1}.mp4"
                final_output = os.path.join(output_dir, video_name)
                
                # 调试日志
                with open(debug_log, "a") as f:
                    f.write(f"\n处理第 {i+1} 对视频:\n")
                    f.write(f"主视频: {main_video}\n")
                    f.write(f"辅助视频: {aux_video}\n")
                    f.write(f"输出文件: {final_output}\n")
                
                # 更新进度和状态
                current_progress = ((i + 1) / total) * 100
                self.update_status(f"正在自动处理第 {i+1}/{total} 对视频，请勿关闭程序...")
                self.update_progress(current_progress)
                
                # 强制更新GUI
                self.root.update_idletasks()
            
                try:
                    # 处理视频对
                    self.process_video_pair(main_video, aux_video, output_dir, final_output)
                    success_count += 1
                    # 调试日志
                    with open(debug_log, "a") as f:
                        f.write(f"第 {i+1} 对视频处理成功\n")
                except Exception as e:
                    error_log = os.path.join(output_dir, f"error_{i+1}.log")
                    with open(error_log, 'w') as f:
                        f.write(f"处理视频对失败:\n{str(e)}")
                    # 调试日志
                    with open(debug_log, "a") as f:
                        f.write(f"第 {i+1} 对视频处理失败: {str(e)}\n")
            
            # 完成进度
            self.update_progress(100)
            self.update_status(f"处理完成! 成功 {success_count}/{total}")
            messagebox.showinfo("完成", f"批量处理完成\n成功: {success_count}\n总数: {total}")
            
        except Exception as e:
            self.update_status("批量处理失败")
            messagebox.showerror("错误", f"批量处理过程中发生错误:\n{str(e)}")
        finally:
            self.root.after(0, lambda: self.process_btn.config(state=tk.NORMAL))
            
    def get_video_dimensions(self, video_path):
        """获取视频尺寸"""
        cmd = [
            "ffprobe",
            "-v", "error",
            "-select_streams", "v:0",
            "-show_entries", "stream=width,height",
            "-of", "csv=p=0",
            video_path
        ]
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode != 0:
            raise RuntimeError(f"获取视频尺寸失败: {video_path}")
        width, height = result.stdout.strip().split(',')
        return int(width), int(height)

    def process_video_pair(self, main_video, aux_video, output_dir, final_output):
        """处理一对视频"""
        # 临时目录
        temp_dir = os.path.join(output_dir, "temp")
        os.makedirs(temp_dir, exist_ok=True)
        
        # 获取主视频原始尺寸
        main_width, main_height = self.get_video_dimensions(main_video)
        
        # 处理主视频
        main_output = os.path.join(temp_dir, "main_fixed.mp4")
        # 根据GPU加速选项设置编码参数
        if self.use_gpu.get():
            video_codec = "h264_nvenc"  # NVIDIA GPU
            # video_codec = "h264_amf"  # AMD GPU
            # video_codec = "h264_qsv"  # Intel GPU
            preset = "fast"
        else:
            video_codec = "libx264"
            preset = "veryfast"
            
        main_cmd = [
            "ffmpeg", "-y",
            "-i", main_video,
            "-vf", f"scale={main_width}:{main_height}",
            "-r", "60",
            "-c:v", video_codec,
            "-preset", preset,
            "-b:v", "10M",
            "-c:a", "aac",
            "-b:a", "199k",
            "-ar", "44100",
            main_output
        ]
        self.run_command(main_cmd)
        
        # 处理辅助视频
        aux_output = os.path.join(temp_dir, "aux_fixed.mp4")
        aux_cmd = [
            "ffmpeg", "-y",
            "-i", aux_video,
            "-vf", f"scale={main_width}:{main_height}",
            "-r", "60",
            "-c:v", video_codec,
            "-preset", preset,
            "-b:v", "10M",
            "-c:a", "aac",
            "-b:a", "199k",
            "-ar", "44100",
            aux_output
        ]
        self.run_command(aux_cmd)
        
        # 合并视频
        # 获取用户设置的码率
        video_bitrate = f"{self.video_bitrate.get()}M"
        audio_bitrate = f"{self.audio_bitrate.get()}k"
        
        merge_cmd = [
            "ffmpeg", "-y",
            "-i", main_output,
            "-stream_loop", "-1",
            "-i", aux_output,
            "-filter_complex",
            f"[0:v]fps=60,scale={main_width}:{main_height},setsar=1[v1];[1:v]fps=60,scale={main_width}:{main_height},setsar=1[v2];[v2][v1]interleave[vout];[0:a]pan=stereo|c0=c0|c1=c1[aout]",
            "-map", "[vout]",
            "-map", "[aout]",
            "-shortest",
            "-r", "120",
            "-c:v", video_codec,
            "-preset", preset,
            "-b:v", video_bitrate,
            "-c:a", "aac",
            "-b:a", audio_bitrate,
            final_output
        ]
        self.run_command(merge_cmd)
        
        # 清理临时文件
        shutil.rmtree(temp_dir)
            
    def run_command(self, cmd):
        """运行命令并检查结果"""
        try:
            startupinfo = None
            if platform.system() == 'Windows':
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='replace',
                startupinfo=startupinfo
            )
            if result.returncode != 0:
                raise RuntimeError(f"命令执行失败: {' '.join(cmd)}\n错误输出:\n{result.stderr}")
        except UnicodeDecodeError:
            # 如果utf-8失败，尝试使用系统默认编码
            startupinfo = None
            if platform.system() == 'Windows':
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                errors='replace',
                startupinfo=startupinfo
            )
            if result.returncode != 0:
                raise RuntimeError(f"命令执行失败: {' '.join(cmd)}\n错误输出:\n{result.stderr}")
            
    def update_status(self, message):
        """更新状态显示"""
        self.root.after(0, lambda: self.status_var.set(message))
        
    def update_progress(self, value):
        """更新进度条"""
        self.root.after(0, lambda: self.progress.configure(value=value))

if __name__ == "__main__":
    root = tk.Tk()
    app = DualVideoProcessor(root)
    root.mainloop()
