import os
import sys
import subprocess
import logging
from datetime import datetime
from shutil import copy2

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('build_debug.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def check_environment():
    """检查Python环境和依赖"""
    logger.info("开始环境检查...")
    
    # 显示当前Python路径
    logger.info(f"Python解释器路径: {sys.executable}")
    
    # 检查Python版本
    py_version = sys.version_info
    logger.info(f"Python版本: {py_version.major}.{py_version.minor}.{py_version.micro}")
    
    # 检查PyInstaller
    try:
        import PyInstaller
        logger.info(f"PyInstaller版本: {PyInstaller.__version__}")
    except ImportError:
        logger.error("PyInstaller未安装")
        logger.info("\n解决方案:")
        logger.info("1. 确保已激活正确的虚拟环境")
        logger.info("2. 使用以下命令安装PyInstaller:")
        logger.info(f"   {sys.executable} -m pip install pyinstaller")
        logger.info("3. 验证安装:")
        logger.info(f"   {sys.executable} -c \"import PyInstaller; print(PyInstaller.__version__)\"")
        return False
    
    # 检查必要文件
    required_files = ['ffmpeg.exe', 'ffprobe.exe', 'main.pyc_Source_Patcher.py']
    missing = [f for f in required_files if not os.path.exists(f)]
    if missing:
        logger.error(f"缺少必要文件: {missing}")
        return False
    
    logger.info("环境检查通过")
    return True

def run_pyinstaller():
    """执行PyInstaller打包"""
    logger.info("开始打包过程...")
    
    # 准备输出目录
    output_dir = "dist"
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # PyInstaller配置
        args = [
            'main.pyc_Source_Patcher.py',
            '--onefile',
            '--windowed',
            '--name=皇冠AB视频处理工具',
            '--add-data=ffmpeg.exe;.',
            '--add-data=ffprobe.exe;.',
            '--hidden-import=ttkbootstrap',
            '--hidden-import=pkg_resources.py2_warn',
            '--distpath', output_dir,
            '--workpath', 'build',
            '--specpath', 'build',
            '--log-level=DEBUG'
        ]
        
        # 执行打包
        logger.info(f"执行命令: pyinstaller {' '.join(args)}")
        result = subprocess.run(['pyinstaller'] + args, check=True, capture_output=True, text=True)
        
        # 记录输出
        with open('build_output.log', 'w') as f:
            f.write(result.stdout)
            if result.stderr:
                f.write("\nERRORS:\n")
                f.write(result.stderr)
        
        logger.info("打包过程完成")
        return True
        
    except subprocess.CalledProcessError as e:
        logger.error(f"打包失败: {e.stderr}")
        return False

def verify_build():
    """验证打包结果"""
    logger.info("验证打包结果...")
    exe_path = os.path.join("dist", "皇冠AB视频处理工具.exe")
    
    if not os.path.exists(exe_path):
        logger.error("未生成可执行文件")
        return False
    
    # 检查文件大小
    size = os.path.getsize(exe_path) / (1024*1024)  # MB
    logger.info(f"生成的可执行文件大小: {size:.2f} MB")
    
    # 检查依赖文件
    for f in ['ffmpeg.exe', 'ffprobe.exe']:
        if not os.path.exists(os.path.join("dist", f)):
            logger.warning(f"依赖文件 {f} 未复制到dist目录")
    
    logger.info("验证完成")
    return True

if __name__ == "__main__":
    logger.info("="*50)
    logger.info("开始调试打包过程")
    logger.info(f"当前目录: {os.getcwd()}")
    
    if not check_environment():
        sys.exit(1)
        
    if not run_pyinstaller():
        logger.error("打包过程失败，请查看build_debug.log和build_output.log")
        sys.exit(1)
        
    if not verify_build():
        logger.warning("打包验证发现问题，请检查输出")
        
    logger.info("调试过程结束")
    logger.info("="*50)
