import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import subprocess
import os
import threading
import time
class VideoProcessor:
    def __init__(self, root):
        self.root = root
        self.root.title("小鸡快跑视频处理工具")
        
        # 初始化变量
        self.input_video = ""
        self.output_video = ""
        self.temp_files = []
        self.batch_mode = False
        self.batch_files = []
        self.current_file_index = 0
        
        # UI布局
        self.create_widgets()
        
    def create_widgets(self):
        """创建GUI界面组件"""
        # 设置窗口初始尺寸和标题
        self.root.geometry("800x600")
        self.root.title("视频处理工具 - 小鸡快跑")
        self.root.resizable(False, False)
        
        # 主框架
        main_frame = ttk.Frame(self.root, padding=15)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 模式选择
        mode_frame = ttk.LabelFrame(main_frame, text="处理模式", padding=10)
        mode_frame.pack(fill=tk.X, pady=(0, 15))
        
        self.mode_var = tk.StringVar(value="single")
        ttk.Radiobutton(mode_frame, text="单文件模式", variable=self.mode_var, 
                      value="single", command=self.toggle_mode).grid(row=0, column=0, padx=10)
        ttk.Radiobutton(mode_frame, text="批量模式", variable=self.mode_var,
                      value="batch", command=self.toggle_mode).grid(row=0, column=1, padx=10)
        
        # 输入输出设置
        io_frame = ttk.Frame(main_frame)
        io_frame.pack(fill=tk.BOTH, expand=True)
        
        # 输入设置
        input_frame = ttk.LabelFrame(io_frame, text="输入设置", padding=10)
        input_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(input_frame, text="输入路径:").grid(row=0, column=0, sticky=tk.W)
        self.input_entry = ttk.Entry(input_frame)
        self.input_entry.grid(row=0, column=1, padx=5, sticky=tk.EW)
        ttk.Button(input_frame, text="浏览", command=self.select_input, width=10).grid(row=0, column=2, padx=(5,0))
        
        # 输出设置
        output_frame = ttk.LabelFrame(io_frame, text="输出设置", padding=10)
        output_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(output_frame, text="输出路径:").grid(row=0, column=0, sticky=tk.W)
        self.output_entry = ttk.Entry(output_frame)
        self.output_entry.grid(row=0, column=1, padx=5, sticky=tk.EW)
        ttk.Button(output_frame, text="浏览", command=self.select_output, width=10).grid(row=0, column=2, padx=(5,0))
        
        # 处理按钮
        btn_frame = ttk.Frame(io_frame)
        btn_frame.pack(fill=tk.X, pady=15)
        self.process_btn = ttk.Button(btn_frame, text="开始处理", command=self.process_video, style="Accent.TButton")
        self.process_btn.pack(ipady=5, ipadx=20)
        
        # 进度显示区域
        progress_frame = ttk.LabelFrame(io_frame, text="处理进度", padding=10)
        progress_frame.pack(fill=tk.X, pady=5)
        
        # 步骤显示
        self.step_var = tk.StringVar(value="等待开始处理...")
        ttk.Label(progress_frame, textvariable=self.step_var).pack(anchor=tk.W)
        
        # 进度条
        self.progress = ttk.Progressbar(progress_frame, orient="horizontal", length=400, mode="determinate")
        self.progress.pack(fill=tk.X, pady=5)
        
        # 百分比和状态显示
        status_frame = ttk.Frame(progress_frame)
        status_frame.pack(fill=tk.X)
        
        self.percent_var = tk.StringVar(value="0%")
        ttk.Label(status_frame, textvariable=self.percent_var).pack(side=tk.LEFT)
        
        self.status_var = tk.StringVar(value="准备就绪")
        ttk.Label(status_frame, textvariable=self.status_var).pack(side=tk.RIGHT)
        
        # 样式配置
        self.root.style = ttk.Style()
        self.root.style.configure("TFrame", background="#f0f0f0")
        self.root.style.configure("TLabelFrame", background="#f0f0f0", font=('微软雅黑', 10, 'bold'))
        self.root.style.configure("TLabel", background="#f0f0f0", font=('微软雅黑', 9))
        self.root.style.configure("TButton", font=('微软雅黑', 9), padding=5)
        self.root.style.configure("Accent.TButton", font=('微软雅黑', 10, 'bold'), 
                                foreground='black', background='#4CAF50', padding=8)
        self.root.style.configure("TRadiobutton", background="#f0f0f0", font=('微软雅黑', 9))
        self.root.style.map("Accent.TButton",
                          foreground=[('pressed', 'white'), ('active', 'white')],
                          background=[('pressed', '#45a049'), ('active', '#66BB6A')])
        self.root.style.configure("Status.TLabel", font=('微软雅黑', 9), foreground='#333')
        self.root.style.configure("success.Horizontal.TProgressbar", troughcolor='#e0e0e0', background='#4CAF50')
        
    def toggle_mode(self):
        """切换处理模式"""
        mode = self.mode_var.get()
        if mode == "single":
            self.batch_mode = False
            self.input_label.config(text="输入视频:")
            self.input_button.config(text="浏览")
        elif mode == "batch":
            self.batch_mode = True
            self.input_label.config(text="输入文件夹:")
            self.input_button.config(text="选择文件夹")

    def select_input(self):
        """选择输入视频文件或文件夹"""
        if self.batch_mode:
            dirpath = filedialog.askdirectory()
            if dirpath:
                self.input_entry.delete(0, tk.END)
                self.input_entry.insert(0, dirpath)
                # 获取文件夹中所有视频文件
                video_files = [f for f in os.listdir(dirpath) 
                             if f.lower().endswith(('.mp4', '.avi', '.mov'))]
                video_files.sort()  # 按文件名排序
                
                if self.batch_mode:
                    self.batch_files = video_files
                    self.current_file_index = 0
        else:
            filepath = filedialog.askopenfilename(filetypes=[("视频文件", "*.mp4 *.avi *.mov")])
            if filepath:
                self.input_entry.delete(0, tk.END)
                self.input_entry.insert(0, filepath)
                self.input_video = filepath
                # 自动设置输出路径
                if not self.output_entry.get():
                    dirname = os.path.dirname(filepath)
                    filename, ext = os.path.splitext(os.path.basename(filepath))
                    output_path = os.path.join(dirname, f"{filename}_斑马{ext}")
                    self.output_entry.insert(0, output_path)
                    self.output_video = output_path
    
    def select_output(self):
        """选择输出文件夹"""
        dirpath = filedialog.askdirectory()
        if dirpath:
            self.output_entry.delete(0, tk.END)
            self.output_entry.insert(0, dirpath)
            
            # 自动设置输出文件名
            if self.batch_mode and self.batch_files:
                filename = f"{os.path.splitext(self.batch_files[0])[0]}_斑马.mp4"
                self.output_video = os.path.join(dirpath, filename)
            elif hasattr(self, 'input_video') and self.input_video:
                filename = f"{os.path.splitext(os.path.basename(self.input_video))[0]}_斑马.mp4"
                self.output_video = os.path.join(dirpath, filename)
    

    
    def process_video(self):
        """执行视频处理"""
        if not self.validate_inputs():
            return
            
        self.status_var.set("正在处理视频...")
        self.progress["value"] = 0
        self.root.update()
        
        # 在新线程中执行处理
        threading.Thread(target=self._process_video, daemon=True).start()
    
    def validate_inputs(self):
        """验证输入参数"""
        if self.batch_mode:
            input_dir = self.input_entry.get()
            output_dir = self.output_entry.get()
            
            if not input_dir:
                messagebox.showerror("错误", "请选择输入文件夹")
                return False
                
            if not os.path.isdir(input_dir):
                messagebox.showerror("错误", "输入文件夹不存在")
                return False
                
            if not output_dir:
                messagebox.showerror("错误", "请指定输出文件夹")
                return False
                
            if not self.batch_files:
                messagebox.showerror("错误", "输入文件夹中没有视频文件")
                return False
                
            return True
        else:
            self.input_video = self.input_entry.get()
            self.output_video = self.output_entry.get()
            
            if not self.input_video:
                messagebox.showerror("错误", "请选择输入视频文件")
                return False
                
            if not os.path.exists(self.input_video):
                messagebox.showerror("错误", "输入视频文件不存在")
                return False
                
            if not self.output_video:
                messagebox.showerror("错误", "请指定输出视频文件")
                return False
                
            return True
    


    def _process_video(self):
        """处理视频"""
        if self.batch_mode:
            self._process_batch()
        else:
            self._process_single()

    def _process_single(self):
        """处理单个视频文件"""
        try:
            input_path = self.input_video
            output_path = self.output_video
            temp_dir = os.path.dirname(output_path)
            
            self._execute_five_steps(input_path, output_path, temp_dir)
            
            # 处理成功后清理临时文件
            self.clean_temp_files()
            
            self.progress["value"] = 100
            self.status_var.set(f"处理完成: {os.path.basename(output_path)}")
            
        except Exception as error:
            error_msg = f"处理失败: {str(error)}"
            self.status_var.set(error_msg)
            messagebox.showerror("错误", error_msg)

    def _process_batch(self):
        """批量处理视频文件(顺序执行)"""
        input_dir = self.input_entry.get()
        output_dir = self.output_entry.get()
        total_files = len(self.batch_files)
        
        for i, filename in enumerate(self.batch_files):
            try:
                self.current_file_index = i
                input_path = os.path.join(input_dir, filename)
                base_name = os.path.splitext(filename)[0]
                # 确保输出文件名唯一
                output_name = f"{base_name}_斑马.mp4"
                output_path = os.path.join(output_dir, output_name)
                counter = 1
                # 如果文件已存在，添加数字后缀
                while os.path.exists(output_path):
                    output_name = f"{base_name}_斑马_{counter}.mp4"
                    output_path = os.path.join(output_dir, output_name)
                    counter += 1
                
                self.status_var.set(f"正在处理 {i+1}/{total_files}: {filename}")
                self.progress["value"] = i * 100 / total_files
                self.root.update()
                
                # 执行三步处理
                self._execute_five_steps(input_path, output_path, output_dir)
                
                # 处理成功后清理临时文件
                self.clean_temp_files()
                
            except Exception as error:
                error_msg = f"处理失败于 {filename}: {str(error)}"
                self.status_var.set(error_msg)
                messagebox.showerror("错误", error_msg)
                return
                
        self.progress["value"] = 100
        self.status_var.set(f"批量处理完成，共处理 {total_files} 个文件")

    def _execute_five_steps(self, input_path, output_path, temp_dir):
        """执行处理步骤"""
        # 第一步：空处理(检测)
        cmd1 = [
            "ffmpeg", "-i", input_path,
            "-f", "null", "-"
        ]
        self.run_ffmpeg_command(cmd1, "视频检测")
        self.progress["value"] = 10
        self.root.update()
        
        # 第二步：生成第一个临时文件
        temp1 = os.path.join(temp_dir, "temp1_A.mp4")
        cmd2 = [
            "ffmpeg", "-y", "-i", input_path,
            "-c:v", "libx264", "-b:v", "3000k", "-maxrate:v:0", "3000k",
            "-bufsize:v:0", "3000k", "-x264opts", "nal-hrd=cbr",
            "-c:a", "copy", "-y", temp1
        ]
        self.temp_files.append(temp1)
        self.run_ffmpeg_command(cmd2, "生成临时文件1")
        self.progress["value"] = 30
        self.root.update()
        
        # 第三步：生成第二个临时文件
        temp2 = os.path.join(temp_dir, "temp2_A.mp4")
        cmd3 = [
            "ffmpeg", "-y", "-i", input_path,
            "-c:v:0", "libx264", "-b:v:0", "5000k", "-maxrate:v:0", "5000k",
            "-bufsize:v:0", "5000k", "-x264opts", "nal-hrd=cbr",
            "-bsf:v", "noise=amount=-20", "-c:a", "copy", "-y", temp2
        ]
        self.temp_files.append(temp2)
        self.run_ffmpeg_command(cmd3, "生成临时文件2")
        self.progress["value"] = 50
        self.root.update()
        
        # 第四步：合并临时文件
        interleaved = os.path.join(temp_dir, "AB_interleaved.mp4")
        print(f"开始合并视频: {temp1} 和 {temp2}")
        try:
            cmd4 = [
                "ffmpeg", "-y", "-i", temp1, "-i", temp2,
                "-filter_complex", "[0:v][0:v][1:v][1:v][1:v]interleave=nb_inputs=5",
                "-map", "0:a?", "-c:v", "libx264", "-preset", "fast",
                "-crf", "18", "-pix_fmt", "yuv420p", interleaved
            ]
            print("执行合并命令:", " ".join(cmd4))
            self.temp_files.append(interleaved)
            self.run_ffmpeg_command(cmd4, "合并视频")
            print("视频合并完成:", interleaved)
        except Exception as e:
            print(f"合并视频出错: {str(e)}")
            raise
        self.progress["value"] = 75
        self.root.update()
        
        # 第五步：最终输出
        cmd5 = [
            "ffmpeg", "-y", "-i", interleaved,
            "-c:v", "copy", "-movflags", "+faststart", "-f", "mp4", output_path
        ]
        self.run_ffmpeg_command(cmd5, "最终输出")
        self.progress["value"] = 100
        self.root.update()
    
    def run_ffmpeg_command(self, cmd, step_num):
        """执行FFmpeg命令并处理输出"""
        try:
            # 更新处理状态
            self.step_var.set(f"正在{step_num}...")
            self.root.update()
            
            # 使用UTF-8编码并隐藏命令窗口
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            process = subprocess.Popen(cmd, 
                                     stdout=subprocess.PIPE,
                                     stderr=subprocess.PIPE,
                                     bufsize=1,
                                     encoding='utf-8',
                                     errors='ignore',
                                     startupinfo=startupinfo,
                                     creationflags=subprocess.CREATE_NO_WINDOW)
            
            # 读取输出并更新进度
            for line in process.stderr:
                if "frame=" in line:
                    # 更新进度条和百分比显示
                    current_value = self.progress["value"]
                    if current_value < 90:  # 防止超过100%
                        self.progress["value"] = current_value + 1
                        self.percent_var.set(f"{int(self.progress['value'])}%")
                        self.step_var.set(f"正在{step_num}... {line.strip()}")
                        self.root.update()
            
            process.wait()
            
            if process.returncode != 0:
                error_output = process.stderr.read()
                raise Exception(f"FFmpeg命令执行失败 (步骤{step_num}): {error_output}")
                
        except Exception as error:
            error_msg = f"FFmpeg命令执行失败 (步骤{step_num}): {str(error)}"
            raise Exception(error_msg)
    

            
            self.progress["value"] = 100
            self.status_var.set(f"处理完成，共处理 {total_files} 个文件")

    def clean_temp_files(self):
        """清理临时文件"""
        for temp_file in self.temp_files:
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
            except:
                pass
        self.temp_files = []

if __name__ == "__main__":
    try:
        root = tk.Tk()
        app = VideoProcessor(root)
        print("GUI初始化完成，进入主循环...")
        root.mainloop()
    except Exception as e:
        print(f"程序运行出错: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        print("程序结束")