# Decompiled with PyLingual (https://pylingual.io)
# Internal filename: nightingale_system.py
# Bytecode version: 3.10.0rc2 (3439)
# Source timestamp: 1970-01-01 00:00:00 UTC (0)

import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import sys
import os
import os
import sys
import threading
import queue
import subprocess
import shutil
import time
from concurrent.futures import ThreadPoolExecutor

class NightingaleApp(tk.Tk):

    def __init__(self, ffmpeg_path, ffprobe_path):
        super().__init__()
        
        # 设置窗口图标
        if getattr(sys, 'frozen', False):
            base_path = os.path.dirname(sys.executable)
        else:
            base_path = os.path.dirname(os.path.abspath(__file__))
        icon_path = os.path.join(base_path, 'app.ico')
        if os.path.exists(icon_path):
            self.iconbitmap(icon_path)
            
        # 设置样式
        self.style = ttk.Style()
        self.style.theme_use('clam')
        self.style.configure('.', font=('Microsoft YaHei', 10))
        self.style.configure('TFrame', background='#f0f0f0')
        self.style.configure('TLabel', background='#f0f0f0')
        self.style.configure('TButton', padding=6, width=10)
        self.style.map('TButton',
            foreground=[('pressed', 'white'), ('active', 'white')],
            background=[('pressed', '#0052cc'), ('active', '#0066ff')]
        )
        self.style.configure('TProgressbar', thickness=20)
        self.style.configure('TEntry', padding=5, bordercolor='#ccc', focuscolor='#0066ff')
        
        # 窗口设置
        self.title('小钢炮1.0版本')
        self.geometry('800x600')
        self.minsize(700, 500)
        self.configure(bg='#f0f0f0')
        
        self.ffmpeg_path = ffmpeg_path
        self.ffprobe_path = ffprobe_path
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(2, weight=1)
        # 输入区域
        self.input_frame = ttk.Frame(self, padding=(15, 15))
        self.input_frame.grid(row=0, column=0, padx=15, pady=15, sticky='ew')
        self.input_frame.grid_columnconfigure(1, weight=1)
        self.video_a_label = ttk.Label(self.input_frame, text='视频A (主素材)')
        self.video_a_label.grid(row=0, column=0, padx=10, pady=5, sticky='w')
        self.video_a_path = ttk.Entry(self.input_frame)
        self.video_a_path.insert(0, '选择主视频文件...')
        self.video_a_path.grid(row=0, column=1, padx=10, pady=5, sticky='ew')
        self.video_a_button = ttk.Button(self.input_frame, text='选择', width=8, command=self.select_video_a)
        self.video_a_button.grid(row=0, column=2, padx=10, pady=5)
        self.video_b_label = ttk.Label(self.input_frame, text='视频B (插入素材)')
        self.video_b_label.grid(row=1, column=0, padx=10, pady=5, sticky='w')
        self.video_b_path = ttk.Entry(self.input_frame)
        self.video_b_path.insert(0, '选择要插入的视频文件...')
        self.video_b_path.grid(row=1, column=1, padx=10, pady=5, sticky='ew')
        self.video_b_button = ttk.Button(self.input_frame, text='选择', width=8, command=self.select_video_b)
        self.video_b_button.grid(row=1, column=2, padx=10, pady=5)
        # 输出区域
        self.output_frame = ttk.Frame(self, padding=(15, 10))
        self.output_frame.grid(row=1, column=0, padx=15, pady=(0, 15), sticky='ew')
        self.output_frame.grid_columnconfigure(1, weight=1)
        self.output_label = ttk.Label(self.output_frame, text='输出文件')
        self.output_label.grid(row=0, column=0, padx=10, pady=5, sticky='w')
        self.output_path = ttk.Entry(self.output_frame)
        self.output_path.insert(0, '选择输出视频文件路径...')
        self.output_path.grid(row=0, column=1, padx=10, pady=5, sticky='ew')
        self.output_button = ttk.Button(self.output_frame, text='保存为', width=8, command=self.select_output)
        self.output_button.grid(row=0, column=2, padx=10, pady=5)
        # 进度区域
        self.progress_frame = ttk.Frame(self, padding=(15, 15))
        self.progress_frame.grid(row=2, column=0, padx=15, pady=(15, 10), sticky='ew')
        self.progress_frame.grid_columnconfigure(0, weight=1)
        self.progress_bar = ttk.Progressbar(self.progress_frame, orient='horizontal', length=200, mode='determinate')
        self.progress_bar.grid(row=0, column=0, padx=10, pady=10, sticky='ew')
        self.progress_bar['value'] = 0
        self.progress_label = ttk.Label(self.progress_frame, text='0%')
        self.progress_label.grid(row=0, column=1, padx=10, pady=10)
        # 状态区域
        self.status_label = ttk.Label(self, text='准备就绪', font=('Microsoft YaHei', 10, 'bold'))
        self.status_label.grid(row=3, column=0, padx=15, pady=(0, 10), sticky='w')
        
        # 开始按钮
        self.start_button = ttk.Button(self, text='开始处理', command=self.start_processing_thread, style='Accent.TButton')
        self.start_button.grid(row=4, column=0, padx=15, pady=15)
        
        # 自定义按钮样式
        self.style.configure('Accent.TButton', 
                           foreground='white',
                           background='#0066ff',
                           font=('Microsoft YaHei', 10, 'bold'),
                           padding=8)
        self.style.map('Accent.TButton',
                     foreground=[('pressed', 'white'), ('active', 'white')],
                     background=[('pressed', '#0044cc'), ('active', '#0088ff')])
        self.progress_queue = queue.Queue()
        self.after(100, self.process_progress_queue)

    def process_progress_queue(self):
        try:
            while True:
                message = self.progress_queue.get_nowait()
                if isinstance(message, tuple):
                    progress, status_text = message
                    self.progress_bar['value'] = progress
                    self.progress_label.config(text=f'{int(progress)}%')
                    self.status_label.config(text=status_text)
                elif message == '__DONE__':
                    self.start_button.config(state='normal', text='开始处理')
                    self.status_label.config(text='处理成功完成！')
                    self.progress_bar['value'] = 100
                    self.progress_label.config(text='100%')
                    messagebox.showinfo('完成', '视频处理已成功完成！')
                elif message == '__ERROR__':
                    self.start_button.config(state='normal', text='开始处理')
                    self.status_label.config(text='处理失败！')
                    messagebox.showerror('错误', '处理过程中发生错误。')
        except queue.Empty:
            pass
        finally:
            pass
        self.after(100, self.process_progress_queue)

    def select_video_a(self):
        path = filedialog.askopenfilename(title='选择视频A', filetypes=[('视频文件', '*.mp4 *.avi *.mkv *.mov')])
        if path:
            self.video_a_path.delete(0, tk.END)
            self.video_a_path.insert(0, path)

    def select_video_b(self):
        path = filedialog.askopenfilename(title='选择视频B', filetypes=[('视频文件', '*.mp4 *.avi *.mkv *.mov')])
        if path:
            self.video_b_path.delete(0, tk.END)
            self.video_b_path.insert(0, path)

    def select_output(self):
        path = filedialog.asksaveasfilename(title='保存输出文件', filetypes=[('AV1 视频', '*.mp4')], defaultextension='.mp4')
        if path:
            self.output_path.delete(0, tk.END)
            self.output_path.insert(0, path)

    def start_processing_thread(self):
        video_a = self.video_a_path.get()
        video_b = self.video_b_path.get()
        output = self.output_path.get()
        if not all([video_a, video_b, output]):
            messagebox.showwarning('输入错误', '请确保已选择视频A、视频B和输出文件路径。')
            return
        self.start_button.config(state='disabled', text='处理中...')
        self.progress_bar['value'] = 0
        self.progress_label.config(text='0%')
        self.status_label.config(text='准备开始...')
        worker = ProcessingThread(video_a, video_b, output, self.progress_queue, self.ffmpeg_path, self.ffprobe_path)
        worker.start()

class ProcessingThread(threading.Thread):

    def __init__(self, video_a, video_b, output_path, progress_queue, ffmpeg_path, ffprobe_path):
        super().__init__()
        self.video_a = video_a
        self.video_b = video_b
        self.output_path = output_path
        self.progress_queue = progress_queue
        self.ffmpeg_path = ffmpeg_path
        self.ffprobe_path = ffprobe_path
        self.daemon = True

    def update_progress(self, value, text):
        self.progress_queue.put((value, text))

    def run_command(self, command, command_name):
        process = subprocess.Popen(command, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == 'win32' else 0)
        process.wait()
        return process.returncode

    def generate_b_frame_indices(self, max_frames):
        self.update_progress(45, '计算B帧插入位置...')
        count = 0
        skip_counter = 0
        current_frame = 10
        b_frames = []
        while current_frame <= max_frames:
            if skip_counter == 5:
                current_frame += 70
                skip_counter = 0
                if current_frame > max_frames:
                    break
            b_frames.append(current_frame)
            count += 1
            skip_counter += 1
            current_frame += 10
        self.update_progress(46, f'已生成 {len(b_frames)} 个B帧插入位置。')
        return b_frames

    def run(self):
        temp_dir = f'temp_nightingale_{int(time.time())}'
        temp_a_frames_dir = os.path.join(temp_dir, 'video_a_frames')
        temp_b_frames_dir = os.path.join(temp_dir, 'video_b_frames')
        final_frames_dir = os.path.join(temp_dir, 'final_frames')
        try:
            self.update_progress(0, '开始处理...')
            os.makedirs(temp_a_frames_dir, exist_ok=True)
            os.makedirs(temp_b_frames_dir, exist_ok=True)
            os.makedirs(final_frames_dir, exist_ok=True)
            self.update_progress(1, '创建临时目录...')
            self.update_progress(2, '获取视频A信息...')
            cmd_duration = [self.ffprobe_path, '-v', 'error', '-show_entries', 'format=duration', '-of', 'default=noprint_wrappers=1:nokey=1', self.video_a]
            process_duration = subprocess.run(cmd_duration, capture_output=True, text=True)
            if process_duration.returncode != 0 or not process_duration.stdout.strip():
                raise Exception('获取视频A时长失败')
            duration_a = float(process_duration.stdout.strip())
            self.update_progress(3, f'视频A时长: {duration_a:.2f} 秒')
            cmd_fps = [self.ffprobe_path, '-v', 'error', '-select_streams', 'v:0', '-show_entries', 'stream=r_frame_rate', '-of', 'default=noprint_wrappers=1:nokey=1', self.video_a]
            process_fps = subprocess.run(cmd_fps, capture_output=True, text=True)
            if process_fps.returncode != 0 or not process_fps.stdout.strip() or '/' not in process_fps.stdout.strip():
                raise Exception('获取视频A帧率失败')
            original_framerate_str = process_fps.stdout.strip()
            original_framerate = eval(original_framerate_str)
            self.update_progress(4, f'视频A原始帧率: {original_framerate} fps ({original_framerate_str})')
            total_output_frames = int(duration_a * 60)
            self.update_progress(5, f'最终输出视频 (60fps) 总帧数: {total_output_frames}')
            self.update_progress(15, '提取视频A的帧...')
            if self.run_command([self.ffmpeg_path, '-i', self.video_a, os.path.join(temp_a_frames_dir, '%d.png')], 'Extract A') != 0:
                raise Exception('提取视频A帧失败')
            self.update_progress(35, '提取视频B的帧...')
            if self.run_command([self.ffmpeg_path, '-i', self.video_b, os.path.join(temp_b_frames_dir, '%d.png')], 'Extract B') != 0:
                raise Exception('提取视频B帧失败')
            self.update_progress(40, '生成B帧插入位置...')
            b_frame_indices = self.generate_b_frame_indices(total_output_frames)
            b_frame_indices_set = set(b_frame_indices)
            b_frame_source_map = {frame_num: i + 1 for i, frame_num in enumerate(b_frame_indices)}
            self.update_progress(41, f'将在 {len(b_frame_indices)} 个位置插入B帧。')
            self.update_progress(42, '开始逐帧构建最终序列...')
            last_reported_progress = -1
            for i in range(1, total_output_frames + 1):
                dest_path = os.path.join(final_frames_dir, f'{i}.png')
                if i in b_frame_indices_set:
                    b_source_index = b_frame_source_map[i]
                    src_path = os.path.join(temp_b_frames_dir, f'{b_source_index}.png')
                    if os.path.exists(src_path):
                        shutil.copyfile(src_path, dest_path)
                    else:
                        pass
                else:
                    a_source_index = int((i - 1) * original_framerate / 60.0) + 1
                    src_path = os.path.join(temp_a_frames_dir, f'{a_source_index}.png')
                    if os.path.exists(src_path):
                        shutil.copyfile(src_path, dest_path)
                    elif False:
                        pass
                progress = 46 + i / total_output_frames * 40
                if int(progress) > last_reported_progress:
                    last_reported_progress = int(progress)
                    self.update_progress(progress, f'重建帧序列: {i} / {total_output_frames}')
            self.update_progress(80, '合成中间视频C...')
            self.update_progress(81, '使用SVT-AV1进行视频合成...')
            output_dir = os.path.dirname(self.output_path)
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)
            temp_video_c_path = os.path.join(temp_dir, 'temp_video_c.mp4')
            encode_cmd = [self.ffmpeg_path, '-framerate', '60', '-i', os.path.join(final_frames_dir, '%d.png'), '-i', self.video_a, '-map', '0:v', '-map', '1:a', '-c:a', 'copy', '-c:v', 'libsvtav1', '-crf', '40', '-preset', '12', '-vf', 'scale=1080:2334', '-y', temp_video_c_path]
            if self.run_command(encode_cmd, 'Encode C') != 0:
                raise Exception('中间视频(C)编码失败')
            self.update_progress(85, '中间视频C合成完毕。')
            self.update_progress(86, '映射到黑色时间轴...')
            self.update_progress(87, f'将视频C映射到黑底视频上，输出到: {self.output_path}')
            duration_str = str(duration_a)
            resolution_str = '1080x2334'
            remap_cmd = [self.ffmpeg_path, '-i', temp_video_c_path, '-f', 'lavfi', '-i', f'color=c=black:s={resolution_str}:r=60:d={duration_str}', '-filter_complex', '[1:v][0:v]overlay=0:0:shortest=1[outv]', '-map', '[outv]', '-map', '0:a', '-c:a', 'copy', '-c:v', 'libsvtav1', '-crf', '40', '-preset', '12', '-y', self.output_path]
            if self.run_command(remap_cmd, 'Remap Final') != 0:
                raise Exception('最终视频映射失败')
            self.update_progress(100, '处理完成！')
            self.progress_queue.put('__DONE__')
        except Exception as e:
            self.progress_queue.put('__ERROR__')

def find_ffmpeg():
    if getattr(sys, 'frozen', False):
        base_path = os.path.dirname(sys.executable)
    else:
        base_path = os.path.dirname(os.path.abspath(__file__))
    ffmpeg_path = os.path.join(base_path, 'ffmpeg.exe' if sys.platform == 'win32' else 'ffmpeg')
    ffprobe_path = os.path.join(base_path, 'ffprobe.exe' if sys.platform == 'win32' else 'ffprobe')
    if os.path.exists(ffmpeg_path) and os.path.exists(ffprobe_path):
        return (ffmpeg_path, ffprobe_path)
    ffmpeg_path = shutil.which('ffmpeg')
    ffprobe_path = shutil.which('ffprobe')
    if ffmpeg_path and ffprobe_path:
        return (ffmpeg_path, ffprobe_path)
    return (None, None)
if __name__ == '__main__':

    ffmpeg, ffprobe = find_ffmpeg()
    if not ffmpeg:
        messagebox.showerror('依赖缺失', '未找到 ffmpeg 和 ffprobe。\n请将它们放置在程序目录下，或确保它们在系统的PATH环境变量中。')
    app = NightingaleApp(ffmpeg, ffprobe)
    app.mainloop()