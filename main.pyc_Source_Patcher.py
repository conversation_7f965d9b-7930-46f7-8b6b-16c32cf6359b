# Decompiled with PyLingual (https://pylingual.io)
# Internal filename: 皇冠.py
# Bytecode version: 3.11a7e (3495)
# Source timestamp: 1970-01-01 00:00:00 UTC (0)

import os
import sys
import time
import tkinter as tk
from tkinter import filedialog, messagebox
import subprocess
import threading
import random
import json
import shutil
VERSION = '1.0.1'
APP_NAME = '皇冠AB视频处理工具'
AUTHOR = '一号团队'
USING_TTK_BOOTSTRAP = False
try:
    import ttkbootstrap as ttk
    from ttkbootstrap import Style
    from ttkbootstrap.constants import *
    from ttkbootstrap.toast import ToastNotification
    from ttkbootstrap.dialogs import Messagebox
    USING_TTK_BOOTSTRAP = True
except ImportError as e:
    print('❌ 缺少必需的ttkbootstrap库')
    print('==================================================')
    print('错误详情:')
    print(f'  {e}')
    print()
    print('解决方案:')
    print('1. 安装ttkbootstrap:')
    print('   pip install ttkbootstrap')
    print()
    print('2. 或者升级到最新版本:')
    print('   pip install --upgrade ttkbootstrap')
    print()
    print('3. 如果使用虚拟环境，请确保在正确的环境中安装')
    print('==================================================')
    try:
        user_input = input('是否尝试自动安装ttkbootstrap? (y/n): ').lower().strip()
        if user_input in ['y', 'yes', '是']:
            print('正在安装ttkbootstrap...')
            import subprocess
            result = subprocess.run([sys.executable, '-m', 'pip', 'install', 'ttkbootstrap'], capture_output=True, text=True)
            if result.returncode == 0:
                print('✅ 安装成功！请重新运行程序。')
            else:
                print('❌ 安装失败，请手动安装:')
                print('   pip install ttkbootstrap')
        else:
            print('请手动安装ttkbootstrap后重新运行程序')
    except KeyboardInterrupt:
        print('\n用户取消操作')
    except Exception:
        print('请手动安装ttkbootstrap后重新运行程序')
    sys.exit(1)

class DragonBoatABProcessor:
    def __init__(self, root):
        self.root = root
        self.root.title(f'{APP_NAME} v{VERSION} | {AUTHOR}出品')
        self.root.geometry('1100x950')
        if USING_TTK_BOOTSTRAP:
            try:
                self.style = ttk.Style(theme='flatly')
            except Exception as e:
                pass
        self.input_video_var = tk.StringVar()
        self.material_video_var = tk.StringVar()
        self.output_path_var = tk.StringVar()
        self.original_duration_var = tk.DoubleVar(value=8.0)
        self.batch_input_folder_var = tk.StringVar()
        self.batch_material_folder_var = tk.StringVar()
        self.batch_output_folder_var = tk.StringVar()
        self.processing_mode = tk.StringVar(value='single')
        self.delete_material_var = tk.BooleanVar(value=True)
        self.generation_count_var = tk.IntVar(value=1)
        self.root.after(100, self.setup_ui)

    def setup_ui(self):
        """设置用户界面"""
        main_container = ttk.Frame(self.root)
        main_container.pack(fill=tk.BOTH, expand=True, padx=25, pady=25)
        header_frame = ttk.Frame(main_container)
        header_frame.pack(fill=tk.X, pady=(0, 20))
        title_label = ttk.Label(header_frame, text=f'🏆 皇冠 - AB视频处理工具 v{VERSION}', font=('Microsoft YaHei UI', 18, 'bold'), bootstyle='primary' if USING_TTK_BOOTSTRAP else None)
        title_label.pack()
        subtitle_label = ttk.Label(header_frame, text=f'{AUTHOR}出品 | 专业的视频AB测试处理工具 | 支持单文件和批量处理', font=('Microsoft YaHei UI', 10), bootstyle='secondary' if USING_TTK_BOOTSTRAP else None)
        subtitle_label.pack(pady=(5, 0))
        contact_label = ttk.Label(header_frame, text='📧 技术支持: <EMAIL>', font=('Microsoft YaHei UI', 9), bootstyle='info' if USING_TTK_BOOTSTRAP else None)
        contact_label.pack(pady=(2, 0))
        mode_frame = ttk.LabelFrame(main_container, text=' 🎯 处理模式 ', padding=15, bootstyle='info' if USING_TTK_BOOTSTRAP else None)
        mode_frame.pack(fill=tk.X, pady=(0, 15))
        mode_buttons_frame = ttk.Frame(mode_frame)
        mode_buttons_frame.pack()
        single_radio = ttk.Radiobutton(mode_buttons_frame, text='📄 单文件处理', variable=self.processing_mode, value='single', command=self.update_ui_mode, bootstyle='primary-outline-toolbutton' if USING_TTK_BOOTSTRAP else None)
        single_radio.pack(side=tk.LEFT, padx=(0, 20))
        batch_radio = ttk.Radiobutton(mode_buttons_frame, text='📁 文件夹批量处理', variable=self.processing_mode, value='batch', command=self.update_ui_mode, bootstyle='primary-outline-toolbutton' if USING_TTK_BOOTSTRAP else None)
        batch_radio.pack(side=tk.LEFT)
        options_frame = ttk.LabelFrame(main_container, text=' ⚙️ 处理选项 ', padding=15, bootstyle='secondary' if USING_TTK_BOOTSTRAP else None)
        options_frame.pack(fill=tk.X, pady=(15, 0))
        delete_material_check = ttk.Checkbutton(options_frame, text='🗑️ 处理完成后自动删除素材视频（节省磁盘空间）', variable=self.delete_material_var, bootstyle='success-round-toggle' if USING_TTK_BOOTSTRAP else None)
        delete_material_check.pack(anchor='w')
        generation_frame = ttk.Frame(options_frame)
        generation_frame.pack(anchor='w', pady=(10, 0))
        generation_label = ttk.Label(generation_frame, text='🎯 每个原视频生成数量:', font=('Microsoft YaHei UI', 10))
        generation_label.pack(side=tk.LEFT)
        generation_spinbox = ttk.Spinbox(generation_frame, from_=1, to=10, width=5, textvariable=self.generation_count_var, bootstyle='info' if USING_TTK_BOOTSTRAP else None)
        generation_spinbox.pack(side=tk.LEFT, padx=(10, 0))
        generation_note_label = ttk.Label(generation_frame, text='（批量处理时，每个原视频将与不同素材配对生成指定数量的视频）', font=('Microsoft YaHei UI', 9), bootstyle='secondary' if USING_TTK_BOOTSTRAP else None)
        generation_note_label.pack(side=tk.LEFT, padx=(10, 0))
        option_note = ttk.Label(options_frame, text='💡 提示：启用删除选项将在视频处理完成后自动删除对应的素材视频文件', font=('Microsoft YaHei UI', 9), bootstyle='secondary' if USING_TTK_BOOTSTRAP else None)
        option_note.pack(anchor='w', pady=(5, 0))
        self.single_frame = ttk.Frame(main_container)
        self.create_single_mode_ui(self.single_frame)
        self.batch_frame = ttk.Frame(main_container)
        self.create_batch_mode_ui(self.batch_frame)
        button_frame = ttk.Frame(main_container)
        self.button_frame = button_frame
        button_frame.pack(fill=tk.X, pady=(20, 0))
        left_buttons = ttk.Frame(button_frame)
        left_buttons.pack(side=tk.LEFT)
        self.start_button = ttk.Button(left_buttons, text='🚀 开始处理', command=self.start_processing, width=15, bootstyle='success' if USING_TTK_BOOTSTRAP else None)
        self.start_button.pack(side=tk.LEFT, padx=(0, 15))
        clear_button = ttk.Button(left_buttons, text='🧹 清空日志', command=self.clear_log, width=12, bootstyle='secondary' if USING_TTK_BOOTSTRAP else None)
        clear_button.pack(side=tk.LEFT)
        progress_frame = ttk.Frame(main_container)
        progress_frame.pack(fill=tk.X, padx=10, pady=(10, 5))
        self.progress_var = tk.DoubleVar()
        if USING_TTK_BOOTSTRAP:
            self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100, bootstyle='success-striped', length=400)
        else:
            self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(fill='x', pady=2)
        self.progress_label = ttk.Label(progress_frame, text='就绪')
        self.progress_label.pack(pady=2)
        right_buttons = ttk.Frame(button_frame)
        right_buttons.pack(side=tk.RIGHT)
        self.status_label = ttk.Label(right_buttons, text='就绪', font=('Microsoft YaHei UI', 10, 'bold'), bootstyle='info' if USING_TTK_BOOTSTRAP else None)
        self.status_label.pack()
        log_frame = ttk.LabelFrame(main_container, text=' 📋 处理日志 ', padding=15, bootstyle='light' if USING_TTK_BOOTSTRAP else None)
        log_frame.pack(fill=tk.BOTH, expand=True, pady=(20, 0))
        log_container = ttk.Frame(log_frame)
        log_container.pack(fill=tk.BOTH, expand=True)
        if USING_TTK_BOOTSTRAP:
            self.log_text = tk.Text(log_container, wrap=tk.WORD, font=('Consolas', 10), height=12, bg='#ffffff', fg='#212529', insertbackground='#212529', selectbackground='#e3f2fd', selectforeground='#212529', relief='flat', padx=12, pady=12)
        else:
            self.log_text = tk.Text(log_container, wrap=tk.WORD, font=('Consolas', 10), height=12, relief='sunken', padx=10, pady=10)
        log_scrollbar = ttk.Scrollbar(log_container, orient=tk.VERTICAL, command=self.log_text.yview, bootstyle='light-round' if USING_TTK_BOOTSTRAP else None)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 8))
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.update_ui_mode()
        self.add_log(f'🏆 {APP_NAME} v{VERSION} 已启动')
        self.add_log(f'👥 {AUTHOR}出品 - 专业视频处理工具')
        self.add_log('📧 技术支持: <EMAIL>')
        self.add_log('📖 请选择处理模式并设置相关文件路径')
        self.setup_default_paths()

    def setup_default_paths(self):
        """设置默认配置"""
        self.add_log('📁 临时文件将在输出文件所在目录创建')

    def create_single_mode_ui(self, parent):
        """创建单文件处理界面"""
        input_frame = ttk.LabelFrame(parent, text=' 📹 选择原视频 ', padding=15, bootstyle='primary' if USING_TTK_BOOTSTRAP else None)
        input_frame.pack(fill=tk.X, pady=(0, 10))
        input_container = ttk.Frame(input_frame)
        input_container.pack(fill=tk.X)
        input_entry = ttk.Entry(input_container, textvariable=self.input_video_var, font=('Microsoft YaHei UI', 10), bootstyle='primary' if USING_TTK_BOOTSTRAP else None)
        input_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 15))
        input_button = ttk.Button(input_container, text='📂 浏览', command=self.choose_input_video, width=10, bootstyle='primary-outline' if USING_TTK_BOOTSTRAP else None)
        input_button.pack(side=tk.RIGHT)
        material_frame = ttk.LabelFrame(parent, text=' 🎬 选择素材视频 ', padding=15, bootstyle='secondary' if USING_TTK_BOOTSTRAP else None)
        material_frame.pack(fill=tk.X, pady=(0, 10))
        material_container = ttk.Frame(material_frame)
        material_container.pack(fill=tk.X)
        material_entry = ttk.Entry(material_container, textvariable=self.material_video_var, font=('Microsoft YaHei UI', 10), bootstyle='secondary' if USING_TTK_BOOTSTRAP else None)
        material_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 15))
        material_button = ttk.Button(material_container, text='📂 浏览', command=self.choose_material_video, width=10, bootstyle='secondary-outline' if USING_TTK_BOOTSTRAP else None)
        material_button.pack(side=tk.RIGHT)
        output_frame = ttk.LabelFrame(parent, text=' 💾 选择输出文件 ', padding=15, bootstyle='warning' if USING_TTK_BOOTSTRAP else None)
        output_frame.pack(fill=tk.X, pady=(0, 10))
        output_container = ttk.Frame(output_frame)
        output_container.pack(fill=tk.X)
        output_entry = ttk.Entry(output_container, textvariable=self.output_path_var, font=('Microsoft YaHei UI', 10), bootstyle='warning' if USING_TTK_BOOTSTRAP else None)
        output_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 15))
        output_button = ttk.Button(output_container, text='💾 保存', command=self.choose_output_path, width=10, bootstyle='warning-outline' if USING_TTK_BOOTSTRAP else None)
        output_button.pack(side=tk.RIGHT)

    def create_batch_mode_ui(self, parent):
        """创建批量处理界面"""
        input_folder_frame = ttk.LabelFrame(parent, text=' 📁 选择原视频文件夹 ', padding=15, bootstyle='primary' if USING_TTK_BOOTSTRAP else None)
        input_folder_frame.pack(fill=tk.X, pady=(0, 10))
        input_folder_container = ttk.Frame(input_folder_frame)
        input_folder_container.pack(fill=tk.X)
        input_folder_entry = ttk.Entry(input_folder_container, textvariable=self.batch_input_folder_var, font=('Microsoft YaHei UI', 10), bootstyle='primary' if USING_TTK_BOOTSTRAP else None)
        input_folder_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 15))
        input_folder_button = ttk.Button(input_folder_container, text='📂 浏览', command=self.choose_batch_input_folder, width=10, bootstyle='primary-outline' if USING_TTK_BOOTSTRAP else None)
        input_folder_button.pack(side=tk.RIGHT)
        material_frame = ttk.LabelFrame(parent, text=' 🎬 选择素材视频文件夹 ', padding=15, bootstyle='secondary' if USING_TTK_BOOTSTRAP else None)
        material_frame.pack(fill=tk.X, pady=(0, 10))
        material_container = ttk.Frame(material_frame)
        material_container.pack(fill=tk.X)
        material_entry = ttk.Entry(material_container, textvariable=self.batch_material_folder_var, font=('Microsoft YaHei UI', 10), bootstyle='secondary' if USING_TTK_BOOTSTRAP else None)
        material_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 15))
        material_button = ttk.Button(material_container, text='📂 浏览', command=self.choose_batch_material_folder, width=10, bootstyle='secondary-outline' if USING_TTK_BOOTSTRAP else None)
        material_button.pack(side=tk.RIGHT)
        output_folder_frame = ttk.LabelFrame(parent, text=' 💾 选择输出文件夹 ', padding=15, bootstyle='warning' if USING_TTK_BOOTSTRAP else None)
        output_folder_frame.pack(fill=tk.X, pady=(0, 10))
        output_folder_container = ttk.Frame(output_folder_frame)
        output_folder_container.pack(fill=tk.X)
        output_folder_entry = ttk.Entry(output_folder_container, textvariable=self.batch_output_folder_var, font=('Microsoft YaHei UI', 10), bootstyle='warning' if USING_TTK_BOOTSTRAP else None)
        output_folder_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 15))
        output_folder_button = ttk.Button(output_folder_container, text='📂 浏览', command=self.choose_batch_output_folder, width=10, bootstyle='warning-outline' if USING_TTK_BOOTSTRAP else None)
        output_folder_button.pack(side=tk.RIGHT)

    def update_ui_mode(self):
        """更新UI模式"""
        if self.processing_mode.get() == 'single':
            self.batch_frame.pack_forget()
            self.single_frame.pack(fill=tk.X, pady=(15, 0), before=self.button_frame)
            self.update_status('单文件处理模式', 'info')
        else:
            self.single_frame.pack_forget()
            self.batch_frame.pack(fill=tk.X, pady=(15, 0), before=self.button_frame)
            self.update_status('批量处理模式', 'warning')

    def update_status(self, message, style='info'):
        """更新状态显示"""
        if hasattr(self, 'status_label'):
            if USING_TTK_BOOTSTRAP:
                self.status_label.config(text=message, bootstyle=style)
            else:
                self.status_label.config(text=message)

    def update_progress(self, value, message=''):
        """更新进度条"""
        if hasattr(self, 'progress_var'):
            self.progress_var.set(value)
        if hasattr(self, 'progress_label') and message:
            self.progress_label.config(text=message)
        self.root.update_idletasks()

    def show_toast(self, title, message, type='info'):
        """显示现代化提示消息"""
        if USING_TTK_BOOTSTRAP:
            try:
                toast = ToastNotification(title=title, message=message, duration=3000, bootstyle=type)
                toast.show_toast()
            except Exception as e:
                print(f'Toast显示失败: {e}')
                try:
                    Messagebox.show_info(message, title)
                except:
                    messagebox.showinfo(title, message)
        messagebox.showinfo(title, message)

    def show_modern_messagebox(self, title, message, type='info'):
        """显示现代化消息框"""
        if USING_TTK_BOOTSTRAP:
            try:
                if type == 'error':
                    Messagebox.show_error(message, title)
                elif type == 'warning':
                    Messagebox.show_warning(message, title)
                else:
                    Messagebox.show_info(message, title)
            except Exception as e:
                print(f'Messagebox显示失败: {e}')
                if type == 'error':
                    messagebox.showerror(title, message)
                elif type == 'warning':
                    messagebox.showwarning(title, message)
                else:
                    messagebox.showinfo(title, message)
        else:
            if type == 'error':
                messagebox.showerror(title, message)
            elif type == 'warning':
                messagebox.showwarning(title, message)
            else:
                messagebox.showinfo(title, message)

    def choose_batch_input_folder(self):
        """选择批量处理输入文件夹"""
        folder = filedialog.askdirectory(title='选择包含原视频的文件夹')
        if folder:
            self.batch_input_folder_var.set(folder)
            self.add_log(f'📁 已选择输入文件夹: {os.path.basename(folder)}')
            video_files = self.get_video_files(folder)
            self.add_log(f'🔍 发现 {len(video_files)} 个视频文件')
            self.update_status(f'已选择文件夹，发现 {len(video_files)} 个视频', 'success')
            if video_files:
                self.show_toast('文件夹选择', f'发现 {len(video_files)} 个视频文件', 'success')

    def choose_batch_material_folder(self):
        """选择批量处理素材视频文件夹"""
        folder = filedialog.askdirectory(title='选择素材视频文件夹')
        if folder:
            self.batch_material_folder_var.set(folder)
            self.add_log(f'🎬 已选择素材视频文件夹: {os.path.basename(folder)}')
            video_files = self.get_video_files(folder)
            self.add_log(f'🔍 发现 {len(video_files)} 个素材视频文件')
            self.update_status(f'已选择素材文件夹，发现 {len(video_files)} 个视频', 'success')
            if video_files:
                self.show_toast('素材文件夹选择', f'发现 {len(video_files)} 个素材视频文件', 'success')

    def choose_batch_output_folder(self):
        """选择批量处理输出文件夹"""
        folder = filedialog.askdirectory(title='选择输出文件夹')
        if folder:
            self.batch_output_folder_var.set(folder)
            self.add_log(f'💾 已选择输出文件夹: {os.path.basename(folder)}')
            self.update_status('已选择输出文件夹', 'success')

    def get_video_files(self, directory):
        """获取目录中的视频文件"""
        video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.m4v']
        video_files = []
        for root, _, files in os.walk(directory):
            for file in files:
                if any((file.lower().endswith(ext) for ext in video_extensions)):
                    video_files.append(os.path.join(root, file))
        return video_files

    def choose_input_video(self):
        """选择输入视频"""
        filename = filedialog.askopenfilename(title='选择原视频', filetypes=[('视频文件', '*.mp4 *.avi *.mov *.mkv'), ('所有文件', '*.*')])
        if filename:
            self.input_video_var.set(filename)
            self.add_log(f'📹 已选择原视频: {os.path.basename(filename)}')
            self.update_status('正在分析视频...', 'warning')
            self.analyze_input_video(filename)

    def analyze_input_video(self, video_path):
        """分析输入视频信息"""
        try:
            cmd = [self.get_ffprobe_path(), '-v', 'quiet', '-print_format', 'json', '-show_format', '-show_streams', video_path]
            result = self.safe_subprocess_run(cmd)
            if result.returncode == 0:
                import json
                data = json.loads(result.stdout)
                video_stream = None
                for stream in data['streams']:
                    if stream['codec_type'] == 'video':
                        video_stream = stream
                        break
                if video_stream:
                    width = video_stream.get('width', 'unknown')
                    height = video_stream.get('height', 'unknown')
                    duration = float(data['format'].get('duration', 8.0))
                    self.original_duration_var.set(duration)
                    self.add_log(f'📊 视频信息: {width}x{height}, 时长: {duration:.2f}秒')
                    self.update_status(f'视频已分析: {width}x{height}', 'success')
                    self.show_toast('视频分析', f'分辨率: {width}x{height}\n时长: {duration:.2f}秒', 'success')
                else:
                    self.add_log('⚠️ 未找到有效的视频流')
                    self.update_status('视频分析失败', 'danger')
            else:
                self.add_log(f'❌ 分析视频失败: {result.stderr}')
                self.update_status('视频分析失败', 'danger')
        except Exception as e:
            self.add_log(f'❌ 分析视频时出错: {str(e)}')
            self.update_status('视频分析出错', 'danger')

    def choose_material_video(self):
        """选择素材视频"""
        filename = filedialog.askopenfilename(title='选择素材视频', filetypes=[('视频文件', '*.mp4 *.avi *.mov *.mkv'), ('所有文件', '*.*')])
        if filename:
            self.material_video_var.set(filename)
            self.add_log(f'🎬 已选择素材视频: {os.path.basename(filename)}')
            self.update_status('已选择素材视频', 'secondary')

    def choose_output_path(self):
        """选择输出路径"""
        filename = filedialog.asksaveasfilename(title='保存输出文件', defaultextension='.mp4', filetypes=[('MP4视频', '*.mp4'), ('所有文件', '*.*')])
        if filename:
            self.output_path_var.set(filename)
            self.add_log(f'💾 已设置输出路径: {os.path.basename(filename)}')
            self.update_status('已设置输出路径', 'warning')

    def add_log(self, message):
        """添加日志"""
        current_time = time.strftime('%H:%M:%S')
        log_message = f'[{current_time}] {message}\n'
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        self.add_log('🧹 日志已清空')
        self.update_status('日志已清空', 'secondary')

    def safe_subprocess_run(self, cmd, **kwargs):
        """安全的subprocess运行，处理编码问题"""
        try:
            self.add_log(f'🔧 执行命令: {" ".join(cmd)}')
            default_kwargs = {
                'capture_output': True, 
                'text': True, 
                'encoding': 'utf-8', 
                'errors': 'replace', 
                'creationflags': subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0,
                'timeout': 3600  # 1小时超时
            }
            default_kwargs.update(kwargs)
            start_time = time.time()
            result = subprocess.run(cmd, **default_kwargs)
            elapsed = time.time() - start_time
            self.add_log(f'✅ 命令执行完成 (耗时: {elapsed:.2f}s)')
            return result
        except subprocess.TimeoutExpired:
            self.add_log(f'❌ 命令执行超时: {" ".join(cmd)}')
            raise
        except UnicodeDecodeError:
            try:
                self.add_log('🔄 尝试GBK编码解码...')
                kwargs_fallback = kwargs.copy()
                kwargs_fallback.update({
                    'capture_output': True, 
                    'text': True, 
                    'encoding': 'gbk', 
                    'errors': 'replace', 
                    'creationflags': subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
                })
                return subprocess.run(cmd, **kwargs_fallback)
            except Exception as e:
                self.add_log(f'⚠️ GBK解码失败: {str(e)}')
                kwargs_binary = kwargs.copy()
                kwargs_binary.update({
                    'capture_output': True, 
                    'text': False, 
                    'creationflags': subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
                })
                result = subprocess.run(cmd, **kwargs_binary)
                self.add_log('🔄 使用二进制模式获取输出')
                if result.stdout:
                    try:
                        result.stdout = result.stdout.decode('utf-8', errors='replace')
                    except:
                        result.stdout = result.stdout.decode('gbk', errors='replace')
                if result.stderr:
                    try:
                        result.stderr = result.stderr.decode('utf-8', errors='replace')
                    except:
                        result.stderr = result.stderr.decode('gbk', errors='replace')
                return result

    def get_ffmpeg_path(self):
        """获取FFmpeg路径 - 确保一致性"""
        if hasattr(self, '_cached_ffmpeg_path'):
            return self._cached_ffmpeg_path
        possible_paths = [os.path.join(os.path.dirname(os.path.abspath(__file__)), 'ffmpeg.exe'), 'ffmpeg.exe', 'ffmpeg', os.path.join(os.path.expanduser('~'), 'Desktop', '五花AB5', 'ffmpeg', 'ffmpeg.exe'), 'C:/Users/<USER>/Desktop/五花AB5/ffmpeg/ffmpeg.exe', os.path.join(os.path.dirname(os.path.abspath(__file__)), 'ffmpeg', 'bin', 'ffmpeg.exe')]
        for path in possible_paths:
            try:
                result = self.safe_subprocess_run([path, '-version'])
                if result.returncode == 0:
                    self._cached_ffmpeg_path = path
                    self.add_log('✅ FFmpeg引擎已就绪')
                    return path
            except:
                continue
        else:
            self._cached_ffmpeg_path = 'ffmpeg'
            return 'ffmpeg'

    def get_ffprobe_path(self):
        """获取FFprobe路径"""
        possible_paths = [os.path.join(os.path.dirname(os.path.abspath(__file__)), 'ffprobe.exe'), 'ffprobe.exe', 'ffprobe', os.path.join(os.path.expanduser('~'), 'Desktop', '五花AB5', 'ffmpeg', 'ffprobe.exe'), 'C:/Users/<USER>/Desktop/五花AB5/ffmpeg/ffprobe.exe', os.path.join(os.path.dirname(os.path.abspath(__file__)), 'ffmpeg', 'bin', 'ffprobe.exe')]
        for path in possible_paths:
            try:
                result = self.safe_subprocess_run([path, '-version'])
                if result.returncode == 0:
                    return path
            except:
                continue
        else:
            return 'ffprobe'

    def start_processing(self):
        """开始处理视频"""
        try:
            self.start_button.config(state='disabled')
            self.update_status('准备处理...', 'warning')
            self.add_log('⏳ 正在启动处理流程...')
            
            # 检查FFmpeg是否可用
            if not self.check_ffmpeg():
                self.add_log('❌ FFmpeg检查失败，无法继续处理')
                self.update_status('FFmpeg不可用', 'danger')
                self.start_button.config(state='normal')
                return
            
            # 根据模式调用相应处理函数
            if self.processing_mode.get() == 'single':
                self.add_log('🔄 进入单文件处理模式')
                self.start_single_processing()
            else:
                self.add_log('🔄 进入批量处理模式')
                self.start_batch_processing()
                
            self.add_log('✅ 处理流程已启动')
        except Exception as e:
            self.add_log(f'❌ 启动处理流程时出错: {str(e)}')
            self.update_status('启动失败', 'danger')
            self.start_button.config(state='normal')

    def start_single_processing(self):
        """开始单文件处理"""
        if not self.input_video_var.get():
            self.show_modern_messagebox('错误', '请选择原视频文件', 'error')
            return
        if not self.material_video_var.get():
            self.show_modern_messagebox('错误', '请选择素材视频文件', 'error')
            return
        if not self.output_path_var.get():
            self.show_modern_messagebox('错误', '请设置输出文件路径', 'error')
            return
        if not self.check_ffmpeg():
            return
        self.start_button.config(state='disabled')
        self.update_status('正在处理...', 'warning')

        def process_thread():
            try:
                self.update_progress(0, '开始处理...')
                self.add_log('🚀 开始皇冠AB处理...')
                material_video_path = self.material_video_var.get()
                success = self.process_dragon_boat_ab_video(self.input_video_var.get(), material_video_path, self.output_path_var.get(), self.original_duration_var.get())
                if success:
                    self.add_log('🎉 处理完成！')
                    self.safe_delete_material_video(material_video_path, os.path.basename(self.input_video_var.get()))
                    self.update_status('处理完成', 'success')
                    self.show_toast('处理完成', '视频处理成功！', 'success')
                else:
                    self.add_log('❌ 处理失败')
                    self.update_status('处理失败', 'danger')
                    self.show_toast('处理失败', '视频处理失败，请查看日志', 'error')
                self.start_button.config(state='normal')
            except Exception as e:
                self.add_log(f'❌ 皇冠AB处理出错: {str(e)}')
                self.add_log('📧 如遇问题请联系: <EMAIL>')
                self.start_button.config(state='normal')

        # 确保线程正确启动并执行核心流程
        self.add_log('🔄 启动批量处理线程...')
        try:
            thread = threading.Thread(
                target=process_thread,
                name='BatchProcessorThread',
                daemon=True
            )
            thread.start()
            self.add_log('✅ 批量处理线程已启动')
            # 添加线程状态检查
            if not thread.is_alive():
                raise RuntimeError('批量处理线程启动失败')
        except Exception as e:
            self.add_log(f'❌ 线程启动失败: {str(e)}')
            self.start_button.config(state='normal')
            return

    def start_batch_processing(self):
        """开始批量处理 - 添加详细执行日志"""
        self.add_log('🔍 开始验证批量处理参数...')
        input_folder = self.batch_input_folder_var.get().strip()
        material_folder = self.batch_material_folder_var.get().strip()
        output_folder = self.batch_output_folder_var.get().strip()
        
        # 详细参数验证
        self.add_log(f'  输入文件夹: {input_folder}')
        self.add_log(f'  素材文件夹: {material_folder}') 
        self.add_log(f'  输出文件夹: {output_folder}')
        
        if not all([input_folder, material_folder, output_folder]):
            self.add_log('❌ 错误: 缺少必要的文件夹参数')
            self.show_modern_messagebox('错误', '请完整设置所有文件夹路径', 'error')
            return
            
        if not os.path.exists(input_folder):
            self.add_log(f'❌ 错误: 输入文件夹不存在 {input_folder}')
            self.show_modern_messagebox('错误', '原视频文件夹不存在', 'error')
            return
            
        if not os.path.exists(material_folder):
            self.add_log(f'❌ 错误: 素材文件夹不存在 {material_folder}')
            self.show_modern_messagebox('错误', '素材视频文件夹不存在', 'error')
            return
            
        if not os.path.exists(output_folder):
            self.add_log(f'⚠️ 输出文件夹不存在，尝试创建 {output_folder}')
            try:
                os.makedirs(output_folder, exist_ok=True)
                self.add_log('✅ 成功创建输出文件夹')
            except Exception as e:
                self.add_log(f'❌ 创建输出文件夹失败: {str(e)}')
                self.show_modern_messagebox('错误', f'无法创建输出文件夹: {str(e)}', 'error')
                return
        # 获取视频文件列表
        self.add_log('🔍 正在扫描视频文件...')
        video_files = self.get_video_files(input_folder)
        material_files = self.get_video_files(material_folder)
        
        self.add_log(f'  发现原视频: {len(video_files)} 个')
        self.add_log(f'  发现素材视频: {len(material_files)} 个')
        
        if not video_files:
            self.add_log('❌ 错误: 原视频文件夹中没有视频文件')
            self.show_modern_messagebox('警告', '原视频文件夹中没有找到视频文件', 'warning')
            return
            
        if not material_files:
            self.add_log('❌ 错误: 素材文件夹中没有视频文件')
            self.show_modern_messagebox('警告', '素材视频文件夹中没有找到视频文件', 'warning')
            return
            
        generation_count = self.generation_count_var.get()
        self.add_log(f'  每个视频生成数量: {generation_count}')
        # 检查FFmpeg
        self.add_log('🔍 正在检查FFmpeg...')
        if not self.check_ffmpeg():
            self.add_log('❌ 错误: FFmpeg检查失败')
            return
            
        # 创建并启动处理线程
        self.add_log('🔄 正在启动处理线程...')
        processing_thread = threading.Thread(
            target=self.batch_process_thread,
            daemon=True
        )
        processing_thread.start()
        
        # 添加线程状态检查
        def check_thread():
            if processing_thread.is_alive():
                self.root.after(100, check_thread)
            else:
                self.add_log('✅ 处理线程已完成')
        
        self.root.after(100, check_thread)

def batch_process_thread(self):
    """批量处理视频的主线程方法"""
    try:
        # 1. 参数获取与验证
        input_folder = self.batch_input_folder_var.get()
        material_folder = self.batch_material_folder_var.get()
        output_folder = self.batch_output_folder_var.get()
        generation_count = self.generation_count_var.get()

        # 验证文件夹存在性
        if not all([os.path.exists(input_folder), os.path.exists(material_folder)]):
            self.add_log('❌ 错误：输入文件夹不存在')
            return False

        # 2. 初始化处理环境
        self.add_log('🛠️ 初始化批量处理环境...')
        self.add_log(f'🎯 每个原视频生成 {generation_count} 个版本')

        # 获取视频文件列表
        try:
            video_files = self.get_video_files(input_folder)
            material_files = self.get_video_files(material_folder)
            
            if not video_files:
                self.add_log('❌ 错误：原视频文件夹中没有视频文件')
                return False
                
            if not material_files:
                self.add_log('❌ 错误：素材文件夹中没有视频文件')
                return False
        except Exception as e:
            self.add_log(f'❌ 获取视频文件失败: {str(e)}')
            return False

        # 3. 检查素材数量
        total_needed_materials = len(video_files) * generation_count
        if len(material_files) < total_needed_materials:
            shortage = total_needed_materials - len(material_files)
            self.add_log(f'⚠️ 素材不足: 需要 {total_needed_materials} 个, 当前只有 {len(material_files)} 个')

        # 4. 核心处理逻辑
        total_count = len(video_files) * generation_count
        success_count = 0
        deleted_material_count = 0
        used_materials = set()

        try:
            for i, video_file in enumerate(video_files, 1):
                video_name = os.path.splitext(os.path.basename(video_file))[0]
                self.add_log(f'\n📹 处理原视频 {i}/{len(video_files)}: {video_name}')
                
                for j in range(generation_count):
                    # 选择素材视频
                    available_materials = [m for m in material_files if m not in used_materials]
                    if not available_materials:
                        self.add_log('⚠️ 没有可用的素材视频')
                        break
                        
                    selected_material = random.choice(available_materials)
                    used_materials.add(selected_material)
                    self.add_log(f'   🎬 使用素材: {os.path.basename(selected_material)}')
                    
                    # 处理视频
                    try:
                        output_file = os.path.join(
                            output_folder, 
                            f'{video_name}_皇冠AB_{j+1}.mp4' if generation_count > 1 else f'{video_name}_皇冠AB.mp4'
                        )
                        success = self.process_dragon_boat_ab_video(video_file, selected_material, output_file, 8.0)
                        
                        if success:
                            success_count += 1
                            if self.delete_material_var.get():
                                if self.safe_delete_material_video(selected_material, f'{video_name}_v{j+1}'):
                                    deleted_material_count += 1
                    except Exception as e:
                        self.add_log(f'   ❌ 处理失败: {str(e)}')
                        continue

            # 4. 最终状态更新
            self.add_log(f'\n🎉 处理完成! 成功: {success_count}/{total_count}')
            self.update_status('处理完成', 'success' if success_count == total_count else 'warning')
            return True

        except Exception as e:
            self.add_log(f'💥 处理过程中发生错误: {str(e)}')
            self.update_status('处理失败', 'danger')
            return False

    except Exception as e:
        self.add_log(f'💥 批量处理初始化失败: {str(e)}')
        self.update_status('处理失败', 'danger')
        return False

    finally:
        self.start_button.config(state='normal')
            # 最终状态更新
            self.add_log(f'\n🎉 处理完成! 成功: {success_count}/{total_count}')
            self.update_status('处理完成', 'success' if success_count == total_count else 'warning')
            self.add_log(f'📁 素材库: {len(material_files)} 个素材视频')
            self.add_log(f'📊 预计需要素材: {total_needed_materials} 个')
            if len(material_files) < total_needed_materials:
                self.add_log(f'⚠️ 警告：素材数量不足！需要 {total_needed_materials} 个，实际 {len(material_files)} 个')
                self.add_log('💡 建议：减少生成数量或增加素材视频')
            self.add_log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
                success_count = 0
                total_count = len(video_files) * generation_count
                deleted_material_count = 0
                used_materials = set()
                for i, video_file in enumerate(video_files, 1):
                    self.add_log(f'\n📹 处理原视频 {i}/{len(video_files)}: {os.path.basename(video_file)}')
                    for j in range(generation_count):
                        current_index = i * generation_count + j
                        self.add_log(f'   🎬 生成版本 {j + 1}/{generation_count} (总进度: {current_index}/{total_count})')
                        self.update_status(f'批量处理中... ({current_index}/{total_count})', 'warning')
                        available_materials = [m for m in material_files if m not in used_materials]
                        if not available_materials:
                            self.add_log('❌ 没有可用的素材视频了，停止处理')
                            break
                        selected_material = random.choice(available_materials)
                        used_materials.add(selected_material)
                    # 视频处理循环
                    for j in range(generation_count):
                        # 选择素材
                        if not available_materials:
                            available_materials = material_files.copy()
                        selected_material = random.choice(available_materials)
                        available_materials.remove(selected_material)
                        
                        self.add_log(f'🎭 配对素材: {os.path.basename(selected_material)}')
                        video_name = os.path.splitext(os.path.basename(video_file))[0]
                        
                        # 确定输出文件名
                # 视频处理循环
                for j in range(generation_count):
                    # 选择素材
                    if not available_materials:
                        available_materials = material_files.copy()
                    selected_material = random.choice(available_materials)
                    available_materials.remove(selected_material)
                    
                    self.add_log(f'🎭 配对素材: {os.path.basename(selected_material)}')
                    video_name = os.path.splitext(os.path.basename(video_file))[0]
                    
                # 视频处理循环
                for j in range(generation_count):
                    # 选择素材
                    if not available_materials:
                        available_materials = material_files.copy()
                    selected_material = random.choice(available_materials)
                    available_materials.remove(selected_material)
                    
                    self.add_log(f'🎭 配对素材: {os.path.basename(selected_material)}')
                    video_name = os.path.splitext(os.path.basename(video_file))[0]
                    
                    # 确定输出文件名
                    output_file = os.path.join(
                        output_folder,
                        f'{video_name}_皇冠AB_v{j + 1}.mp4' if generation_count > 1
                        else f'{video_name}_皇冠AB.mp4'
                    )
                    
                    # 验证工具路径
                    self.add_log(f'🔍 验证FFmpeg路径: {self.get_ffmpeg_path()}')
                    self.add_log(f'🔍 验证FFprobe路径: {self.get_ffprobe_path()}')
                    
                    # 处理视频
                    self.add_log('🔄 调用皇冠AB处理方法...')
                    self.root.update()
                    
                    try:
                        self.add_log(f'🚀 开始处理: {os.path.basename(video_file)}')
                        success = self.process_dragon_boat_ab_video(
                            video_file,
                            selected_material,
                            output_file,
                            8.0
                        )
                        self.root.update()
                        
                        if success:
                            success_count += 1
                            self.add_log(f'✅ 处理成功 ({success_count}/{total_count})')
                            if (self.delete_material_var.get() and 
                                selected_material in material_files):
                                if self.safe_delete_material_video(
                                    selected_material, 
                                    f'{os.path.basename(video_file)}_v{j+1}'
                                ):
                                    material_files.remove(selected_material)
                                    deleted_material_count += 1
                        else:
                            self.add_log('❌ 处理失败，请检查日志')
                    except Exception as e:
                        self.add_log(f'💥 处理异常: {str(e)}')
                        continue

                # 最终状态更新
                self.add_log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
        finally:
            self.start_button.config(state='normal')
            # 最终状态更新
            self.add_log(f'\n🎉 处理完成! 成功: {success_count}/{total_count}')
            self.update_status('处理完成', 'success' if success_count == total_count else 'warning')
            self.add_log(f'📁 素材库: {len(material_files)} 个素材视频')
            self.add_log(f'📊 预计需要素材: {total_needed_materials} 个')
            if len(material_files) < total_needed_materials:
                self.add_log(f'⚠️ 警告：素材数量不足！需要 {total_needed_materials} 个，实际 {len(material_files)} 个')
                self.add_log('💡 建议：减少生成数量或增加素材视频')
            self.add_log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')

    def safe_delete_material_video(self, file_path, video_name):
        """安全删除素材视频文件"""
        if not self.delete_material_var.get():
            return False
            
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                self.add_log(f'🗑️ 已删除素材视频: {video_name}')
                return True
            return False
        except Exception as e:
            self.add_log(f'⚠️ 删除素材视频失败({video_name}): {str(e)}')
            return False
        """检查FFmpeg是否可用"""
        try:
            ffmpeg_path = self.get_ffmpeg_path()
            result = self.safe_subprocess_run([ffmpeg_path, '-version'])
            if result.returncode == 0:
                self.add_log('✅ FFmpeg检查通过')
                return True
            self.show_modern_messagebox('错误', 'FFmpeg不可用，请确保已正确安装FFmpeg', 'error')
            return False
        except subprocess.CalledProcessError as e:
            self.add_log(f'❌ FFmpeg检查失败: {str(e)}')
            self.show_modern_messagebox('错误', f'FFmpeg检查失败: {str(e)}', 'error')
            return False

    def process_dragon_boat_ab_video(self, input_video, material_video, output_path, duration):
        """处理皇冠AB视频 - 严格按照用户命令序列"""
        try:
            self.add_log(f'🚀 开始处理视频: {os.path.basename(input_video)}')
            self.add_log(f'   🎬 使用素材: {os.path.basename(material_video)}')
            
            # 创建临时目录
            temp_dir = os.path.join(os.path.dirname(output_path), 'temp_ab_video')
            os.makedirs(temp_dir, exist_ok=True)
            temp1_path = os.path.join(temp_dir, 'temp_std.mp4')
            temp2_path = os.path.join(temp_dir, 'temp_hq.mp4')
            
            # 1. FFprobe分析视频信息 (用户指定命令)
            self.add_log('🔍 执行FFprobe分析...')
            cmd = [
                self.get_ffprobe_path(),
                '-v', 'quiet',
                '-print_format', 'json',
                '-show_format',
                '-show_streams',
                input_video
            ]
            result = self.safe_subprocess_run(cmd)
            if result.returncode != 0:
                self.add_log(f'❌ FFprobe分析失败: {result.stderr}')
                return False
            self.add_log('✅ 视频分析完成')
            
            # 2. 生成标准质量版本 (用户指定命令)
            self.add_log('🛠️ 生成标准质量版本...')
            cmd2 = [
                self.get_ffmpeg_path(),
                '-i', input_video,
                '-c:v', 'libx264',
                '-b:v', '3000k',
                '-maxrate:v:0', '3000k',
                '-bufsize:v:0', '3000k',
                '-x264opts', 'nal-hrd=cbr',
                '-c:a', 'copy',
                '-y', temp1_path
            ]
            result2 = self.safe_subprocess_run(cmd2)
            if result2.returncode != 0:
                self.add_log(f'❌ 标准质量生成失败: {result2.stderr}')
                return False
            self.add_log('✅ 标准质量版本生成完成')
            
            # 3. 生成高质量版本 (用户指定命令)
            self.add_log('🛠️ 生成高质量版本...')
            cmd3 = [
                self.get_ffmpeg_path(),
                '-i', input_video,
                '-c:v:0', 'libx264',
                '-b:v:0', '5000k',
                '-maxrate:v:0', '5000k',
                '-bufsize:v:0', '5000k',
                '-x264opts', 'nal-hrd=cbr',
                '-bsf:v', 'noise=amount=-20',
                '-c:a', 'copy',
                '-y', temp2_path
            ]
            result3 = self.safe_subprocess_run(cmd3)
            if result3.returncode != 0:
                self.add_log(f'❌ 高质量生成失败: {result3.stderr}')
                return False
            self.add_log('✅ 高质量版本生成完成')
            
            # 4. 合并两个版本 (用户指定命令)
            self.add_log('🔀 合并视频版本...')
            cmd4 = [
                self.get_ffmpeg_path(),
                '-i', temp1_path,
                '-i', temp2_path,
                '-filter_complex', '[0:v]fps=60[a];[1:v]fps=30[b];[a][b]interleave',
                '-c:v', 'libx264',
                '-preset', 'fast',
                '-movflags', '+faststart',
                '-f', 'mp4',
                '-y', output_path
            ]
            result4 = self.safe_subprocess_run(cmd4)
            if result4.returncode != 0:
                self.add_log(f'❌ 合并失败: {result4.stderr}')
                return False
            
            self.add_log('🎉 视频处理完成!')
            return True
            
        except Exception as e:
            self.add_log(f'💥 处理异常: {str(e)}')
            return False
        finally:
            # 清理临时文件
            if os.path.exists(temp_dir):
                try:
                    shutil.rmtree(temp_dir)
                    self.add_log('🧹 临时文件已清理')
                except Exception as e:
                    self.add_log(f'⚠️ 清理临时文件出错: {str(e)}')

if __name__ == '__main__':
    root = tk.Tk()
    app = DragonBoatABProcessor(root)
    root.mainloop()