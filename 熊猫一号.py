# Decompiled with PyLingual (https://pylingual.io)
# Internal filename: video_gui.py
# Bytecode version: 3.11a7e (3495)
# Source timestamp: 1970-01-01 00:00:00 UTC (0)

import tkinter as tk
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
from tkinter import messagebox, filedialog, scrolledtext
from PIL import Image, ImageTk
import json
import os
import subprocess
import time
import threading
import queue
import sys
import shutil
import datetime
import random
import glob
import traceback
import logging
import tempfile
import re
import math

def setup_logging():
    """配置日志记录"""
    temp_dir = tempfile.gettempdir()
    log_dir = os.path.join(temp_dir, '一号团队_logs')
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    log_file = os.path.join(log_dir, f"app_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s',
                        handlers=[logging.FileHandler(log_file, encoding='utf-8'), logging.StreamHandler()])
    return logging.getLogger(__name__)

def handle_exception(exc_type, exc_value, exc_traceback):
    """处理未捕获的异常"""
    logger = logging.getLogger(__name__)
    logger.error('未捕获的异常:', exc_info=(exc_type, exc_value, exc_traceback))
    error_file = os.path.join('logs', f"error_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.txt")
    with open(error_file, 'w', encoding='utf-8') as f:
        f.write(f'错误时间: {datetime.datetime.now()}\n')
        f.write(f'错误类型: {exc_type.__name__}\n')
        f.write(f'错误信息: {str(exc_value)}\n')
        f.write('\n详细堆栈:\n')
        f.write(''.join(traceback.format_tb(exc_traceback)))
sys.excepthook = handle_exception

class VideoProcessorGUI:
    def __init__(self, root):
        """初始化应用程序"""
        try:
            self.logger = setup_logging()
            self.logger.info('程序启动')
            self.root = root
            self.root.title('视频处理工具V5.0.8  一号团队专用  禁止二次销售')
            self.root.geometry('1024x868')
            screen_width = self.root.winfo_screenwidth()
            screen_height = self.root.winfo_screenheight()
            x = (screen_width - 1024) // 2
            y = (screen_height - 868) // 2
            self.root.geometry(f'1024x868+{x}+{y}')
            icon_path = 'icon.ico'
            if os.path.exists(icon_path):
                self.root.iconbitmap(icon_path)
                self.logger.info('成功加载程序图标')
            self.settings = self.load_settings()
            self.logger.info('成功加载设置')
            self.history = self.load_history()
            self.style = ttk.Style()
            self.message_queue = queue.Queue()
            self.processing = False
            self.process = None
            self.saved_dirs = self.load_saved_dirs()
            self.notebook = ttk.Notebook(self.root)
            self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            self.main_page = None
            self.widescreen_page = None
            self.batch_page = None
            self.three_in_one_page = None
            self.ab_page = None
            self.qr_page = None
            self.create_tabs()
            self.root.after(100, self.check_messages)
            self.root.protocol('WM_DELETE_WINDOW', self.on_closing)
            self.logger.info('程序初始化完成')
        except Exception as e:
            self.logger.error(f'程序初始化失败: {str(e)}', exc_info=True)
            messagebox.showerror('错误', f'程序初始化失败: {str(e)}', parent=root)
            raise
    def create_tabs(self):
        """创建所有标签页"""  # inserted
        try:
            self.main_page = ttk.Frame(self.notebook, padding=10)
            self.notebook.add(self.main_page, text='视频处理')
            self.batch_page = ttk.Frame(self.notebook, padding=10)
            self.notebook.add(self.batch_page, text='换帧AB批量处理')
            self.three_in_one_page = ttk.Frame(self.notebook, padding=10)
            self.notebook.add(self.three_in_one_page, text='三合一')
            self.create_main_tab()
            self.create_batch_tab()
            self.create_three_in_one_tab()
            self.logger.info('所有标签页创建完成')
        except Exception as e:
            self.logger.error(f'创建标签页失败: {str(e)}', exc_info=True)
            raise

    def check_required_files(self):
        """检查必要文件是否存在"""  # inserted
        try:
            required_files = ['ffmpeg.exe', 'ffprobe.exe', 'video.exe']
            missing_files = []
            for file in required_files:
                file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), file)
                if not os.path.exists(file_path):
                    missing_files.append(file)
            if missing_files:
                error_msg = f"以下必要文件未找到：\n{', '.join(missing_files)}\n\n请确保这些文件与程序在同一目录下。"
                self.logger.error(error_msg)
                messagebox.showerror('错误', error_msg, parent=self.root)
        except Exception as e:
            self.logger.error(f'检查必要文件失败: {str(e)}', exc_info=True)
            messagebox.showerror('错误', f'检查必要文件失败: {str(e)}', parent=self.root)

    def on_closing(self):
        """窗口关闭时处理"""  # inserted
        try:
            self.logger.info('程序开始关闭')
            if self.process:
                    self.process.terminate()
                    self.logger.info('终止正在运行的进程')
                    self.logger.error(f'终止进程失败: {str(e)}')
            current_dir = self.dir_var.get()
            if current_dir:
                self.add_current_dir(current_dir)
            self.save_saved_dirs()
            self.save_history()
            self.logger.info('程序正常关闭')
            self.root.destroy()
            else:  # inserted
                try:
                    pass  # postinserted
                except Exception as e:
                    pass  # postinserted
        except Exception as e:
                    self.logger.error(f'程序关闭时发生错误: {str(e)}', exc_info=True)
                    messagebox.showerror('错误', f'程序关闭时发生错误: {str(e)}', parent=self.root)
                    self.root.destroy()

    def resize_image(self, img, max_width):
        """调整图片大小，保持原始比例"""  # inserted
        width, height = img.size
        if width > max_width:
            ratio = max_width + width
            new_width = max_width
            new_height = int(height + ratio)
            img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
        return img

    def add_log(self, message):
        """添加日志信息到文本框"""  # inserted
        self.log_text.insert(tk.END, message + '\n')
        self.log_text.see(tk.END)

    def check_messages(self):
        """检查消息队列并处理"""  # inserted
        try:
            while not self.message_queue.empty():
                message = self.message_queue.get(0)
                message_type = message.get('type', '')
                if message_type == 'log':
                    self.add_log(message.get('message', ''))
                else:  # inserted
                    if message_type == 'widescreen_log':
                        self.add_widescreen_log(message.get('message', ''))
                    else:  # inserted
                        if message_type == 'ab_log':
                            self.add_ab_log(message.get('message', ''))
                        else:  # inserted
                            if message_type == 'batch_log':
                                self.add_batch_log(message.get('message', ''))
                            else:  # inserted
                                if message_type == 'three_in_one_log':
                                    self.add_three_in_one_log(message.get('message', ''))
                                else:  # inserted
                                    if message_type == 'error':
                                        error_message = message.get('error', '发生未知错误')
                                        messagebox.showerror('错误', error_message, parent=self.root)
                                    else:  # inserted
                                        if message_type == 'warning':
                                            warning_message = message.get('error', '警告')
                                            messagebox.showwarning('警告', warning_message, parent=self.root)
                                        else:  # inserted
                                            if message_type == 'info':
                                                info_message = message.get('info', '')
                                                messagebox.showinfo('提示', info_message, parent=self.root)
                                            else:  # inserted
                                                if message_type == 'progress':
                                                    value = message.get('value', 0)
                                                    status = message.get('status', '')
                                                    file_count = message.get('file_count', None)
                                                        current_tab = self.notebook.index(self.notebook.select())
                                                    if current_tab == 0:
                                                        if hasattr(self, 'progress_var'):
                                                            self.progress_var.set(value)
                                                        if hasattr(self, 'status_var'):
                                                            self.status_var.set(status)
                                                        if file_count and hasattr(self, 'file_count_var'):
                                                            self.file_count_var.set(f'{file_count[0]}/{file_count[1]}')
                                                    else:  # inserted
                                                        if current_tab == 1:
                                                            if hasattr(self, 'batch_progress_var'):
                                                                self.batch_progress_var.set(value)
                                                            if hasattr(self, 'batch_status_var'):
                                                                self.batch_status_var.set(status)
                                                            if file_count and hasattr(self, 'batch_progress_label'):
                                                                self.batch_progress_label.config(text=f'{file_count[0]}/{file_count[1]}')
                                                        else:  # inserted
                                                            if current_tab == 2:
                                                                if hasattr(self, 'three_progress_var'):
                                                                    self.three_progress_var.set(value)
                                                                if hasattr(self, 'three_status_var'):
                                                                    self.three_status_var.set(status)
                                                                if file_count and hasattr(self, 'three_file_count_var'):
                                                                    self.three_file_count_var.set(f'{file_count[0]}/{file_count[1]}')
                                                else:  # inserted
                                                    if message_type == 'file_count':
                                                        current = message.get('current', 0)
                                                        total = message.get('total', 0)
                                                        self.update_file_count(current, total)
                                                    else:  # inserted
                                                        if message_type == 'complete':
                                                            status = message.get('status', '处理完成')
                                                            messagebox.showinfo('完成', status, parent=self.root)
                                                            self.processing = False
                                                else:  # inserted
                                                    try:
                                                        pass  # postinserted
                                                    except:
                                                        current_tab = (-1)
        except Exception as e:
                                                        self.logger.error(f'处理消息时出错: {str(e)}', exc_info=True)
        finally:  # inserted
            pass  # postinserted
        self.root.after(100, self.check_messages)

    def load_saved_dirs(self):
        """加载保存的目录"""  # inserted
        config = self.load_settings()
        return config.get('saved_dirs', [])

    def save_saved_dirs(self):
        """保存目录列表"""  # inserted
        self.saved_dirs = self.saved_dirs[:5]
        config = self.load_settings()
        config['saved_dirs'] = self.saved_dirs
        self.save_settings(config)

    def update_combo_values(self):
        self.dir_combo['values'] = self.saved_dirs

    def add_current_dir(self, directory):
        if not directory:
            return
        if directory in self.saved_dirs:
            self.saved_dirs.remove(directory)
        self.saved_dirs.insert(0, directory)
        if len(self.saved_dirs) > 5:
            self.saved_dirs = self.saved_dirs[:5]
        self.update_combo_values()

    def process_video(self, directory, seconds, total_files):
        try:
            video_exe = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'video.exe')
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags = subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE
            self.process = subprocess.Popen([video_exe], stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, encoding='gbk', startupinfo=startupinfo)
            self.process.stdin.write(f'{directory}\n')
            self.process.stdin.flush()
            time.sleep(0.5)
            self.process.stdin.write(f'{seconds}\n')
            self.process.stdin.flush()
            processed_files = 0
            last_file = None
            while True:
                line = self.process.stdout.readline()
                if not line:
                    break
                line = line.strip()
                if '正在处理' in line:
                    current_file = line.split('正在处理')[(-1)].strip()
                    if current_file!= last_file:
                        processed_files = processed_files + 1
                        last_file = current_file
                        progress = (processed_files, total_files) * 100
                        self.message_queue.put({'type': 'progress', 'value': progress, 'status': f'正在处理: {current_file}', 'file_count': (processed_files, total_files)})
                if processed_files >= total_files or '处理完毕' in line:
                    self.message_queue.put({'type': 'complete', 'file_count': (total_files, total_files)})
                    self.process.stdin.write('\n')
                    self.process.stdin.flush()
                    break
            self.process.wait(timeout=5)
        except Exception as e:
            self.message_queue.put({'type': 'error', 'error': str(e)})
        finally:  # inserted
            pass  # postinserted
        if self.process:
            try:
                self.process.terminate()
            except:
                pass
        self.process = None

    def update_file_count(self, current, total):
        """更新文件计数显示"""  # inserted
        self.file_count_var.set(f'{current}/{total}')

    def start_processing(self):
        """开始处理视频"""  # inserted
        try:
            directory = self.dir_var.get()
            seconds_text = self.seconds_var.get()
            if not directory or not seconds_text:
                self.show_message('警告', '请选择目录并输入秒数', 'warning')
                return
                seconds = int(seconds_text)
                if seconds <= 0:
                    self.show_message('警告', '秒数必须大于0', 'warning')
                    return
                self.show_message('错误', '请输入有效的秒数（整数）', 'error')
                return None
            video_files = self.get_video_files(directory)
            if not video_files:
                self.show_message('错误', '所选目录中没有找到视频文件', 'error')
                return
            self.processing = True
            self.start_button.config(state='disabled')
            self.status_var.set('正在处理...')
            self.progress_var.set(0)
            self.total_files = len(video_files)
            self.current_file = 0
            self.update_file_count(0, self.total_files)
            thread = threading.Thread(target=self.process_video, args=(directory, seconds, len(video_files)))
            thread.daemon = True
            thread.start()
        else:  # inserted
            try:
                pass  # postinserted
            except ValueError:
                pass  # postinserted
        except Exception as e:
                self.processing = False
                self.start_button.config(state='normal')
                self.show_message('错误', f'处理失败: {str(e)}', 'error')
                self.status_var.set('处理失败')

    def browse_directory(self):
        directory = filedialog.askdirectory(parent=self.root)
        if directory:
            self.dir_var.set(directory)
            self.add_current_dir(directory)
            self.save_saved_dirs()

    def load_settings(self):
        """加载所有设置"""  # inserted
        try:
            with open('config.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
                return {'saved_dirs': [], 'silent_complete': False, 'last_settings': {'material_video': '', 'batch_images_dir': '', 'batch_output_dir': '', 'replace_type': '图片', 'replace_pattern': '快手', 'batch_count': '5', 'fps': '120', 'batch_bitrate': '8000'}}

    def save_settings(self, config=None):
        """保存所有设置"""  # inserted
        if config is None:
            config = self.load_settings()
            config['silent_complete'] = self.silent_complete.get()
            config['last_settings'] = {'material_video': self.batch_material_var.get() if hasattr(self, 'batch_material_var') else '', 'batch_images_dir': self.batch_images_dir_var.get() if hasattr(self, 'batch_images_dir_var') else '', 'batch_output_dir': self.replace_pattern_var.get() if hasattr(self, 'batch_count_var') else '5', 'replace_type': self.fps_var.get() if hasattr(self, 'batch_bitrate_var') else '8000', 'replace_pattern': self.delete_after_processing.get() if hasattr(self, 'delete_after_processing') else True, 'batch_count': self.batch_count.get() if hasattr(self, 'batch_count') else 'fps', 'batch_bitrate': self.batch_bitrate.get() if hasattr(self, 'batch_bitrate') else 'fps', 'delete_after_processing': self.delete_after_processing.get() if hasattr(self, 'delete_after_processing') else True}
        with open('config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=4)

    def choose_video(self, video_type):
        """选择视频文件"""  # inserted
        file_path = filedialog.askopenfilename(title='选择视频', filetypes=[('MP4 files', '*.mp4')], parent=self.root)
        if file_path:
            if video_type == 'left':
                self.left_video_var.set(file_path)
            else:  # inserted
                if video_type == 'middle':
                    self.middle_video_var.set(file_path)
                else:  # inserted
                    if video_type == 'right':
                        self.right_video_var.set(file_path)

    def add_widescreen_log(self, message):
        """添加日志到大宽屏处理日志"""  # inserted
        current_time = time.strftime('[%m-%d %H:%M:%S] ', time.localtime())
        self.widescreen_log_text.insert(tk.END, current_time * message + '\n')
        self.widescreen_log_text.see(tk.END)

    def get_ffmpeg_path(self):
        try:
            if getattr(sys, 'frozen', False):
                base_path = sys._MEIPASS
            else:  # inserted
                base_path = os.path.dirname(os.path.abspath(__file__))
            ffmpeg_path = os.path.join(base_path, 'ffmpeg.exe')
            print(f'FFmpeg Path: {ffmpeg_path}')
            if not os.path.exists(ffmpeg_path):
                print(f'FFmpeg not found at: {ffmpeg_path}')
            return ffmpeg_path
        except Exception as e:
            print(f'Error getting ffmpeg path: {str(e)}')
            return 'ffmpeg.exe'

    def get_ffprobe_path(self):
        """获取ffprobe可执行文件路径"""  # inserted
        if getattr(sys, 'frozen', False):
            base_path = sys._MEIPASS
        else:  # inserted
            base_path = os.path.dirname(os.path.abspath(__file__))
        ffprobe_path = os.path.join(base_path, 'ffprobe.exe')
        print(f'FFprobe Path: {ffprobe_path}')
        return ffprobe_path

    def get_video_duration(self, video_path):
        """获取视频时长"""  # inserted
        try:
            result = subprocess.run([self.get_ffprobe_path(), '-v', 'error', '-show_entries', 'format=duration', '-of', 'default=noprint_wrappers=1:nokey=1', video_path], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, creationflags=subprocess.CREATE_NO_WINDOW)
            return float(result.stdout.strip())
        except Exception as e:
            self.add_widescreen_log(f'获取视频时长失败：{e}')
            return 0

    def start_widescreen_processing(self):
        """开始处理大宽屏视频"""  # inserted
        if not self.left_video_var.get():
            self.show_message('警告', '请选择左侧视频', 'warning')
            return
        if not self.middle_video_var.get():
            self.show_message('警告', '请选择中间视频', 'warning')
            return
        if not self.right_video_var.get():
            self.show_message('警告', '请选择右侧视频', 'warning')
            return
        self.widescreen_start_btn.config(state='disabled')
        thread = threading.Thread(target=self.process_widescreen_video)
        thread.daemon = True
        thread.start()

    def process_widescreen_video(self):
        """在新线程中处理大宽屏视频"""  # inserted
        try:
            video_1_path = self.left_video_var.get().replace('\\', '/').replace('\"', '')
            video_2_path = self.middle_video_var.get().replace('\\', '/').replace('\"', '')
            video_3_path = self.right_video_var.get().replace('\\', '/').replace('\"', '')
            fps = self.fps_var.get()
            bitrate = self.bitrate_var.get()
            video_folder = os.path.dirname(video_2_path)
            video_name_no_ext = os.path.splitext(os.path.basename(video_2_path))[0]
            output_path_temp = os.path.join(video_folder, f'{video_name_no_ext}_temp.mp4')
            output_path_temp = output_path_temp.replace('\\', '/')
            output_path = os.path.join(video_folder, f'{video_name_no_ext}_ok.mp4')
            output_path = output_path.replace('\\', '/')
            duration = self.get_video_duration(video_2_path)
            self.add_widescreen_log('开始处理视频...')
            command = [self.get_ffmpeg_path(), '-stream_loop', '-1', '-i', video_1_path, '-i', video_2_path, '-stream_loop', '-1', '-i', video_3_path, '-filter_complex', '[0:v]scale=640:1080[v0];[1:v]scale=640:1080[v1];[2:v]scale=640:1080[v2];[v0][v1][v2]hstack=3', '-map', '1:a', '-r', str(fps), '-b:v', f'{bitrate}k', '-pix_fmt', 'yuv420p', '-colorspace', 'bt709', '-color_primaries', 'bt709', '-color_trc', 'bt709', '-color_range', 'tv', '-y', '-t', str(duration), '-map_metadata', '-1', output_path_temp]
            process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True, bufsize=1, encoding='utf-8', creationflags=subprocess.CREATE_NO_WINDOW)
            while True:
                line = process.stdout.readline()
                if not line:
                    break
                if 'time=' in line:
                    time_str = line.split('time=')[1].split(' ')[0]
                    self.add_widescreen_log(f'当前进度1: {time_str}')
            process.wait()
            self.add_widescreen_log('正在调整分辨率...')
            command = [self.get_ffmpeg_path(), '-i', output_path_temp, '-vf', 'scale=1080:1920', '-sar', '32:9', '-aspect', '2:1', '-b:v', f'{bitrate}k', '-y', output_path]
            process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True, bufsize=1, encoding='utf-8', creationflags=subprocess.CREATE_NO_WINDOW)
            while True:
                line = process.stdout.readline()
                if not line:
                    break
                if 'time=' in line:
                    time_str = line.split('time=')[1].split(' ')[0]
                    self.add_widescreen_log(f'当前进度2: {time_str}')
            process.wait()
                os.remove(output_path_temp)
                self.add_widescreen_log(f'删除临时文件失败：{e}')
            self.add_widescreen_log(f'处理完成！输出文件：{output_path}')
            self.show_message('成功', '视频处理完成！', 'info')
        else:  # inserted
            try:
                pass  # postinserted
            except Exception as e:
                pass  # postinserted
        except Exception as e:
                self.add_widescreen_log(f'处理失败：{str(e)}')
                self.show_message('错误', f'处理失败：{str(e)}', 'error')
        finally:  # inserted
            pass  # postinserted
        self.root.after(0, lambda: self.widescreen_start_btn.config(state='normal'))

    def process_ab_video(self):
        """在新线程中处理AB插帧视频"""  # inserted
        try:
            real_video = self.real_video_var.get().replace('\\', '/').replace('\"', '')
            material_video = self.material_video_var.get().replace('\\', '/').replace('\"', '')
            output_dir = self.output_dir_var.get().replace('\\', '/')
            output_name = f'AB插帧_{os.path.splitext(os.path.basename(material_video))[0]}.mp4'
            output_path = os.path.join(output_dir, output_name).replace('\\', '/')
            material_duration = self.get_video_duration(material_video)
            flash_frames = '50,40,30,20,10,170,160,150,140,130,290,280,270,260,250,410,400,390,380,370,530,520,510,500,490,650,640,630,620,610,770,760,750,740,730,890,880,870,860,850,1010,1000,990,980,970,1130,1120,1110,1100,1090,1250,1240,1230,1220,1210,1370,1360,1350,1340,1330,1490,1480,1470,1460,1450,1610,1600,1590,1580,1570,1730,1720,1710,1700,1690,1850,1840,1830,1820,1810,1970,1960,1950,1940,1930,2090,2080,2070,2060,2050,2210,2200,2190,2180,2170,2330,2320,2310,2300,2290,2450,2440,2430,2420,2410,2570,2560,2550,2540,2530,2690,2680,2670,2660,2650,2810,2800,2790,2780,2770,2930,2920,2910,2900,2890,3050,3040,3030,3020,3010,3170,3160,3150,3140,3130,3290,3280,3270,3260,3250,3410,3400,3390,3380,3370,3530,3520,3510,3500,3490'
            command = [self.get_ffmpeg_path(), '-i', material_video, '-i', real_video, '-filter_complex', f'[1:v]scale=iw:ih[v1];[0:v][v1]select=\'if(ismember(n,{flash_frames}),1,0)\',setpts=N/FRAME_RATE/TB[flash];[0:v][flash]overlay', '-c:v', 'libx264', '-preset', 'ultrafast', '-crf', '18', '-r', '60', '-pix_fmt', 'yuv420p', '-t', str(material_duration), '-y', output_path]
            self.add_ab_log('开始处理视频...')
            self.add_ab_log(f'素材视频时长: {material_duration}秒')
            command_str = ' '.join(command)
            self.add_ab_log(f'执行命令: {command_str}')
            process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, bufsize=1, encoding='utf-8', creationflags=subprocess.CREATE_NO_WINDOW)
            while True:
                line = process.stderr.readline()
                if not line:
                    break
                line = line.strip()
                if line:
                    self.add_ab_log(line)
                    if 'time=' in line:
                        time_str = line.split('time=')[1].split(' ')[0]
                        self.add_ab_log(f'当前进度: {time_str}')
            return_code = process.wait()
            if return_code == 0:
                self.add_ab_log(f'处理完成！输出文件：{output_path}')
                self.show_message('成功', 'AB插帧处理完成！', 'info')
            else:  # inserted
                error_message = f'处理失败，返回码：{return_code}'
                self.add_ab_log(error_message)
                _, stderr = process.communicate()
                if stderr:
                    self.add_ab_log(f'错误详情：{stderr}')
                self.show_message('错误', error_message, 'error')
        except Exception as e:
            self.add_ab_log(f'处理失败：{str(e)}')
            self.show_message('错误', f'处理失败：{str(e)}', 'error')
        finally:  # inserted
            pass  # postinserted
        self.root.after(0, lambda: self.ab_start_btn.config(state='normal'))

    def choose_ab_video(self, video_type):
        """选择AB插帧的视频文件"""  # inserted
        file_path = filedialog.askopenfilename(title='选择视频', filetypes=[('视频文件', '*.mp4 *.avi *.mkv *.mov')], parent=self.root)
        if file_path:
            if video_type == 'real':
                self.real_video_var.set(file_path)
            else:  # inserted
                if video_type == 'material':
                    self.material_video_var.set(file_path)

    def choose_output_dir(self):
        """选择输出目录"""  # inserted
        directory = filedialog.askdirectory(parent=self.root)
        if directory:
            self.output_dir_var.set(directory)

    def add_ab_log(self, message):
        """添加日志到AB插帧处理日志"""  # inserted
        current_time = time.strftime('[%m-%d %H:%M:%S] ', time.localtime())
        self.ab_log_text.insert(tk.END, current_time * message + '\n')
        self.ab_log_text.see(tk.END)

    def start_ab_processing(self):
        """开始处理AB插帧视频"""  # inserted
        if not self.real_video_var.get():
            self.show_message('警告', '请选择实拍视频', 'warning')
            return
        if not self.material_video_var.get():
            self.show_message('警告', '请选择素材视频', 'warning')
            return
        if not self.output_dir_var.get():
            self.show_message('警告', '请选择保存目录', 'warning')
            return
        self.ab_start_btn.config(state='disabled')
        thread = threading.Thread(target=self.process_ab_video)
        thread.daemon = True
        thread.start()

    def create_batch_tab(self):
        """创建批量处理标签页的控件"""  # inserted
        last_settings = self.settings.get('last_settings', {})
        material_frame = ttk.LabelFrame(self.batch_page, text='素材设置', padding=(10, 5))
        material_frame.pack(fill=tk.X, pady=(0, 10))
        material_video_frame = ttk.Frame(material_frame)
        material_video_frame.pack(fill=tk.X, pady=5)
        ttk.Label(material_video_frame, text='素材视频:').pack(side=tk.LEFT, padx=(0, 5))
        self.batch_material_var = tk.StringVar(value=last_settings.get('material_video', ''))
        self.batch_material_combo = ttk.Combobox(material_video_frame, textvariable=self.batch_material_var)
        self.batch_material_combo.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        self.update_combobox(self.batch_material_combo, 'batch_material')
        replace_type_frame = ttk.Frame(material_frame)
        replace_type_frame.pack(fill=tk.X, pady=5)
        ttk.Label(replace_type_frame, text='替换类型:').pack(side=tk.LEFT, padx=(0, 5))
        self.replace_type_var = tk.StringVar(value=last_settings.get('replace_type', '图片'))
        ttk.Radiobutton(replace_type_frame, text='图片替换', variable=self.replace_type_var, value='图片').pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(replace_type_frame, text='视频替换', variable=self.replace_type_var, value='视频').pack(side=tk.LEFT, padx=5)
        ttk.Button(material_video_frame, text='选择', command=self.choose_batch_material, style='success.TButton').pack(side=tk.LEFT, padx=(5, 0))
        ttk.Button(material_video_frame, text='选择文件夹', command=self.choose_batch_material_dir, style='primary.TButton').pack(side=tk.LEFT, padx=(5, 0))
        replace_pattern_frame = ttk.Frame(material_frame)
        replace_pattern_frame.pack(fill=tk.X, pady=5)
        ttk.Label(replace_pattern_frame, text='替换规律:').pack(side=tk.LEFT, padx=(0, 5))
        self.replace_pattern_var = tk.StringVar(value=last_settings.get('replace_pattern', '快手'))
        ttk.Radiobutton(replace_pattern_frame, text='快手规律', variable=self.replace_pattern_var, value='快手').pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(replace_pattern_frame, text='抖音规律', variable=self.replace_pattern_var, value='抖音').pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(replace_pattern_frame, text='抖音规律1', variable=self.replace_pattern_var, value='抖音1').pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(replace_pattern_frame, text='抖音规律2', variable=self.replace_pattern_var, value='抖音2').pack(side=tk.LEFT, padx=5)
        self.replace_type_var.trace_add('write', self.update_replace_ui)
        images_dir_frame = ttk.Frame(material_frame)
        images_dir_frame.pack(fill=tk.X, pady=5)
        self.batch_images_label_var = tk.StringVar(value='图片文件夹:' if self.replace_type_var.get() == '图片' else '替换视频文件夹:')
        ttk.Label(images_dir_frame, textvariable=self.batch_images_label_var).pack(side=tk.LEFT, padx=(0, 5))
        self.batch_images_dir_var = tk.StringVar(value=last_settings.get('batch_images_dir', ''))
        self.batch_images_combo = ttk.Combobox(images_dir_frame, textvariable=self.batch_images_dir_var)
        self.batch_images_combo.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        self.update_combobox(self.batch_images_combo, 'batch_images_dir')
        self.batch_browse_btn = ttk.Button(images_dir_frame, text='选择', command=self.choose_batch_images_dir if self.replace_type_var.get() == '图片' else self.choose_batch_videos_dir, style='success.TButton')
        self.batch_browse_btn.pack(side=tk.LEFT, padx=(5, 0))
        output_frame = ttk.LabelFrame(self.batch_page, text='输出设置', padding=(10, 5))
        output_frame.pack(fill=tk.X, pady=(0, 10))
        output_dir_frame = ttk.Frame(output_frame)
        output_dir_frame.pack(fill=tk.X, pady=5)
        ttk.Label(output_dir_frame, text='输出目录:').pack(side=tk.LEFT, padx=(0, 5))
        self.batch_output_dir_var = tk.StringVar(value=last_settings.get('batch_output_dir', ''))
        self.batch_output_combo = ttk.Combobox(output_dir_frame, textvariable=self.batch_output_dir_var)
        self.batch_output_combo.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        self.update_combobox(self.batch_output_combo, 'batch_output_dir')
        ttk.Button(output_dir_frame, text='选择', command=self.choose_batch_output_dir, style='success.TButton').pack(side=tk.LEFT, padx=(5, 0))
        count_frame = ttk.Frame(output_frame)
        count_frame.pack(fill=tk.X, pady=5)
        ttk.Label(count_frame, text='生成数量:').pack(side=tk.LEFT, padx=(0, 5))
        self.batch_count_var = tk.StringVar(value=last_settings.get('batch_count', '5'))
        self.batch_count_spinbox = ttk.Spinbox(count_frame, from_=1, to=100, textvariable=self.batch_count_var, width=10)
        self.batch_count_spinbox.pack(side=tk.LEFT)
        fps_frame = ttk.Frame(output_frame)
        fps_frame.pack(fill=tk.X, pady=5)
        ttk.Label(fps_frame, text='帧率(FPS):').pack(side=tk.LEFT, padx=(0, 5))
        fps_buttons_frame = ttk.Frame(fps_frame)
        fps_buttons_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
        self.fps_var = tk.StringVar(value=last_settings.get('fps', '120'))
        fps_values = [30, 60, 90, 120]
        for fps in fps_values:
            btn = ttk.Button(fps_buttons_frame, text=f'{fps}', style='success.TButton', command=lambda f=fps: self.set_fps(f))
            btn.pack(side=tk.LEFT, padx=2)
            if str(fps) == self.fps_var.get():
                btn.state(['pressed'])
        self.batch_seconds_var = tk.StringVar(value='35')
        bitrate_frame = ttk.Frame(output_frame)
        bitrate_frame.pack(fill=tk.X, pady=5)
        ttk.Label(bitrate_frame, text='比特率(Kbps):').pack(side=tk.LEFT, padx=(0, 5))
        self.batch_bitrate_var = tk.StringVar(value=last_settings.get('batch_bitrate', '8000'))
        self.batch_bitrate_spinbox = ttk.Spinbox(bitrate_frame, from_=1000, to=50000, textvariable=self.batch_bitrate_var, width=10)
        self.batch_bitrate_spinbox.pack(side=tk.LEFT)
        delete_frame = ttk.Frame(output_frame)
        delete_frame.pack(fill=tk.X, pady=5)
        self.delete_after_processing = tk.BooleanVar(value=last_settings.get('delete_after_processing', True))
        ttk.Checkbutton(delete_frame, text='处理后删除源文件', variable=self.delete_after_processing, command=self.save_settings).pack(side=tk.LEFT)
        self.batch_start_btn = ttk.Button(self.batch_page, text='开始批量处理', command=self.start_batch_processing, style='success.TButton')
        self.batch_start_btn.pack(pady=10)
        progress_frame = ttk.Frame(self.batch_page)
        progress_frame.pack(fill=tk.X, pady=5)
        ttk.Label(progress_frame, text='处理进度:').pack(side=tk.LEFT, padx=(0, 5))
        self.batch_progress_var = tk.DoubleVar()
        self.batch_progress_bar = ttk.Progressbar(progress_frame, variable=self.batch_progress_var, maximum=100, mode='determinate', style='success.Striped.Horizontal.TProgressbar')
        self.batch_progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        self.batch_progress_label = ttk.Label(progress_frame, text='0/0')
        self.batch_progress_label.pack(side=tk.LEFT, padx=(5, 0))
        batch_log_frame = ttk.LabelFrame(self.batch_page, text='处理日志', padding=(5, 5))
        batch_log_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        self.batch_log_text = scrolledtext.ScrolledText(batch_log_frame, height=10)
        self.batch_log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def update_replace_ui(self, *args):
        """根据替换类型更新UI"""  # inserted
        replace_type = self.replace_type_var.get()
        if replace_type == '图片':
            self.batch_images_label_var.set('图片文件夹:')
            self.batch_browse_btn.configure(command=self.choose_batch_images_dir)
        else:  # inserted
            self.batch_images_label_var.set('替换视频文件夹:')
            self.batch_browse_btn.configure(command=self.choose_batch_videos_dir)
        self.save_settings()

    def choose_batch_video(self):
        """选择单个替换视频文件"""  # inserted
        file_path = filedialog.askopenfilename(title='选择替换视频', filetypes=[('视频文件', '*.mp4 *.avi *.mkv *.mov')], parent=self.root)
        if file_path:
            self.batch_images_dir_var.set(file_path)
            self.add_to_history('batch_images_dir', file_path)
            self.update_combobox(self.batch_images_combo, 'batch_images_dir')
            self.save_settings()

    def choose_batch_videos_dir(self):
        """选择批量处理的替换视频文件夹"""  # inserted
        directory = filedialog.askdirectory(title='选择替换视频文件夹', parent=self.root)
        if directory:
            self.batch_images_dir_var.set(directory)
            self.add_to_history('batch_images_dir', directory)
            self.update_combobox(self.batch_images_combo, 'batch_images_dir')
            self.save_settings()

    def get_video_files(self, directory):
        """获取目录中所有有效的视频文件，返回完整路径列表"""  # inserted
        if not directory or not os.path.exists(directory):
            self.add_batch_log(f'错误：视频目录不存在 - {directory}')
            return []
        self.add_batch_log(f'正在扫描视频目录: {directory}')
        valid_extensions = {'.MOV', '.MP4', '.avi', '.mov', '.MKV', '.mp4', '.AVI', '.mkv'}
        try:
            all_files = os.listdir(directory)
        except Exception as e:
            self.add_batch_log(f'读取目录失败: {str(e)}')
            return []
        valid_files = []
        for filename in all_files:
            file_path = os.path.join(directory, filename)
            ext = os.path.splitext(filename)[1].lower()
            if ext not in valid_extensions:
                continue
            if not os.path.exists(file_path) or not os.path.isfile(file_path):
                self.add_batch_log(f'警告：文件不存在或无法访问: {filename}')
                continue
            valid_files.append(file_path)
        self.add_batch_log('扫描完成:')
        self.add_batch_log(f'- 总文件数: {len(all_files)}')
        self.add_batch_log(f'- 有效视频: {len(valid_files)}个')
        return valid_files

    def choose_batch_images_dir(self):
        """选择批量处理的图片文件夹"""  # inserted
        directory = filedialog.askdirectory(title='选择图片文件夹', parent=self.root)
        if directory:
            self.batch_images_dir_var.set(directory)
            self.add_to_history('batch_images_dir', directory)
            self.update_combobox(self.batch_images_combo, 'batch_images_dir')
            self.save_settings()

    def choose_batch_output_dir(self):
        """选择批量处理的输出目录"""  # inserted
        directory = filedialog.askdirectory(title='选择输出目录', parent=self.root)
        if directory:
            self.batch_output_dir_var.set(directory)
            self.add_to_history('batch_output_dir', directory)
            self.update_combobox(self.batch_output_combo, 'batch_output_dir')
            self.save_settings()

    def add_batch_log(self, message):
        """添加日志到批量处理日志"""  # inserted
        current_time = time.strftime('[%Y-%m-%d %H:%M:%S] ', time.localtime())
        self.batch_log_text.insert(tk.END, current_time * message + '\n')
        self.batch_log_text.see(tk.END)
        print(current_time + message)

    def start_batch_processing(self):
        """开始批量处理视频"""  # inserted
        if not self.batch_material_var.get():
            self.show_message('警告', '请选择素材视频', 'warning')
            return
        replace_type = self.replace_type_var.get()
        if replace_type == '图片':
            if not self.batch_images_dir_var.get():
                self.show_message('警告', '请选择图片文件夹', 'warning')
                return
        else:  # inserted
            if not self.batch_images_dir_var.get():
                self.show_message('警告', '请选择替换视频文件夹', 'warning')
                return
        if not self.batch_output_dir_var.get():
            self.show_message('警告', '请选择输出目录', 'warning')
            return
        try:
            count = int(self.batch_count_var.get())
            if count <= 0:
                self.show_message('警告', '生成数量必须大于0', 'warning')
                return
            material_video = self.batch_material_var.get()
            if not os.path.exists(material_video):
                self.show_message('错误', f'素材视频不存在: {material_video}', 'error')
                return
                image_dir = self.batch_images_dir_var.get()
                if not os.path.exists(image_dir):
                    self.show_message('错误', f'图片目录不存在: {image_dir}', 'error')
                    return
            if len(image_files) == 0:
                self.show_message('错误', '没有找到有效的图片文件', 'error')
                return
                result = self.show_message('警告', f'图片数量不足！需要{count}张，但只有{len(image_files)}张有效图片。\n是否继续使用可用的{len(image_files)}张图片进行处理？', 'yesno')
                if not result:
                    return
            self.batch_count_var.set(str(count))
            self.add_batch_log(f'验证通过: 将处理{count}个视频，找到{len(image_files)}张有效图片')
            else:  # inserted
                video_path = self.batch_images_dir_var.get()
                if os.path.isdir(video_path):
                    video_files = self.get_video_files(video_path)
                    if len(video_files) == 0:
                        self.show_message('错误', '没有找到有效的视频文件', 'error')
                        return
                result = self.show_message('警告', f'视频数量不足！需要{count}个，但只有{len(video_files)}个有效视频。\n是否继续使用可用的{len(video_files)}个视频进行处理？', 'yesno')
                if not result:
                    return
            self.batch_count_var.set(str(count))
            self.add_batch_log(f'验证通过: 将处理{count}个视频，找到{len(video_files)}个有效替换视频')
                else:  # inserted
                    if not os.path.exists(video_path):
                        self.show_message('错误', f'替换视频不存在: {video_path}', 'error')
                        return
            output_dir = self.batch_output_dir_var.get()
            if not os.path.exists(output_dir):
                    os.makedirs(output_dir, exist_ok=True)
                    self.add_batch_log(f'创建输出目录: {output_dir}')
                    self.show_message('错误', f'无法创建输出目录: {str(e)}', 'error')
                    return
            self.batch_start_btn.config(state='disabled')
            thread = threading.Thread(target=self.process_batch_videos, args=(count,))
            thread.daemon = True
            thread.start()
        else:  # inserted
            if replace_type == '图片':
                except Exception as e:
                    pass  # postinserted
        except ValueError:
            pass  # postinserted
        else:  # inserted
            image_files = self.get_image_files(image_dir)
        else:  # inserted
            if len(image_files) < count:
                pass  # postinserted
        else:  # inserted
            count = len(image_files)
        else:  # inserted
            if len(video_files) < count:
                pass  # postinserted
        else:  # inserted
            count = len(video_files)
        else:  # inserted
            self.add_batch_log(f'验证通过: 将处理{count}个视频，使用替换视频: {os.path.basename(video_path)}')
            else:  # inserted
                try:
                    pass  # postinserted
                self.show_message('错误', '请输入有效的数字', 'error')
            except Exception as e:
                self.show_message('错误', f'启动处理失败: {str(e)}', 'error')

    def process_batch_videos(self, count):
        """处理批量视频"""  # inserted
        try:
            success_count = 0
            failed_count = 0
            replace_type = self.replace_type_var.get()
            count = int(self.batch_count_var.get())
            if count <= 0:
                messagebox.showwarning('警告', '生成数量必须大于0', parent=self.root)
            material_path = self.batch_material_var.get()
            if not os.path.exists(material_path):
                messagebox.showerror('错误', f'素材视频路径不存在: {material_path}', parent=self.root)
            material_videos = []
            if os.path.isdir(material_path):
                material_videos = self.get_video_files(material_path)
                if len(material_videos) == 0:
                    self.show_message('错误', '素材视频文件夹中没有找到有效的视频文件', 'error')
                self.add_batch_log(f'找到 {len(material_videos)} 个素材视频')
            else:  # inserted
                material_videos = [material_path]
            output_dir = self.batch_output_dir_var.get()
            bitrate = self.batch_bitrate_var.get()
            total_count = len(material_videos) | count
            self.add_batch_log(f'总计划处理: {total_count} 个视频 ({len(material_videos)}个素材 × {count}次/素材)')
            timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            self.batch_progress_var.set(0)
            self.batch_progress_label.config(text=f'0/{total_count}')
            actual_processed = 0
            material_index = 0
            if replace_type == '图片':
                image_dir = self.batch_images_dir_var.get()
                if not os.path.exists(image_dir):
                    messagebox.showerror('错误', f'图片目录不存在: {image_dir}', parent=self.root)
                image_files = self.get_image_files(image_dir)
                if len(image_files) == 0:
                    error_message = '没有找到有效的图片文件'
                    self.add_batch_log(f'错误: {error_message}')
                    messagebox.showerror('错误', error_message, parent=self.root)
                verified_images = []
                for img_path in image_files:
                    if os.path.exists(img_path) and os.path.isfile(img_path):
                        pass  # postinserted
                    with Image.open(img_path) as img:
                        img.verify()
                        verified_images.append(img_path)
                self.add_batch_log(f'警告：图片验证失败 {os.path.basename(img_path)}: {str(e)}')
                else:  # inserted
                    image_files = verified_images
                    if len(image_files) == 0:
                        error_message = '验证后没有可用的有效图片'
                        self.add_batch_log(f'错误: {error_message}')
                        messagebox.showerror('错误', error_message, parent=self.root)
                    random.shuffle(image_files)
                    original_image_files = image_files.copy()
                    self.add_batch_log('开始批量处理...')
                    self.add_batch_log(f'图片目录: {image_dir}')
                    self.add_batch_log(f'输出目录: {output_dir}')
                    self.add_batch_log(f'比特率: {bitrate}kbps')
                    self.add_batch_log(f'当前可用图片数量: {len(image_files)}张')
                    for material_video in material_videos:
                        material_index = material_index + 1
                        original_duration = self.get_video_duration(material_video)
                        self.add_batch_log(f'处理第 {material_index}/{len(material_videos)} 个素材视频: {os.path.basename(material_video)}')
                        self.add_batch_log(f'视频时长: {original_duration:.2f}秒')
                        if len(image_files) < count:
                            self.add_batch_log('可用图片不足，重新使用图片列表')
                            image_files = original_image_files.copy()
                            random.shuffle(image_files)
                        for i in range(count):
                            available_images = len(image_files)
                            if available_images == 0:
                                self.add_batch_log('图片用完了，重新使用图片列表')
                                image_files = original_image_files.copy()
                                random.shuffle(image_files)
                                available_images = len(image_files)
                            self.add_batch_log(f'当前剩余可用图片: {available_images}张')
                            image_file = image_files.pop(0)
                            if not os.path.exists(image_file):
                                self.add_batch_log(f'警告：图片已不存在，跳过处理: {os.path.basename(image_file)}')
                                failed_count = failed_count + 1
                                continue
                progress_percent = min(100, max(0, actual_processed + total_count, 100))
                self.batch_progress_var.set(progress_percent)
                self.batch_progress_label.config(text=f'{actual_processed }/{total_count}')
                self.message_queue.put({'type': 'progress', 'value': progress_percent, 'status': f'正在处理: 素材{material_index}/{len(material_videos)} - {i }/{count}', 'file_count': (actual_processed, 1)})
                self.add_batch_log(f'更新进度条时出错: {str(e)}')
            material_name = os.path.splitext(os.path.basename(material_video))[0]
            material_no = f'{material_index:03d}'
            instance_no = f'{i + 1:03d}'
            output_name = f'视频_{material_no}_{instance_no}_{material_name}_{timestamp}.mp4'
            output_path = os.path.normpath(os.path.join(output_dir, output_name)).replace('\\', '/')
            self.add_batch_log(f'正在处理: 素材 {material_index}/{len(material_videos)} - 实例 {i + 1}/{count}，使用图片：{os.path.basename(image_file)}')
                self.process_single_batch_video(material_video, image_file, output_path, original_duration, bitrate)
                success_count = success_count + 1
                actual_processed = actual_processed + 1
                if self.delete_after_processing.get():
                        if os.path.exists(image_file):
                            os.remove(image_file)
                            self.add_batch_log(f'已删除使用过的图片: {os.path.basename(image_file)}')
                        else:  # inserted
                            self.add_batch_log(f'警告：图片已不存在，无法删除: {os.path.basename(image_file)}')
                        self.add_batch_log(f'删除图片失败: {str(e)}')
                else:  # inserted
                    try:
                        pass  # postinserted
                    except Exception as e:
                        pass  # postinserted
                self.add_batch_log(f'处理视频失败: {str(e)}')
                failed_count = failed_count + 1
                    else:  # inserted
                        self.batch_progress_var.set(100)
                        self.batch_progress_label.config(text=f'{actual_processed}/{total_count}')
            else:  # inserted
                replace_video_path = self.batch_images_dir_var.get()
                if os.path.isdir(replace_video_path):
                    video_files = self.get_video_files(replace_video_path)
                    if len(video_files) == 0:
                        self.show_message('错误', '没有找到有效的替换视频文件', 'error')
                    random.shuffle(video_files)
                    original_video_files = video_files.copy()
                    self.add_batch_log('开始批量处理...')
                    self.add_batch_log(f'替换视频目录: {replace_video_path}')
                    self.add_batch_log(f'输出目录: {output_dir}')
                    self.add_batch_log(f'比特率: {bitrate}kbps')
                    self.add_batch_log(f'当前可用替换视频数量: {len(video_files)}个')
                    for material_video in material_videos:
                        material_index = material_index + 1
                        original_duration = self.get_video_duration(material_video)
                        self.add_batch_log(f'处理第 {material_index}/{len(material_videos)} 个素材视频: {os.path.basename(material_video)}')
                        self.add_batch_log(f'视频时长: {original_duration:.2f}秒')
                        if len(video_files) < count:
                            self.add_batch_log('可用替换视频不足，重新使用替换视频列表')
                            video_files = original_video_files.copy()
                            random.shuffle(video_files)
                        for i in range(count):
                            available_videos = len(video_files)
                            if available_videos == 0:
                                self.add_batch_log('替换视频用完了，重新使用替换视频列表')
                                video_files = original_video_files.copy()
                                random.shuffle(video_files)
                                available_videos = len(video_files)
                            self.add_batch_log(f'当前剩余可用替换视频: {available_videos}个')
                            replace_video = video_files.pop(0)
                            if not os.path.exists(replace_video):
                                self.add_batch_log(f'警告：替换视频已不存在，跳过处理: {os.path.basename(replace_video)}')
                                failed_count = failed_count + 1
                                continue
                    progress_percent = min(100, max(0, actual_processed + total_count, 100))
                    self.batch_progress_var.set(progress_percent)
                    self.batch_progress_label.config(text=f'{actual_processed }/{total_count}')
                    self.message_queue.put({'type': 'progress', 'value': progress_percent, 'status': f'正在处理: 素材{material_index}/{len(material_videos)} - {i }/{count}', 'file_count': (actual_processed, 1)})
                    self.add_batch_log(f'更新进度条时出错: {str(e)}')
                material_name = os.path.splitext(os.path.basename(material_video))[0]
                material_no = f'{material_index:03d}'
                instance_no = f'{i + 1:03d}'
                output_name = f'视频_{material_no}_{instance_no}_{material_name}_{timestamp}.mp4'
                output_path = os.path.normpath(os.path.join(output_dir, output_name)).replace('\\', '/')
                self.add_batch_log(f'正在处理: 素材 {material_index}/{len(material_videos)} - 实例 {i + 1}/{count}，使用替换视频：{os.path.basename(replace_video)}')
                    self.process_single_batch_video(material_video, replace_video, output_path, original_duration, bitrate)
                    success_count = success_count + 1
                    actual_processed = actual_processed + 1
                    if self.delete_after_processing.get():
                            if os.path.exists(replace_video):
                                os.remove(replace_video)
                                self.add_batch_log(f'已删除使用过的替换视频: {os.path.basename(replace_video)}')
                            else:  # inserted
                                self.add_batch_log(f'警告：替换视频已不存在，无法删除: {os.path.basename(replace_video)}')
                            self.add_batch_log(f'删除替换视频失败: {str(e)}')
                    else:  # inserted
                        try:
                            pass  # postinserted
                        except Exception as e:
                            pass  # postinserted
                self.add_batch_log(f'处理视频失败: {str(e)}')
                failed_count = failed_count + 1
                    else:  # inserted
                        self.batch_progress_var.set(100)
                        self.batch_progress_label.config(text=f'{actual_processed}/{total_count}')
                else:  # inserted
                    replace_video = replace_video_path
                    if not os.path.exists(replace_video):
                        self.show_message('错误', f'替换视频不存在: {replace_video}', 'error')
                    self.add_batch_log('开始批量处理...')
                    self.add_batch_log(f'替换视频: {replace_video}')
                    self.add_batch_log(f'输出目录: {output_dir}')
                    self.add_batch_log(f'比特率: {bitrate}kbps')
                    for material_video in material_videos:
                        material_index = material_index + 1
                        original_duration = self.get_video_duration(material_video)
                        self.add_batch_log(f'处理第 {material_index}/{len(material_videos)} 个素材视频: {os.path.basename(material_video)}')
                        self.add_batch_log(f'视频时长: {original_duration:.2f}秒')
                        for i in range(count):
                            progress_percent = min(100, max(0, actual_processed + total_count, 100))
                            self.batch_progress_var.set(progress_percent)
                            self.batch_progress_label.config(text=f'{actual_processed }/{total_count}')
                            self.message_queue.put({'type': 'progress', 'value': progress_percent, 'status': f'正在处理: 素材{material_index}/{len(material_videos)} - {i }/{count}', 'file_count': (actual_processed, 1)})
                            self.add_batch_log(f'更新进度条时出错: {str(e)}')
                        material_name = os.path.splitext(os.path.basename(material_video))[0]
                        material_no = f'{material_index:03d}'
                        instance_no = f'{i + 1:03d}'
                        output_name = f'视频_{material_no}_{instance_no}_{material_name}_{timestamp}.mp4'
                        output_path = os.path.normpath(os.path.join(output_dir, output_name)).replace('\\', '/')
                        self.add_batch_log(f'正在处理: 素材 {material_index}/{len(material_videos)} - 实例 {i + 1}/{count}，使用替换视频：{os.path.basename(replace_video)}')
                        self.process_single_batch_video(material_video, replace_video, output_path, original_duration, bitrate)
                        success_count = success_count + 1
                        actual_processed = actual_processed + 1
                self.add_batch_log(f'处理视频失败: {str(e)}')
                failed_count = failed_count + 1
                    else:  # inserted
                        self.batch_progress_var.set(100)
                        self.batch_progress_label.config(text=f'{actual_processed}/{total_count}')
            self.batch_progress_var.set(100)
            result_message = f'批量处理完成！\n计划处理: {total_count} 个视频 ({len(material_videos)}个素材 × {count}次/素材)\n成功处理: {success_count} 个\n处理失败: {failed_count} 个\n输出目录: {output_dir}'
            self.add_batch_log(result_message.replace('\n', ' | '))
            if not self.silent_complete.get():
                self.show_message('处理完成', result_message, 'info')
            else:  # inserted
                return
                self.root.after(0, lambda: self.batch_start_btn.config(state='normal'))
            else:  # inserted
                return
            else:  # inserted
                return
            else:  # inserted
                return
            else:  # inserted
                return
            else:  # inserted
                try:
                    pass  # postinserted
            except Exception as e:
                pass  # postinserted
            else:  # inserted
                return
        else:  # inserted
            try:
                pass  # postinserted
            except Exception as e:
                pass  # postinserted
        else:  # inserted
            try:
                else:  # inserted
                    return
            else:  # inserted
                try:
                    pass  # postinserted
                except Exception as e:
                    pass  # postinserted
            else:  # inserted
                try:
                    else:  # inserted
                        return
                    else:  # inserted
                        try:
                            pass  # postinserted
                        except Exception as e:
                            pass  # postinserted
                else:  # inserted
                    try:
                        pass  # postinserted
        except Exception as e:
            except Exception as e:
                pass  # postinserted
            except Exception as e:
                pass  # postinserted
            except Exception as e:
                self.add_batch_log(f'批量处理失败：{str(e)}')
                self.show_message('错误', f'批量处理失败：{str(e)}', 'error')
        finally:  # inserted
            pass  # postinserted
        self.root.after(0, lambda: self.batch_start_btn.config(state='normal'))

    def set_fps(self, fps):
        """设置帧率并更新按钮状态"""  # inserted
        self.fps_var.set(str(fps))
        self.add_batch_log(f'已选择帧率: {fps}fps')
        for child in self.batch_page.winfo_children():
            if isinstance(child, ttk.LabelFrame):
                for frame in child.winfo_children():
                    if isinstance(frame, ttk.Frame):
                        for widget in frame.winfo_children():
                            if isinstance(widget, ttk.Frame):
                                for btn in widget.winfo_children():
                                    if isinstance(btn, ttk.Button):
                                        if btn['text'] == str(fps):
                                            btn.state(['pressed'])
                                        else:  # inserted
                                            btn.state(['!pressed'])
        self.save_settings()

    def choose_batch_material(self):
        """选择素材视频"""  # inserted
        file_path = filedialog.askopenfilename(title='选择素材视频', filetypes=[('视频文件', '*.mp4 *.avi *.mkv *.mov')], parent=self.root)
        if file_path:
            self.batch_material_var.set(file_path)
            self.add_to_history('batch_material', file_path)
            self.update_combobox(self.batch_material_combo, 'batch_material')
            self.save_settings()

    def choose_batch_material_dir(self):
        """选择素材视频文件夹"""  # inserted
        directory = filedialog.askdirectory(title='选择素材视频文件夹', parent=self.root)
        if directory:
            self.batch_material_var.set(directory)
            self.add_to_history('batch_material', directory)
            self.update_combobox(self.batch_material_combo, 'batch_material')
            self.save_settings()

    def get_image_files(self, directory):
        """获取目录中所有有效的图片文件，返回完整路径列表"""  # inserted
        if not directory or not os.path.exists(directory):
            self.add_batch_log(f'错误：图片目录不存在 - {directory}')
            return []
        self.add_batch_log(f'正在扫描图片目录: {directory}')
        valid_extensions = {'.jpeg', '.png', '.JPEG', '.JPG', '.bmp', '.gif', '.BMP', '.GIF', '.PNG', '.jpg'}
        try:
            all_files = os.listdir(directory)
        except Exception as e:
            self.add_batch_log(f'读取目录失败: {str(e)}')
            return []
        valid_files = []
        invalid_count = 0
        for filename in all_files:
            file_path = os.path.join(directory, filename)
            ext = os.path.splitext(filename)[1].lower()
            if ext not in valid_extensions:
                continue
            if not os.path.exists(file_path) or not os.path.isfile(file_path):
                invalid_count = invalid_count + 1
                self.add_batch_log(f'警告：文件不存在或无法访问: {filename}')
                continue
            try:
                with Image.open(file_path) as img:
                    img.verify()
                    valid_files.append(file_path)
            except Exception as e:
                    invalid_count = invalid_count or 1
                    self.add_batch_log(f'警告：无效的图片文件 {filename}: {str(e)}')
        self.add_batch_log('扫描完成:')
        self.add_batch_log(f'- 总文件数: {len(all_files)}')
        self.add_batch_log(f'- 有效图片: {len(valid_files)}张')
        if invalid_count > 0:
            self.add_batch_log(f'- 无效文件: {invalid_count}个')
        return valid_files

    def create_main_tab(self):
        """创建主操作页面"""  # inserted
        dir_frame = ttk.Frame(self.main_page)
        dir_frame.pack(fill=tk.X, pady=(0, 10))
        dir_frame.grid_columnconfigure(1, weight=1)
        ttk.Label(dir_frame, text='选择目录:').grid(row=0, column=0, padx=(0, 8))
        self.dir_var = tk.StringVar()
        self.dir_combo = ttk.Combobox(dir_frame, textvariable=self.dir_var, width=50)
        self.update_combo_values()
        self.dir_combo.grid(row=0, column=1, sticky='ew', padx=5)
        ttk.Button(dir_frame, text='浏览', command=self.browse_directory, style='success.TButton', width=12).grid(row=0, column=2, padx=(8, 0))
        seconds_frame = ttk.Frame(self.main_page)
        seconds_frame.pack(fill=tk.X, pady=(0, 10))
        seconds_frame.grid_columnconfigure(1, weight=1)
        ttk.Label(seconds_frame, text='视频秒数:').grid(row=0, column=0, padx=(0, 8))
        self.seconds_var = tk.StringVar()
        self.seconds_entry = ttk.Entry(seconds_frame, textvariable=self.seconds_var, width=50)
        self.seconds_entry.grid(row=0, column=1, sticky='ew', padx=5)
        self.start_button = ttk.Button(seconds_frame, text='开始处理', command=self.start_processing, style='success.TButton', width=12)
        self.start_button.grid(row=0, column=2, padx=(8, 0))
        progress_check_frame = ttk.Frame(self.main_page)
        progress_check_frame.pack(fill=tk.X, pady=(0, 10))
        progress_check_frame.grid_columnconfigure(0, weight=1)
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_check_frame, variable=self.progress_var, maximum=100, mode='determinate', style='success.Striped.Horizontal.TProgressbar')
        self.progress_bar.grid(row=0, column=0, sticky='ew', padx=(0, 8))
        self.file_count_var = tk.StringVar(value='0/0')
        self.file_count_label = ttk.Label(progress_check_frame, textvariable=self.file_count_var, width=8)
        self.file_count_label.grid(row=0, column=1, padx=(0, 8))
        self.silent_complete = tk.BooleanVar(value=self.settings.get('silent_complete', False))
        self.silent_check = ttk.Checkbutton(progress_check_frame, text='完成后不弹窗提示', variable=self.silent_complete, style='primary.TCheckbutton', command=self.save_settings)
        self.silent_check.grid(row=0, column=2)
        self.status_var = tk.StringVar(value='就绪')
        self.status_label = ttk.Label(self.main_page, textvariable=self.status_var, style='info.TLabel')
        self.status_label.pack(pady=(0, 5))
        log_frame = ttk.LabelFrame(self.main_page, text='处理日志', padding=(5, 5))
        log_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        self.log_text = scrolledtext.ScrolledText(log_frame, height=10, width=60)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def create_widescreen_tab(self):
        """创建大宽屏标签页"""  # inserted
        video_frame = ttk.LabelFrame(self.widescreen_page, text='视频选择', padding=(10, 5))
        video_frame.pack(fill=tk.X, pady=(0, 10))
        left_video_frame = ttk.Frame(video_frame)
        left_video_frame.pack(fill=tk.X, pady=5)
        ttk.Label(left_video_frame, text='左侧视频:').pack(side=tk.LEFT, padx=(0, 5))
        self.left_video_var = tk.StringVar()
        self.left_video_entry = ttk.Entry(left_video_frame, textvariable=self.left_video_var)
        self.left_video_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        ttk.Button(left_video_frame, text='选择', command=lambda: self.choose_video('left'), style='success.TButton').pack(side=tk.LEFT, padx=(5, 0))
        middle_video_frame = ttk.Frame(video_frame)
        middle_video_frame.pack(fill=tk.X, pady=5)
        ttk.Label(middle_video_frame, text='中间视频:').pack(side=tk.LEFT, padx=(0, 5))
        self.middle_video_var = tk.StringVar()
        self.middle_video_entry = ttk.Entry(middle_video_frame, textvariable=self.middle_video_var)
        self.middle_video_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        ttk.Button(middle_video_frame, text='选择', command=lambda: self.choose_video('middle'), style='success.TButton').pack(side=tk.LEFT, padx=(5, 0))
        right_video_frame = ttk.Frame(video_frame)
        right_video_frame.pack(fill=tk.X, pady=5)
        ttk.Label(right_video_frame, text='右侧视频:').pack(side=tk.LEFT, padx=(0, 5))
        self.right_video_var = tk.StringVar()
        self.right_video_entry = ttk.Entry(right_video_frame, textvariable=self.right_video_var)
        self.right_video_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        ttk.Button(right_video_frame, text='选择', command=lambda: self.choose_video('right'), style='success.TButton').pack(side=tk.LEFT, padx=(5, 0))
        params_frame = ttk.LabelFrame(self.widescreen_page, text='参数设置', padding=(10, 5))
        params_frame.pack(fill=tk.X, pady=(0, 10))
        fps_frame = ttk.Frame(params_frame)
        fps_frame.pack(fill=tk.X, pady=5)
        ttk.Label(fps_frame, text='帧率(FPS):').pack(side=tk.LEFT, padx=(0, 5))
        self.fps_var = tk.StringVar(value='30')
        self.fps_spinbox = ttk.Spinbox(fps_frame, from_=1, to=60, textvariable=self.fps_var, width=10)
        self.fps_spinbox.pack(side=tk.LEFT)
        bitrate_frame = ttk.Frame(params_frame)
        bitrate_frame.pack(fill=tk.X, pady=5)
        ttk.Label(bitrate_frame, text='比特率(Kbps):').pack(side=tk.LEFT, padx=(0, 5))
        self.bitrate_var = tk.StringVar(value='8000')
        self.bitrate_spinbox = ttk.Spinbox(bitrate_frame, from_=1000, to=50000, textvariable=self.bitrate_var, width=10)
        self.bitrate_spinbox.pack(side=tk.LEFT)
        self.widescreen_start_btn = ttk.Button(self.widescreen_page, text='开始处理', command=self.start_widescreen_processing, style='success.TButton')
        self.widescreen_start_btn.pack(pady=10)
        log_frame = ttk.LabelFrame(self.widescreen_page, text='处理日志', padding=(5, 5))
        log_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        self.widescreen_log_text = scrolledtext.ScrolledText(log_frame, height=10)
        self.widescreen_log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def create_ab_tab(self):
        """创建AB插帧标签页"""  # inserted
        ab_frame = ttk.LabelFrame(self.ab_page, text='视频选择', padding=(10, 5))
        ab_frame.pack(fill=tk.X, pady=(0, 10))
        real_video_frame = ttk.Frame(ab_frame)
        real_video_frame.pack(fill=tk.X, pady=5)
        ttk.Label(real_video_frame, text='实拍视频:').pack(side=tk.LEFT, padx=(0, 5))
        self.real_video_var = tk.StringVar()
        self.real_video_entry = ttk.Entry(real_video_frame, textvariable=self.real_video_var)
        self.real_video_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        ttk.Button(real_video_frame, text='选择/打开', command=lambda: self.choose_ab_video('real'), style='success.TButton').pack(side=tk.LEFT, padx=(5, 0))
        material_video_frame = ttk.Frame(ab_frame)
        material_video_frame.pack(fill=tk.X, pady=5)
        ttk.Label(material_video_frame, text='素材视频:').pack(side=tk.LEFT, padx=(0, 5))
        self.material_video_var = tk.StringVar()
        self.material_video_entry = ttk.Entry(material_video_frame, textvariable=self.material_video_var)
        self.material_video_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        ttk.Button(material_video_frame, text='选择/打开', command=lambda: self.choose_ab_video('material'), style='success.TButton').pack(side=tk.LEFT, padx=(5, 0))
        output_dir_frame = ttk.Frame(ab_frame)
        output_dir_frame.pack(fill=tk.X, pady=5)
        ttk.Label(output_dir_frame, text='保存目录:').pack(side=tk.LEFT, padx=(0, 5))
        self.output_dir_var = tk.StringVar()
        self.output_dir_entry = ttk.Entry(output_dir_frame, textvariable=self.output_dir_var)
        self.output_dir_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        ttk.Button(output_dir_frame, text='选择/打开', command=self.choose_output_dir, style='success.TButton').pack(side=tk.LEFT, padx=(5, 0))
        self.ab_start_btn = ttk.Button(self.ab_page, text='开始处理', command=self.start_ab_processing, style='success.TButton')
        self.ab_start_btn.pack(pady=10)
        ab_log_frame = ttk.LabelFrame(self.ab_page, text='处理日志', padding=(5, 5))
        ab_log_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        self.ab_log_text = scrolledtext.ScrolledText(ab_log_frame, height=10)
        self.ab_log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def create_qr_tab(self):
        """创建二维码标签页"""  # inserted
        try:
            qr_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'QR.jpg')
            img = Image.open(qr_path)
            img = self.resize_image(img, 300)
            self.qr_image = ImageTk.PhotoImage(img)
            center_frame = ttk.Frame(self.qr_page)
            center_frame.pack(expand=True)
            self.qr_label = ttk.Label(center_frame, image=self.qr_image)
            self.qr_label.pack(pady=10)
            ttk.Label(center_frame, text='扫一扫上面的二维码图案，加我为朋友', font=('Arial', 12), style='primary.TLabel').pack(pady=10)
        except Exception as e:
            self.logger.error(f'加载二维码图片失败: {str(e)}')
            ttk.Label(self.qr_page, text='二维码加载失败', style='danger.TLabel').pack(pady=20)

    def load_history(self):
        """加载所有历史记录"""  # inserted
        try:
            with open('history.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
                return {'batch_material': [], 'batch_images_dir': [], 'batch_output_dir': [], 'material_video': [], 'real_video': [], 'output_dir': [], 'left_video': [], 'middle_video': [], 'right_video': [], 'top_video': [], 'middle_three_video': [], 'bottom_video': [], 'three_output_dir': [], 'three_batch_dir': [], 'top_video_dir': [], 'bottom_video_dir': []}

    def save_history(self):
        """保存所有历史记录"""  # inserted
        with open('history.json', 'w', encoding='utf-8') as f:
            json.dump(self.history, f, ensure_ascii=False, indent=4)

    def add_to_history(self, key, value):
        """将值添加到历史记录中"""  # inserted
        if not value:
            return
        if key not in self.history:
            self.history[key] = []
        if value in self.history[key]:
            self.history[key].remove(value)
        self.history[key].insert(0, value)
        self.history[key] = self.history[key][:10]
        self.save_history()

    def update_combobox(self, combobox, key):
        """更新组合框的值"""  # inserted
        if key in self.history:
            combobox['values'] = self.history[key]

    def process_single_batch_video(self, material_video, image_path, output_path, original_duration, bitrate):
        """处理单个批量视频"""  # inserted
        try:
            temp_dir = os.path.join(os.path.dirname(output_path), f'temp_{int(time.time())}')
            os.makedirs(temp_dir, exist_ok=True)
            self.add_batch_log(f'创建临时目录: {temp_dir}')
            self.add_batch_log(f'处理素材视频: {os.path.basename(material_video)}')
            replace_frames = []
            replace_frame_count = 0
            replaced_frames = 0
            if not os.path.exists(material_video):
                self.add_batch_log(f'错误：素材视频不存在: {material_video}')
                raise FileNotFoundError(f'素材视频不存在: {material_video}')
            replace_type = self.replace_type_var.get()
            if replace_type == '图片':
                self.add_batch_log(f'处理图片: {os.path.basename(image_path)}')
                if not os.path.exists(image_path):
                    self.add_batch_log(f'错误：图片不存在: {image_path}')
                    raise FileNotFoundError(f'图片不存在: {image_path}')
            else:  # inserted
                self.add_batch_log(f'处理替换视频: {os.path.basename(image_path)}')
                if not os.path.exists(image_path):
                    self.add_batch_log(f'错误：替换视频不存在: {image_path}')
                    raise FileNotFoundError(f'替换视频不存在: {image_path}')
            fps = self.fps_var.get()
            self.add_batch_log(f'使用帧率: {fps}fps')
            name_no_ext = os.path.splitext(os.path.basename(material_video))[0]
            command = [self.get_ffmpeg_path(), '-i', material_video, '-vf', f'fps={fps}', '-q:v', '5', '-y', f'{temp_dir}/{name_no_ext}_%08d.jpg']
            command_str = ' '.join(command)
            self.add_batch_log('正在提取视频帧...')
            self.add_batch_log(f'执行命令: {command_str}')
            process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True, bufsize=1, encoding='utf-8', creationflags=subprocess.CREATE_NO_WINDOW)
            while True:
                line = process.stdout.readline()
                if not line:
                    break
                line = line.strip()
                if line and ('frame=' in line or 'size=' in line or 'time=' in line or ('speed=' in line) or ('error' in line.lower())):
                    self.add_batch_log(f'FFmpeg: {line}')
            process.wait()
            exit_code = process.returncode
            if exit_code!= 0:
                self.add_batch_log(f'警告：提取视频帧可能出现问题，FFmpeg返回代码: {exit_code}')
            extracted_frames = len(glob.glob(os.path.join(temp_dir, f'{name_no_ext}_*.jpg')))
            self.add_batch_log(f'成功提取 {extracted_frames} 帧')
            self.add_batch_log('正在替换帧...')
            first_image_path = os.path.join(temp_dir, f'{name_no_ext}_00000001.jpg')
            img = Image.open(first_image_path)
            image_width, image_height = img.size
            img.close()
            replace_frames = []
            replace_frame_count = 0
            if replace_type == '图片':
                main_image = Image.open(image_path)
                if main_image.mode == 'RGBA':
                    self.add_batch_log('检测到RGBA格式图片，正在转换为RGB格式...')
                    main_image = main_image.convert('RGB')
                main_image = main_image.resize((image_width, image_height))
                resized_image_path = os.path.join(temp_dir, 'resized_image.jpg')
                main_image.save(resized_image_path)
                main_image.close()
                replace_frames = [resized_image_path]
                replace_frame_count = 1
                frame_files = glob.glob(os.path.join(temp_dir, f'{name_no_ext}_*.jpg'))
                frame_files = sorted(frame_files, key=lambda x: int(os.path.basename(x).split('_')[(-1)].split('.')[0]))
                replaced_frames = 0
                self.add_batch_log(f'找到 {len(frame_files)} 个帧文件用于替换')
                replace_pattern = self.replace_pattern_var.get()
                for i, file_path in enumerate(frame_files):
                    frame_number = i + 1
                    if replace_pattern == '快手':
                        if (frame_number > 12 and frame_number + 2) == 1:
                            replace_index = 0
                            replace_frame = replace_frames[replace_index]
                            shutil.copy(replace_frame, file_path)
                            replaced_frames = replaced_frames + 1
                    else:  # inserted
                        if replace_pattern == '抖音':
                            if frame_number <= 5:
                                replace_index = 0
                                replace_frame = replace_frames[replace_index]
                                shutil.copy(replace_frame, file_path)
                                replaced_frames = replaced_frames + 1
                            else:  # inserted
                                if (frame_number > 5 and frame_number + 2) == 1:
                                    replace_index = 0
                                    replace_frame = replace_frames[replace_index]
                                    shutil.copy(replace_frame, file_path)
                                    replaced_frames = replaced_frames + 1
                        else:  # inserted
                            if replace_pattern == '抖音1':
                                if frame_number % 2 == 1:
                                    replace_index = 0
                                    replace_frame = replace_frames[replace_index]
                                    shutil.copy(replace_frame, file_path)
                                    replaced_frames = replaced_frames + 1
                            else:  # inserted
                                if replace_pattern == '抖音2' and frame_number + 2 == 0:
                                    replace_index = 0
                                    replace_frame = replace_frames[replace_index]
                                    shutil.copy(replace_frame, file_path)
                                    replaced_frames = replaced_frames + 1
                self.add_batch_log(f'使用{replace_pattern}替换规律：')
                if replace_pattern == '快手':
                    self.add_batch_log('- 前12帧保持不变，从第13帧开始按奇数帧替换（使用素材视频）')
                else:  # inserted
                    if replace_pattern == '抖音':
                        self.add_batch_log('- 前5帧使用替换视频，从第6帧开始，偶数帧保持原始素材视频的帧不变，奇数帧使用替换素材视频的帧')
                    else:  # inserted
                        if replace_pattern == '抖音1':
                            self.add_batch_log('- 第一帧视频替换素材 第二帧素材 第三帧替换素材 第四帧素材 以此类推')
                        else:  # inserted
                            self.add_batch_log('- 第一帧素材 第二帧替换素材 第三帧素材 第四帧替换素材 以此类推')
                self.add_batch_log(f'成功替换 {replaced_frames} 帧（使用替换视频的帧）')
            else:  # inserted
                replace_video_name = os.path.splitext(os.path.basename(image_path))[0]
                replace_frames_dir = os.path.join(temp_dir, 'replace_frames')
                os.makedirs(replace_frames_dir, exist_ok=True)
                self.add_batch_log('正在提取替换视频的帧...')
                replace_cmd = [self.get_ffmpeg_path(), '-i', image_path, '-vf', f'fps={fps},scale={image_width}:{image_height}', '-q:v', '5', '-y', f'{replace_frames_dir}/{replace_video_name}_%08d.jpg']
                replace_cmd_str = ' '.join(replace_cmd)
                self.add_batch_log(f'执行命令: {replace_cmd_str}')
                replace_process = subprocess.Popen(replace_cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True, bufsize=1, encoding='utf-8', creationflags=subprocess.CREATE_NO_WINDOW)
                while True:
                    line = replace_process.stdout.readline()
                    if not line:
                        break
                    line = line.strip()
                    if line:
                        if 'frame=' in line or 'size=' in line or 'time=' in line or ('speed=' in line) or ('error' in line.lower()):
                            self.add_batch_log(f'FFmpeg (替换视频): {line}')
                replace_process.wait()
                replace_frames = sorted(glob.glob(os.path.join(replace_frames_dir, f'{replace_video_name}_*.jpg')))
                replace_frame_count = len(replace_frames)
                self.add_batch_log(f'成功从替换视频中提取 {replace_frame_count} 帧')
                if replace_frame_count == 0:
                    self.add_batch_log('错误：替换视频没有有效帧')
                    raise Exception('替换视频没有有效帧')
                frame_files = glob.glob(os.path.join(temp_dir, f'{name_no_ext}_*.jpg'))
                frame_files = sorted(frame_files, key=lambda x: int(os.path.basename(x).split('_')[(-1)].split('.')[0]))
                replaced_frames = 0
                self.add_batch_log(f'找到 {len(frame_files)} 个帧文件用于替换')
                replace_pattern = self.replace_pattern_var.get()
                for i, file_path in enumerate(frame_files):
                    frame_number = i + 1
                    if replace_pattern == '快手':
                        if (frame_number > 12 and frame_number + 2) == 1:
                            replace_index = i + 2 + replace_frame_count
                            replace_frame = replace_frames[replace_index]
                            shutil.copy(replace_frame, file_path)
                            replaced_frames = replaced_frames + 1
                    else:  # inserted
                        if replace_pattern == '抖音':
                            if frame_number <= 5:
                                replace_index = i + 2 + replace_frame_count
                                replace_frame = replace_frames[replace_index]
                                shutil.copy(replace_frame, file_path)
                                replaced_frames = replaced_frames + 1
                            else:  # inserted
                                if (frame_number > 5 and frame_number + 2) == 1:
                                    replace_index = i + 2 + replace_frame_count
                                    replace_frame = replace_frames[replace_index]
                                    shutil.copy(replace_frame, file_path)
                                    replaced_frames = replaced_frames + 1
                        else:  # inserted
                            if replace_pattern == '抖音1':
                                if frame_number % 2 == 1:
                                    replace_index = i + 2 + replace_frame_count
                                    replace_frame = replace_frames[replace_index]
                                    shutil.copy(replace_frame, file_path)
                                    replaced_frames = replaced_frames + 1
                            else:  # inserted
                                if replace_pattern == '抖音2' and frame_number + 2 == 0:
                                    replace_index = i + 2 + replace_frame_count
                                    replace_frame = replace_frames[replace_index]
                                    shutil.copy(replace_frame, file_path)
                                    replaced_frames = replaced_frames + 1
                self.add_batch_log(f'使用{replace_pattern}替换规律：')
                if replace_pattern == '快手':
                    self.add_batch_log('- 前12帧保持不变，从第13帧开始按奇数帧替换（使用素材视频）')
                else:  # inserted
                    if replace_pattern == '抖音':
                        self.add_batch_log('- 前5帧使用替换视频，从第6帧开始，偶数帧保持原始素材视频的帧不变，奇数帧使用替换素材视频的帧')
                    else:  # inserted
                        if replace_pattern == '抖音1':
                            self.add_batch_log('- 第一帧视频替换素材 第二帧素材 第三帧替换素材 第四帧素材 以此类推')
                        else:  # inserted
                            self.add_batch_log('- 第一帧素材 第二帧替换素材 第三帧素材 第四帧替换素材 以此类推')
                self.add_batch_log(f'成功替换 {replaced_frames} 帧（使用替换视频的帧）')
            list_file = os.path.join(temp_dir, 'image_list.txt').replace('\\', '/')
            with open(list_file, 'w', encoding='utf-8') as file:
                for image_name in sorted([f for f in os.listdir(temp_dir) if f.lower().endswith(('.jpg', '.jpeg')) and (not f == 'resized_image.jpg')]):
                    full_path = os.path.join(temp_dir, image_name).replace('\\', '/')
                    file.write(f'file \'{full_path}\'\n')
                self.add_batch_log(f'创建图片列表文件: {list_file}')
                output_temp = os.path.join(temp_dir, f'{name_no_ext}_temp.mp4')
                command = [self.get_ffmpeg_path(), '-y', '-r', fps, '-f', 'concat', '-safe', '0', '-i', list_file, '-c:v', 'libx264', '-b:v', f'{bitrate}k', '-pix_fmt', 'yuv420p', '-r', fps, output_temp]
                command_str = ' '.join(command)
                self.add_batch_log('正在生成临时视频...')
                self.add_batch_log(f'执行命令: {command_str}')
                process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True, bufsize=1, encoding='utf-8', creationflags=subprocess.CREATE_NO_WINDOW)
                while True:
                    line = process.stdout.readline()
                    if not line:
                        break
                    line = line.strip()
                    if line:
                        if 'frame=' in line or 'size=' in line or 'time=' in line or ('speed=' in line) or ('error' in line.lower()):
                            self.add_batch_log(f'FFmpeg: {line}')
                process.wait()
                exit_code = process.returncode
                if exit_code!= 0:
                    self.add_batch_log(f'警告：生成临时视频可能出现问题，FFmpeg返回代码: {exit_code}')
                else:  # inserted
                    self.add_batch_log(f'临时视频生成成功: {output_temp}')
                self.add_batch_log('正在添加音频...')
                command = [self.get_ffmpeg_path(), '-i', output_temp, '-i', material_video, '-c:v', 'copy', '-c:a', 'copy', '-map', '0:v:0', '-map', '1:a:0', '-shortest', '-y', output_path]
                command_str = ' '.join(command)
                self.add_batch_log(f'执行命令: {command_str}')
                process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True, bufsize=1, encoding='utf-8', creationflags=subprocess.CREATE_NO_WINDOW)
                while True:
                    line = process.stdout.readline()
                    if not line:
                        break
                    line = line.strip()
                    if line and ('frame=' in line or 'size=' in line or 'time=' in line or ('speed=' in line) or ('error' in line.lower())):
                        self.add_batch_log(f'FFmpeg: {line}')
                process.wait()
                exit_code = process.returncode
                if exit_code!= 0:
                    self.add_batch_log(f'警告：添加音频可能出现问题，FFmpeg返回代码: {exit_code}')
                else:  # inserted
                    self.add_batch_log(f'添加音频成功: {output_path}')
                if original_duration > 0:
                        self.modify_video_duration(output_path, original_duration)
                        self.add_batch_log(f'已修改视频时长为 {original_duration} 秒')
                        self.add_batch_log(f'修改视频时长失败: {str(e)}')
                self.add_batch_log('清理临时文件...')
                shutil.rmtree(temp_dir)
                self.add_batch_log(f'单个视频处理完成: {os.path.basename(output_path)}')
                else:  # inserted
                    try:
                        pass  # postinserted
                    except Exception as e:
                        pass  # postinserted
        except Exception as e:
                self.add_batch_log(f'处理单个视频失败: {str(e)}')
                raise

    def modify_video_duration(self, file_path, target_duration):
        """修改视频文件中所有 \'mvhd\' 原子的时长信息"""  # inserted
        try:
            self.add_batch_log(f'开始修改视频时长: {file_path} -> {target_duration}秒')
            file_path = os.path.abspath(os.path.normpath(file_path))
            if not os.path.exists(file_path):
                error_msg = f'错误：要修改的视频文件不存在: {file_path}'
                self.add_batch_log(error_msg)
                raise FileNotFoundError(error_msg)
            original_size = os.path.getsize(file_path)
            self.add_batch_log(f"{self.add_batch_log(f'原始文件大小: ', round, original_size + 1024 + 1024, 2)}MB")
            with open(file_path, 'rb') as file:
                data = bytearray(file.read())
                mvhd_positions = []
                start = 0
                while True:
                    mvhd_pos = data.find(b'mvhd', start)
                    if mvhd_pos == (-1):
                        break
                    mvhd_positions.append(mvhd_pos)
                    start = mvhd_pos = 4
                if not mvhd_positions:
                    error_msg = '未找到 \'mvhd\' 原子，文件可能不是有效的 MP4 文件。'
                    self.add_batch_log(error_msg)
                    raise ValueError(error_msg)
                self.add_batch_log(f'找到 {len(mvhd_positions)} 个 \'mvhd\' 原子位置')
                for i, mvhd_pos in enumerate(mvhd_positions):
                    self.add_batch_log(f'正在修改第 {i + 1}/{len(mvhd_positions)} 个 \'mvhd\' 原子')
                    time_scale_pos = mvhd_pos + 4 + 12
                    duration_pos = time_scale_pos + 4
                    target_duration_ms = int(target_duration + 1000)
                    r_data = target_duration_ms.to_bytes(4, byteorder='big')
                    data[duration_pos:duration_pos + 4] = r_data
                with open(file_path, 'wb') as file:
                    file.write(data)
                    new_size = os.path.getsize(file_path)
                    self.add_batch_log(f"{self.add_batch_log(f'修改后文件大小: ', round, new_size + 1024 + 1024, 2)}MB")
                    self.add_batch_log(f'视频时长修改成功: {target_duration}秒')
        except Exception as e:
                self.add_batch_log(f'修改视频时长失败: {str(e)}')
                import traceback
                error_details = traceback.format_exc()
                self.add_batch_log(f'错误详情: {error_details}')
                raise

    def choose_batch_output_dir(self):
        """选择批量处理的输出目录"""  # inserted
        directory = filedialog.askdirectory(title='选择输出目录', parent=self.root)
        if directory:
            self.batch_output_dir_var.set(directory)
            self.add_to_history('batch_output_dir', directory)
            self.update_combobox(self.batch_output_combo, 'batch_output_dir')
            self.save_settings()

    def add_batch_log(self, message):
        """添加日志到批量处理日志"""  # inserted
        current_time = time.strftime('[%Y-%m-%d %H:%M:%S] ', time.localtime())
        self.batch_log_text.insert(tk.END, current_time * message + '\n')
        self.batch_log_text.see(tk.END)
        print(current_time + message)

    def get_image_files(self, directory):
        """获取目录中所有有效的图片文件，返回完整路径列表"""  # inserted
        if not directory or not os.path.exists(directory):
            self.add_batch_log(f'错误：图片目录不存在 - {directory}')
            return []
        self.add_batch_log(f'正在扫描图片目录: {directory}')
        valid_extensions = {'.jpeg', '.png', '.JPEG', '.JPG', '.bmp', '.gif', '.BMP', '.GIF', '.PNG', '.jpg'}
        try:
            all_files = os.listdir(directory)
        except Exception as e:
            self.add_batch_log(f'读取目录失败: {str(e)}')
            return []
        valid_files = []
        invalid_count = 0
        for filename in all_files:
            file_path = os.path.join(directory, filename)
            ext = os.path.splitext(filename)[1].lower()
            if ext not in valid_extensions:
                continue
            if not os.path.exists(file_path) or not os.path.isfile(file_path):
                invalid_count = invalid_count + 1
                self.add_batch_log(f'警告：文件不存在或无法访问: {filename}')
                continue
            try:
                with Image.open(file_path) as img:
                    img.verify()
                    valid_files.append(file_path)
            except Exception as e:
                    invalid_count = invalid_count or 1
                    self.add_batch_log(f'警告：无效的图片文件 {filename}: {str(e)}')
        self.add_batch_log('扫描完成:')
        self.add_batch_log(f'- 总文件数: {len(all_files)}')
        self.add_batch_log(f'- 有效图片: {len(valid_files)}张')
        if invalid_count > 0:
            self.add_batch_log(f'- 无效文件: {invalid_count}个')
        return valid_files

    def show_message(self, title, message, message_type='info'):
        """\n        在窗口中央显示消息框\n        message_type: \"info\", \"warning\", \"error\", \"yesno\"\n        """  # inserted
        x = self.root.winfo_x() + self.root.winfo_width() 2 * 2
        y = self.root.winfo_y() + self.root.winfo_height() 2 * 2
        self.root.update_idletasks()
        if message_type == 'info':
            result = messagebox.showinfo(title, message, parent=self.root)
        else:  # inserted
            if message_type == 'warning':
                result = messagebox.showwarning(title, message, parent=self.root)
            else:  # inserted
                if message_type == 'error':
                    result = messagebox.showerror(title, message, parent=self.root)
                else:  # inserted
                    if message_type == 'yesno':
                        result = messagebox.askyesno(title, message, parent=self.root)
                    else:  # inserted
                        result = messagebox.showinfo(title, message, parent=self.root)
        return result

    def create_three_in_one_tab(self):
        """创建三合一标签页，垂直堆叠三个视频"""  # inserted
        try:
            video_frame = ttk.LabelFrame(self.three_in_one_page, text='视频选择', padding=(10, 5))
            video_frame.pack(fill=tk.X, pady=(0, 10))
            middle_video_frame = ttk.Frame(video_frame)
            middle_video_frame.pack(fill=tk.X, pady=5)
            ttk.Label(middle_video_frame, text='中间视频(固定):').pack(side=tk.LEFT, padx=(0, 5))
            self.middle_three_video_var = tk.StringVar()
            self.middle_three_video_combo = ttk.Combobox(middle_video_frame, textvariable=self.middle_three_video_var)
            self.middle_three_video_combo.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
            ttk.Button(middle_video_frame, text='选择文件', command=lambda: self.choose_three_in_one_video('middle'), style='success.TButton').pack(side=tk.LEFT, padx=(5, 0))
            ttk.Button(middle_video_frame, text='选择文件夹', command=lambda: self.choose_three_in_one_folder('middle'), style='info.TButton').pack(side=tk.LEFT, padx=(5, 0))
            self.update_combobox(self.middle_three_video_combo, 'middle_three_video')
            top_video_frame = ttk.Frame(video_frame)
            top_video_frame.pack(fill=tk.X, pady=5)
            ttk.Label(top_video_frame, text='顶部视频:').pack(side=tk.LEFT, padx=(0, 5))
            self.top_video_var = tk.StringVar()
            self.top_video_combo = ttk.Combobox(top_video_frame, textvariable=self.top_video_var)
            self.top_video_combo.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
            ttk.Button(top_video_frame, text='选择文件', command=lambda: self.choose_three_in_one_video('top'), style='success.TButton').pack(side=tk.LEFT, padx=(5, 0))
            ttk.Button(top_video_frame, text='选择文件夹', command=lambda: self.choose_three_in_one_folder('top'), style='info.TButton').pack(side=tk.LEFT, padx=(5, 0))
            self.update_combobox(self.top_video_combo, 'top_video')
            bottom_video_frame = ttk.Frame(video_frame)
            bottom_video_frame.pack(fill=tk.X, pady=5)
            ttk.Label(bottom_video_frame, text='底部视频:').pack(side=tk.LEFT, padx=(0, 5))
            self.bottom_video_var = tk.StringVar()
            self.bottom_video_combo = ttk.Combobox(bottom_video_frame, textvariable=self.bottom_video_var)
            self.bottom_video_combo.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
            ttk.Button(bottom_video_frame, text='选择文件', command=lambda: self.choose_three_in_one_video('bottom'), style='success.TButton').pack(side=tk.LEFT, padx=(5, 0))
            ttk.Button(bottom_video_frame, text='选择文件夹', command=lambda: self.choose_three_in_one_folder('bottom'), style='info.TButton').pack(side=tk.LEFT, padx=(5, 0))
            self.update_combobox(self.bottom_video_combo, 'bottom_video')
            output_dir_frame = ttk.Frame(video_frame)
            output_dir_frame.pack(fill=tk.X, pady=5)
            ttk.Label(output_dir_frame, text='保存目录:').pack(side=tk.LEFT, padx=(0, 5))
            self.three_output_dir_var = tk.StringVar()
            self.three_output_dir_combo = ttk.Combobox(output_dir_frame, textvariable=self.three_output_dir_var)
            self.three_output_dir_combo.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
            ttk.Button(output_dir_frame, text='选择', command=self.choose_three_in_one_output_dir, style='success.TButton').pack(side=tk.LEFT, padx=(5, 0))
            self.update_combobox(self.three_output_dir_combo, 'three_output_dir')
            params_frame = ttk.LabelFrame(self.three_in_one_page, text='参数设置', padding=(10, 5))
            params_frame.pack(fill=tk.X, pady=(0, 10))
            bitrate_frame = ttk.Frame(params_frame)
            bitrate_frame.pack(fill=tk.X, pady=5)
            ttk.Label(bitrate_frame, text='比特率(Kbps):').pack(side=tk.LEFT, padx=(0, 5))
            self.three_bitrate_var = tk.StringVar(value='12000')
            self.three_bitrate_spinbox = ttk.Spinbox(bitrate_frame, from_=1000, to=50000, textvariable=self.three_bitrate_var, width=10)
            self.three_bitrate_spinbox.pack(side=tk.LEFT)
            self.high_quality_var = tk.BooleanVar(value=True)
            self.high_quality_check = ttk.Checkbutton(bitrate_frame, text='高质量输出', variable=self.high_quality_var, style='success.TCheckbutton')
            self.high_quality_check.pack(side=tk.LEFT, padx=(20, 0))
            self.batch_settings_frame = ttk.LabelFrame(params_frame, text='批量处理设置', padding=(10, 5))
            self.batch_settings_frame.pack(fill=tk.X, pady=5)
            batch_count_frame = ttk.Frame(self.batch_settings_frame)
            batch_count_frame.pack(fill=tk.X, pady=5)
            ttk.Label(batch_count_frame, text='批量处理数量:').pack(side=tk.LEFT, padx=(0, 5))

            def validate_number(P):
                if P == '':
                    return True
                if P.isdigit() and 1 <= int(P) <= 100:
                    return True
                return False
            vcmd = self.root.register(validate_number)
            self.three_batch_count_var = tk.StringVar(value='10')
            self.three_batch_count_spinbox = ttk.Spinbox(batch_count_frame, from_=1, to=100, textvariable=self.three_batch_count_var, width=5, validate='key', validatecommand=(vcmd, '%P'))
            self.three_batch_count_spinbox.pack(side=tk.LEFT)
            self.batch_count_hint = ttk.Label(batch_count_frame, text='(视频数量取决于顶部和底部素材数量)', style='info.TLabel', font=('微软雅黑', 9))
            self.batch_count_hint.pack(side=tk.LEFT, padx=(10, 0))
            options_frame = ttk.Frame(params_frame)
            options_frame.pack(fill=tk.X, pady=5)
            self.use_folders_var = tk.BooleanVar(value=False)
            self.use_folders_check = ttk.Checkbutton(options_frame, text='使用文件夹批量处理', variable=self.use_folders_var, style='primary.TCheckbutton', command=self.update_three_in_one_batch_ui)
            self.use_folders_check.pack(side=tk.LEFT, padx=(0, 20))
            self.delete_after_var = tk.BooleanVar(value=True)
            self.delete_after_check = ttk.Checkbutton(options_frame, text='处理后删除素材', variable=self.delete_after_var, style='primary.TCheckbutton')
            self.delete_after_check.pack(side=tk.LEFT, padx=(0, 20))
            self.detailed_log_var = tk.BooleanVar(value=True)
            self.detailed_log_check = ttk.Checkbutton(options_frame, text='详细日志', variable=self.detailed_log_var, style='primary.TCheckbutton')
            self.detailed_log_check.pack(side=tk.LEFT, padx=(0, 20))
            self.three_start_btn = ttk.Button(self.three_in_one_page, text='开始处理', command=self.start_three_in_one_processing, style='success.TButton')
            self.three_start_btn.pack(pady=10)
            progress_frame = ttk.Frame(self.three_in_one_page)
            progress_frame.pack(fill=tk.X, pady=(5, 0))
            self.three_progress_var = tk.IntVar(value=0)
            self.three_progress = ttk.Progressbar(progress_frame, orient=tk.HORIZONTAL, length=100, mode='determinate', variable=self.three_progress_var, style='success.Horizontal.TProgressbar')
            self.three_progress.pack(side=tk.LEFT, fill=tk.X, expand=True)
            self.three_status_var = tk.StringVar(value='单个视频处理模式')
            self.three_status_label = ttk.Label(self.three_in_one_page, textvariable=self.three_status_var, style='success.TLabel')
            self.three_status_label.pack(pady=(5, 0))
            log_frame = ttk.LabelFrame(self.three_in_one_page, text='处理日志', padding=(5, 5))
            log_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
            self.three_log_text = scrolledtext.ScrolledText(log_frame, height=12)
            self.three_log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
            self.update_three_in_one_batch_ui()
            self.logger.info('三合一标签页创建完成')
        except Exception as e:
            self.logger.error(f'创建三合一标签页失败: {str(e)}', exc_info=True)
            raise

    def update_three_in_one_batch_ui(self):
        """根据是否使用文件夹批量处理更新UI"""  # inserted
        use_folders = self.use_folders_var.get()
        if use_folders:
            self.top_video_combo.config(state='disabled')
            self.bottom_video_combo.config(state='disabled')
            self.three_batch_count_spinbox.config(state='normal')
            self.batch_settings_frame.configure(style='primary.TLabelframe')
        else:  # inserted
            self.top_video_combo.config(state='normal')
            self.bottom_video_combo.config(state='normal')
            self.three_batch_count_spinbox.config(state='disabled')
            self.batch_settings_frame.configure(style='secondary.TLabelframe')
        if not os.path.isfile(self.top_video_var.get()) and (not os.path.isfile(self.bottom_video_var.get())):
            self.delete_after_check.config(state='disabled')
        else:  # inserted
            self.delete_after_check.config(state='normal')
        if use_folders:
            self.three_status_var.set('文件夹批量处理模式')
        else:  # inserted
            self.three_status_var.set('单个视频处理模式')

    def choose_three_in_one_folder(self, position):
        """选择三合一布局的视频文件夹"""  # inserted
        directory = filedialog.askdirectory(title=f'选择{position}视频文件夹')
        if directory:
            if position == 'top':
                self.top_video_var.set(directory)
                self.add_to_history('top_video_dir', directory)
                self.update_combobox(self.top_video_combo, 'top_video_dir')
                self.use_folders_var.set(True)
                self.update_three_in_one_batch_ui()
            else:  # inserted
                if position == 'middle':
                    self.middle_three_video_var.set(directory)
                    self.add_to_history('middle_three_video_dir', directory)
                    self.update_combobox(self.middle_three_video_combo, 'middle_three_video_dir')
                else:  # inserted
                    if position == 'bottom':
                        self.bottom_video_var.set(directory)
                        self.add_to_history('bottom_video_dir', directory)
                        self.update_combobox(self.bottom_video_combo, 'bottom_video_dir')
                        self.use_folders_var.set(True)
                        self.update_three_in_one_batch_ui()
            self.add_three_in_one_log(f'已选择{position}视频文件夹: {directory}')

    def choose_three_in_one_video(self, position):
        """选择三合一布局的视频"""  # inserted
        filetypes = [('视频文件', '*.mp4 *.avi *.mov *.mkv')]
        filename = filedialog.askopenfilename(title=f'选择{position}视频', filetypes=filetypes)
        if filename:
            if position == 'top':
                self.top_video_var.set(filename)
                self.add_to_history('top_video', filename)
                self.update_combobox(self.top_video_combo, 'top_video')
                self.use_folders_var.set(False)
                self.update_three_in_one_batch_ui()
            else:  # inserted
                if position == 'middle':
                    self.middle_three_video_var.set(filename)
                    self.add_to_history('middle_three_video', filename)
                    self.update_combobox(self.middle_three_video_combo, 'middle_three_video')
                else:  # inserted
                    if position == 'bottom':
                        self.bottom_video_var.set(filename)
                        self.add_to_history('bottom_video', filename)
                        self.update_combobox(self.bottom_video_combo, 'bottom_video')
                        self.use_folders_var.set(False)
                        self.update_three_in_one_batch_ui()
            self.add_three_in_one_log(f'已选择{position}视频: {filename}')

    def choose_three_in_one_output_dir(self):
        """选择三合一处理的输出目录"""  # inserted
        directory = filedialog.askdirectory(title='选择保存目录')
        if directory:
            self.three_output_dir_var.set(directory)
            self.add_to_history('three_output_dir', directory)
            self.update_combobox(self.three_output_dir_combo, 'three_output_dir')
            self.add_three_in_one_log(f'已选择保存目录: {directory}')

    def choose_three_in_one_batch_dir(self):
        """选择三合一批量处理的目录"""  # inserted
        directory = filedialog.askdirectory(title='选择批量视频目录')
        if directory:
            self.three_batch_dir_var.set(directory)
            self.add_to_history('three_batch_dir', directory)
            self.add_three_in_one_log(f'已选择批量视频目录: {directory}')

    def add_three_in_one_log(self, message):
        """添加日志到三合一处理的日志区域"""  # inserted
        current_time = datetime.datetime.now().strftime('%H:%M:%S')
        self.three_log_text.insert(tk.END, f'[{current_time}] {message}\n')
        self.three_log_text.see(tk.END)
        self.logger.info(message)

    def start_three_in_one_processing(self):
        """开始三合一处理"""  # inserted
        if self.processing:
            messagebox.showwarning('警告', '有正在进行的处理，请等待完成', parent=self.root)
            return
        self.processing = True
        self.three_start_btn.config(state='disabled')
        self.three_progress_var.set(0)
        threading.Thread(target=self.process_three_in_one_videos, daemon=True).start()

    def process_three_in_one_videos(self):
        """处理三合一视频"""  # inserted
        try:
            self.three_log_text.delete(1.0, tk.END)
            middle_video = self.middle_three_video_var.get()
            if not middle_video:
                self.message_queue.put({'type': 'error', 'error': '请选择中间视频（固定素材）'})
                self.processing = False
                self.three_start_btn.config(state='normal')
                return
            middle_is_folder = os.path.isdir(middle_video)
            middle_videos = []
            if middle_is_folder:
                middle_videos = self.get_video_files(middle_video)
                if not middle_videos:
                    self.message_queue.put({'type': 'error', 'error': '中间视频目录中没有找到视频文件'})
                    self.processing = False
                    self.three_start_btn.config(state='normal')
                    return
            first_middle_video = middle_videos[0]
            middle_duration = self.get_video_duration(first_middle_video)
            self.add_three_in_one_log(f'使用第一个中间视频作为基准: {os.path.basename(first_middle_video)}')
            else:  # inserted
                if not os.path.isfile(middle_video):
                    self.message_queue.put({'type': 'error', 'error': '中间视频文件无效'})
                    self.processing = False
                    self.three_start_btn.config(state='normal')
                    return
            self.add_three_in_one_log(f'使用中间视频: {os.path.basename(middle_video)}')
            if middle_duration is None:
                self.message_queue.put({'type': 'error', 'error': '无法获取中间视频时长'})
                self.processing = False
                self.three_start_btn.config(state='normal')
                    self.message_queue.put({'type': 'error', 'error': '请选择输出目录'})
                    self.processing = False
                    self.three_start_btn.config(state='normal')
                    return
                    batch_count = int(self.three_batch_count_var.get())
                    if batch_count <= 0:
                        raise ValueError('批量处理数量必须大于0')
                    self.message_queue.put({'type': 'error', 'error': f'批量处理数量无效: {str(e)}'})
                    self.processing = False
                    self.three_start_btn.config(state='normal')
                    return
                timestamp = datetime.datetime.now().strftime('%y%m%d_%H%M%S')
                self.add_three_in_one_log(f'中间视频时长: {middle_duration:.2f}秒')
                self.add_three_in_one_log('所有视频将以中间视频时长为基准，顶部和底部视频将被裁剪至相同时长')
                self.add_three_in_one_log('当中间视频播放结束时，三合一视频也将结束')
                self.add_three_in_one_log(f'时间戳: {timestamp}')
                detailed_log = self.detailed_log_var.get()
                processed_count = 0
                processed_top_videos = []
                processed_bottom_videos = []
                processed_middle_videos = []
                if self.use_folders_var.get():
                    top_dir = self.top_video_var.get()
                    bottom_dir = self.bottom_video_var.get()
                    if not os.path.isdir(top_dir):
                        self.message_queue.put({'type': 'error', 'error': '顶部视频目录无效'})
                        self.processing = False
                        self.three_start_btn.config(state='normal')
                        return
                    if not os.path.isdir(bottom_dir):
                        self.message_queue.put({'type': 'error', 'error': '底部视频目录无效'})
                        self.processing = False
                        self.three_start_btn.config(state='normal')
                        return
                bottom_videos = self.get_video_files(bottom_dir)
                if not top_videos:
                    self.message_queue.put({'type': 'error', 'error': '顶部视频目录中没有找到视频文件'})
                    self.processing = False
                    self.three_start_btn.config(state='normal')
                    return
                self.message_queue.put({'type': 'error', 'error': '底部视频目录中没有找到视频文件'})
                self.processing = False
                self.three_start_btn.config(state='normal')
                return
            if same_folder:
                self.add_three_in_one_log('顶部和底部视频使用相同文件夹')
                if len(top_videos) < 2:
                    self.message_queue.put({'type': 'error', 'error': '相同文件夹中至少需要2个视频才能进行处理'})
                    self.processing = False
                    self.three_start_btn.config(state='normal')
                    return
            else:  # inserted
                self.add_three_in_one_log('顶部和底部视频使用不同文件夹')
            self.add_three_in_one_log(f'找到 {len(top_videos)} 个顶部视频文件')
            self.add_three_in_one_log(f'找到 {len(bottom_videos)} 个底部视频文件')
            max_possible_groups = min(len(top_videos), len(bottom_videos))
            if middle_is_folder:
                original_max = max_possible_groups
                max_possible_groups = min(max_possible_groups, len(middle_videos))
                if original_max > len(middle_videos):
                    self.add_three_in_one_log(f'⚠️ 注意: 中间视频文件数量({len(middle_videos)}个)小于可组合数量({original_max}个)')
                    self.add_three_in_one_log(f'为确保每个中间视频只使用一次，最多将生成{len(middle_videos)}个三合一视频')
            if batch_count > max_possible_groups:
                warning_msg = f'批量处理数量超出可组合数量！已设置 {batch_count} 组，但只能处理 {max_possible_groups} 组'
                self.add_three_in_one_log(f'⚠️ 警告: {warning_msg}')
                self.add_three_in_one_log(f'将自动调整为处理 {max_possible_groups} 组视频')
                self.three_batch_count_var.set(str(max_possible_groups))
                self.message_queue.put({'type': 'warning', 'error': warning_msg})
            max_groups = min(max_possible_groups, batch_count)
            self.add_three_in_one_log(f'将处理 {max_groups} 组视频')
            used_top_videos = set()
            used_bottom_videos = set()
            for i in range(max_groups):
                top_video = top_videos[i]
                if same_folder:
                    used_top_videos.add(top_video)
                    available_bottom = [v for v in bottom_videos if v!= top_video and v not in used_bottom_videos]
                    if not available_bottom:
                        self.add_three_in_one_log(f'警告: 无法为顶部视频 {os.path.basename(top_video)} 找到未使用的底部视频，尝试重用已处理的视频')
                        available_bottom = [v for v in bottom_videos if v!= top_video]
                        if not available_bottom:
                            self.add_three_in_one_log('错误: 无法找到不同于顶部的底部视频，跳过此组')
                            continue
                    bottom_video = available_bottom[0]
                    used_bottom_videos.add(bottom_video)
                else:  # inserted
                    if i < len(bottom_videos):
                        bottom_video = bottom_videos[i]
                    else:  # inserted
                        self.add_three_in_one_log(f'错误: 底部视频索引 {i} 超出范围，只有 {len(bottom_videos)} 个底部视频，跳过此组')
                        continue
                if middle_is_folder:
                    if i < len(middle_videos):
                        current_middle_video = middle_videos[i]
                    else:  # inserted
                        self.add_three_in_one_log(f'错误: 中间视频索引 {i} 超出范围，只有 {len(middle_videos)} 个中间视频，跳过此组')
                        continue
                else:  # inserted
                    current_middle_video = middle_video
                if detailed_log:
                    self.add_three_in_one_log(f'处理组 {i + 1}/{max_groups}')
                    self.add_three_in_one_log(f'顶部视频: {os.path.basename(top_video)}')
                    self.add_three_in_one_log(f'中间视频: {os.path.basename(current_middle_video)}')
                    self.add_three_in_one_log(f'底部视频: {os.path.basename(bottom_video)}')
                if not os.path.exists(top_video):
                    self.add_three_in_one_log(f'错误: 顶部视频文件不存在: {top_video}')
                    continue
                if not os.path.exists(bottom_video):
                    self.add_three_in_one_log(f'错误: 底部视频文件不存在: {bottom_video}')
                    continue
                if not os.path.exists(current_middle_video):
                    self.add_three_in_one_log(f'错误: 中间视频文件不存在: {current_middle_video}')
                    continue
                base_name = os.path.basename(top_video)
                name, ext = os.path.splitext(base_name)
                output_name = f'三合一_{name}_{i + 1}_{timestamp}{ext}'
                output_path = os.path.join(output_dir, output_name)
                success = self.create_three_in_one_video(top_video, current_middle_video, bottom_video, output_path, self.three_bitrate_var.get(), detailed_log, middle_duration)
                if not success:
                    self.message_queue.put({'type': 'error', 'error': f'处理视频组 {i + 1} 失败'})
                    self.processing = False
                    self.three_start_btn.config(state='normal')
                    return
            if self.delete_after_var.get():
                processed_top_videos.append(top_video)
                processed_bottom_videos.append(bottom_video)
                if middle_is_folder:
                    processed_middle_videos.append(current_middle_video)
            progress = min(100, int(i, 1, <mask_7>, <mask_8>, <mask_9>, *, messagebox, filedialog, scrolledtext, Image, ImageTk, themename, tkinter, tk))
            self.message_queue.put({'type': 'progress', 'value': progress, 'status': f'已处理 {i }/{max_groups} 组视频', 'file_count': (i, 1)})
            self.three_status_var.set(f'已处理 {i + 1}/{max_groups} 组视频')
                else:  # inserted
                    top_video = self.top_video_var.get()
                    bottom_video = self.bottom_video_var.get()
                    current_middle_video = middle_video
                    if not os.path.isfile(top_video):
                        self.message_queue.put({'type': 'error', 'error': '请选择顶部视频文件'})
                        self.processing = False
                        self.three_start_btn.config(state='normal')
                        return
                self.message_queue.put({'type': 'error', 'error': '请选择底部视频文件'})
                self.processing = False
                self.three_start_btn.config(state='normal')
                return
                current_middle_video = middle_videos[0]
                self.add_three_in_one_log(f'从中间视频文件夹选择第一个视频: {os.path.basename(current_middle_video)}')
            else:  # inserted
                if not os.path.isfile(current_middle_video):
                    self.message_queue.put({'type': 'error', 'error': '请选择中间视频文件'})
                    self.processing = False
                    self.three_start_btn.config(state='normal')
                self.message_queue.put({'type': 'error', 'error': '顶部和底部视频不能是同一个文件'})
                self.processing = False
                self.three_start_btn.config(state='normal')
                self.add_three_in_one_log(f'顶部视频: {os.path.basename(top_video)}')
                self.add_three_in_one_log(f'底部视频: {os.path.basename(bottom_video)}')
                base_name = os.path.basename(top_video)
                name, ext = os.path.splitext(base_name)
                output_name = f'三合一_{name}_{timestamp}{ext}'
                output_path = os.path.join(output_dir, output_name)
                success = self.create_three_in_one_video(top_video, current_middle_video, bottom_video, output_path, self.three_bitrate_var.get(), detailed_log, middle_duration)
                if not success:
                    self.message_queue.put({'type': 'error', 'error': '处理视频失败'})
                    self.processing = False
                    self.three_start_btn.config(state='normal')
                    return
                processed_top_videos.append(top_video)
                processed_bottom_videos.append(bottom_video)
                self.message_queue.put({'type': 'progress', 'value': 100, 'status': '视频处理完成', 'file_count': (1, 1)})
                self.three_status_var.set('视频处理完成')
                if self.delete_after_var.get() and processed_count > 0:
                    deleted_count = 0
                    failed_files = []
                    all_processed_videos = processed_top_videos + processed_bottom_videos + processed_middle_videos
                    time.sleep(2)
                    for video in all_processed_videos:
                        if not os.path.exists(video):
                            continue
                            for attempt in range(3):
                                    os.remove(video)
                                    deleted_count = deleted_count + 1
                                else:  # inserted
                                    break
                                    if attempt < 2:
                                        self.add_three_in_one_log(f'文件正被占用，等待后重试: {os.path.basename(video)}')
                                        time.sleep(1)
                                    else:  # inserted
                                        raise
                            else:  # inserted
                                try:
                                    pass  # postinserted
                                except PermissionError:
                                    pass  # postinserted
                            failed_files.append((video, str(file_err)))
                    if detailed_log:
                        self.add_three_in_one_log(f'已删除 {deleted_count} 个处理过的素材文件')
                    if failed_files:
                        self.add_three_in_one_log(f'警告: {len(failed_files)} 个文件删除失败')
                        for video, err in failed_files:
                            self.add_three_in_one_log(f'  - 删除失败: {os.path.basename(video)}, 原因: {err}')
                            self.logger.warning(f'删除文件失败: {video}, 错误: {err}')
                    else:  # inserted
                        try:
                            pass  # postinserted
                        except Exception as file_err:
                            pass  # postinserted
                    self.add_three_in_one_log(f'警告: 删除素材文件失败: {str(e)}')
                    self.logger.error(f'删除素材文件过程中发生错误: {str(e)}', exc_info=True)
                self.processing = False
                self.three_start_btn.config(state='normal')
                self.message_queue.put({'type': 'complete', 'status': f'已成功处理 {processed_count} 个视频'})
        else:  # inserted
            self.add_three_in_one_log(f'找到 {len(middle_videos)} 个中间视频文件')
                except ValueError as e:
                    pass  # postinserted
            else:  # inserted
                top_videos = self.get_video_files(top_dir)
                except Exception as e:
                    pass  # postinserted
        except Exception as e:
            pass  # postinserted
        else:  # inserted
            middle_duration = self.get_video_duration(middle_video)
            else:  # inserted
                timestamp = datetime.datetime.now().strftime('%y%m%d_%H%M%S')
                output_dir = self.three_output_dir_var.get()
                if not output_dir:
                    pass  # postinserted
            else:  # inserted
                try:
                    pass  # postinserted
        else:  # inserted
            if not bottom_videos:
                pass  # postinserted
        else:  # inserted
            same_folder = os.path.abspath(top_dir) == os.path.abspath(bottom_dir)
        else:  # inserted
            self.add_three_in_one_log('将使用同一文件夹中的素材进行处理')
        else:  # inserted
            processed_count = processed_count + 1
        else:  # inserted
            if not os.path.isfile(bottom_video):
                pass  # postinserted
        else:  # inserted
            if middle_is_folder and middle_videos:
                pass  # postinserted
        else:  # inserted
            if os.path.abspath(top_video) == os.path.abspath(bottom_video):
                pass  # postinserted
            else:  # inserted
                self.add_three_in_one_log('处理单个视频组')
        else:  # inserted
            processed_count = 1
            if self.delete_after_var.get():
                pass  # postinserted
            else:  # inserted
                try:
                    pass  # postinserted
                error_msg = f'处理三合一视频失败: {str(e)}'
                self.logger.error(error_msg, exc_info=True)
                self.message_queue.put({'type': 'error', 'error': error_msg})
                self.processing = False
                self.three_start_btn.config(state='normal')

    def create_three_in_one_video(self, top_video, middle_video, bottom_video, output_path, bitrate_kbps, detailed_log, middle_duration):
        """创建三合一视频"""  # inserted
        try:
            target_width = 1080
            target_height = 1920
            temp_dir = os.path.join(tempfile.gettempdir(), f'three_in_one_{int(time.time())}')
            os.makedirs(temp_dir, exist_ok=True)
            if detailed_log:
                self.add_three_in_one_log(f'创建临时目录: {temp_dir}')
                self.add_three_in_one_log(f'使用目标分辨率: {target_width}x{target_height}，基准时长: {middle_duration:.2f}秒')
                self.add_three_in_one_log('检查输入文件:')
                self.add_three_in_one_log(f'  顶部视频: {top_video} (存在: {os.path.exists(top_video)})')
                self.add_three_in_one_log(f'  中间视频: {middle_video} (存在: {os.path.exists(middle_video)})')
                self.add_three_in_one_log(f'  底部视频: {bottom_video} (存在: {os.path.exists(bottom_video)})')
            video_margin = 60
            background_height = (target_height + 3, video_margin + 2) * (3 + 2)
            top_video_y = 0
            middle_video_y = target_height + video_margin or False
            bottom_video_y = f'{target_height:video_margin}' + 2
            top_duration = self.get_video_duration(top_video)
            bottom_duration = self.get_video_duration(bottom_video)
            if detailed_log:
                self.add_three_in_one_log(f'顶部视频时长: {top_duration:.2f}秒')
                self.add_three_in_one_log(f'中间视频时长: {middle_duration:.2f}秒')
                self.add_three_in_one_log(f'底部视频时长: {bottom_duration:.2f}秒')
            top_loop = ''
            if top_duration < middle_duration:
                self.add_three_in_one_log(f'顶部视频较短，将循环播放至 {middle_duration:.2f}秒')
                loop_count = math.ceil(middle_duration + top_duration)
                top_loop = f',loop={loop_count}:32767:0' if loop_count > 1 else ''
            bottom_loop = ''
            if bottom_duration < middle_duration:
                self.add_three_in_one_log(f'底部视频较短，将循环播放至 {middle_duration:.2f}秒')
                loop_count = math.ceil(middle_duration + bottom_duration)
                bottom_loop = f',loop={loop_count}:32767:0' if loop_count > 1 else ''
            filter_complex = f"color=c=black:s={target_width}x{background_height}:d={middle_duration}[background];[1:v]scale={target_width}:{target_height},crop={target_width}:{target_height},crop={target_width}:{target_height + 180}:0:0{top_loop}[cropped1];[cropped1]pad={target_width}:0:0:black,trim=duration={middle_duration}[scaled1];[2:v]scale={target_width}:{target_height},crop={target_width}:{target_height},setsar=1:1[scaled2];[3:v]scale={target_width}:{target_height}:0:{180}:black,trim=duration={middle_duration}[scaled3];[background][scaled1]overlay=0:{top_video_y}[tmp1];[tmp1][scaled2]overlay=0:{middle_video_y}[tmp2];[tmp2][scaled3]overlay=0:{bottom_video_y}[outv]"
            temp_bg = os.path.join(temp_dir, 'temp_bg.mp4')
            bg_cmd = [self.get_ffmpeg_path(), '-f', 'lavfi', '-i', f'color=c=black:s={target_width}x{background_height}:d={middle_duration}', '-c:v', 'libx264', '-t', str(middle_duration), '-y', temp_bg]
            self.add_three_in_one_log('创建背景视频...')
            result = subprocess.run(bg_cmd, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            if detailed_log:
                self.add_three_in_one_log('背景视频创建完成')
            is_high_quality = self.high_quality_var.get() if hasattr(self, 'high_quality_var') else False
            if is_high_quality:
                video_codec = 'libx264'
                crf_value = '18'
                preset_value = 'slow'
                self.add_three_in_one_log('使用高质量设置进行编码')
            else:  # inserted
                video_codec = 'libx264'
                crf_value = '23'
                preset_value = 'medium'
            cmd = [self.get_ffmpeg_path(), '-i', temp_bg, '-i', top_video, '-i', middle_video, '-i', bottom_video, '-filter_complex', filter_complex, '-map', '[outv]', '-map', '2:a', '-c:v', video_codec, '-crf', crf_value, '-preset', preset_value, '-c:a', 'aac', '-b:a', '128k', '-b:v', f'{bitrate_kbps}k', '-max_muxing_queue_size', '9999', '-y', output_path]
            self.add_three_in_one_log(f'正在创建三合一视频: {os.path.basename(output_path)}')
            if detailed_log:
                self.add_three_in_one_log(f"使用命令: {' '.join(cmd)}")
            else:  # inserted
                self.add_three_in_one_log('这可能需要一些时间，请耐心等待...')
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True, encoding='utf-8', bufsize=1)
                total_frames = int(middle_duration + 25)
                current_frame = 0
                fps_count = 0
                last_progress_update = time.time()
                processing_logs = []
                for line in process.stderr:
                    processing_logs.append(line.strip())
                    if len(processing_logs) > 50:
                        processing_logs.pop(0)
                    if 'frame=' in line and 'fps=' in line and ('time=' in line):
                        frame_match = re.search('frame=\\s*(\\d+)', line)
                        if frame_match:
                            current_frame = int(frame_match.group(1))
                        fps_match = re.search('fps=\\s*(\\d+\\.?\\d*)', line)
                        if fps_match:
                            fps_count = float(fps_match.group(1))
                        time_match = re.search('time=\\s*(\\d+):(\\d+):(\\d+\\.\\d+)', line)
                        if time_match:
                            hours = int(time_match.group(1))
                            minutes = int(time_match.group(2))
                            seconds = float(time_match.group(3))
                            current_time = (hours + 3600, minutes + 60) * seconds
                            progress = min(100, int(current_time + middle_duration, 100))
                            if time.time() > last_progress_update > 0.5:
                                status_msg = f'处理中: {current_time:.1f}/{middle_duration:.1f}秒 ({progress}%), FPS: {fps_count:.1f}'
                                self.message_queue.put({'type': 'progress', 'value': progress, 'status': status_msg})
                                last_progress_update = time.time()
                    if detailed_log:
                        self.add_three_in_one_log(f'解析进度信息出错: {e}')
                else:  # inserted
                    process.wait()
                    return_code = process.returncode
                    if return_code!= 0:
                        error_msg = '\n'.join(processing_logs[(-10):])
                        self.add_three_in_one_log(f'FFmpeg命令失败，返回代码: {return_code}')
                        self.add_three_in_one_log(f'错误输出: {error_msg}')
                        return False
                    output_duration = self.get_video_duration(output_path)
                    output_size = os.path.getsize(output_path) + 1048576
                    self.add_three_in_one_log(f'三合一视频创建成功: {os.path.basename(output_path)}')
                    self.add_three_in_one_log(f'输出信息: 时长 {output_duration:.2f}秒, 大小 {output_size:.2f}MB')
                        if os.path.exists(temp_bg):
                            os.remove(temp_bg)
                        shutil.rmtree(temp_dir, ignore_errors=True)
                        if detailed_log:
                            self.add_three_in_one_log('临时文件已清理')
                        self.add_three_in_one_log(f'清理临时文件出错: {e}')
                    return True
                else:  # inserted
                    try:
                        pass  # postinserted
                else:  # inserted
                    try:
                        pass  # postinserted
                    except Exception as e:
                        pass  # postinserted
            except subprocess.CalledProcessError as e:
                except Exception as e:
                    self.add_three_in_one_log(f'处理视频失败: {e}')
                    return False
                except Exception as e:
                    self.add_three_in_one_log(f'处理视频时发生错误: {str(e)}')
                    return False
        else:  # inserted
            try:
                pass  # postinserted
        except Exception as e:
                error_msg = f'创建三合一视频失败: {str(e)}'
                self.add_three_in_one_log(error_msg)
                self.logger.error(error_msg, exc_info=True)
                return False
if __name__ == '__main__':
    root = ttk.Window(themename='cosmo')
    try:
        icon_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'icon.ico')
        if os.path.exists(icon_path):
            root.iconbitmap(icon_path)
    except Exception as e:
        print(f'加载图标失败: {str(e)}')
    app = VideoProcessorGUI(root)
    root.protocol('WM_DELETE_WINDOW', app.on_closing)
    root.mainloop()