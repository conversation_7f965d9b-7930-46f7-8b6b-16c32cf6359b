import os
import subprocess
import json
import tkinter as tk
from tkinter import ttk, filedialog
from threading import Thread

class VideoProcessorApp:
    def __init__(self, root):
        self.root = root
        self.root.title("两仪视频处理器")
        self.root.geometry("600x400")
        
        self.create_widgets()
        
    def create_widgets(self):
        # 主视频目录
        ttk.Label(self.root, text="主视频目录:").pack(pady=5)
        self.main_dir_entry = ttk.Entry(self.root, width=50)
        self.main_dir_entry.pack(pady=5)
        ttk.Button(self.root, text="选择", command=lambda: self.select_dir(self.main_dir_entry)).pack(pady=5)
        
        # 辅助视频目录
        ttk.Label(self.root, text="辅助视频目录:").pack(pady=5)
        self.aux_dir_entry = ttk.Entry(self.root, width=50)
        self.aux_dir_entry.pack(pady=5)
        ttk.Button(self.root, text="选择", command=lambda: self.select_dir(self.aux_dir_entry)).pack(pady=5)
        
        # 输出目录
        ttk.Label(self.root, text="输出目录:").pack(pady=5)
        self.output_dir_entry = ttk.Entry(self.root, width=50)
        self.output_dir_entry.pack(pady=5)
        ttk.Button(self.root, text="选择", command=lambda: self.select_dir(self.output_dir_entry)).pack(pady=5)
        
        # 处理按钮
        self.process_btn = ttk.Button(self.root, text="开始处理", command=self.start_processing)
        self.process_btn.pack(pady=10)
        
        # 进度显示
        self.progress_text = tk.Text(self.root, height=10, width=70)
        self.progress_text.pack(pady=5)
        
    def select_dir(self, entry_widget):
        dir_path = filedialog.askdirectory()
        if dir_path:
            entry_widget.delete(0, tk.END)
            entry_widget.insert(0, dir_path)
    
    def start_processing(self):
        main_dir = self.main_dir_entry.get()
        aux_dir = self.aux_dir_entry.get()
        output_dir = self.output_dir_entry.get()
        
        if not all([main_dir, aux_dir, output_dir]):
            self.append_progress("请选择所有目录!")
            return
            
        self.process_btn.config(state=tk.DISABLED)
        self.progress_text.delete(1.0, tk.END)
        
        # 启动处理线程
        processor = VideoProcessor(main_dir, aux_dir, output_dir, self.append_progress)
        processor.start()
        
        # 检查线程状态的定时器
        self.check_thread(processor)
    
    def check_thread(self, thread):
        if thread.is_alive():
            self.root.after(100, lambda: self.check_thread(thread))
        else:
            self.process_btn.config(state=tk.NORMAL)
    
    def append_progress(self, message):
        self.progress_text.insert(tk.END, message + "\n")
        self.progress_text.see(tk.END)

class VideoProcessor(Thread):
    def __init__(self, main_dir, aux_dir, output_dir, progress_callback):
        super().__init__()
        self.main_dir = main_dir
        self.aux_dir = aux_dir
        self.output_dir = output_dir
        self.progress_callback = progress_callback
    
    def run(self):
        # 改进的文件收集逻辑
        def collect_videos(directory):
            videos = []
            for f in os.listdir(directory):
                full_path = os.path.join(directory, f)
                if os.path.isfile(full_path) and f.lower().endswith('.mp4') and not f.startswith('.'):
                    videos.append(full_path)
            
            # 按文件名自然排序（不依赖natsort）
            return sorted(videos, key=lambda x: os.path.basename(x).lower())
        
        main_videos = collect_videos(self.main_dir)
        aux_videos = collect_videos(self.aux_dir)
        
        self.progress_callback(f"找到主视频: {len(main_videos)}个")
        self.progress_callback(f"找到辅助视频: {len(aux_videos)}个")

        if len(main_videos) != len(aux_videos):
            self.progress_callback("警告: 主视频和辅助视频数量不匹配")

        success_count = 0
        for main_video, aux_video in zip(main_videos, aux_videos):
            if self.process_video_pair(main_video, aux_video):
                success_count += 1

        self.progress_callback(f"\n处理完成! 成功处理 {success_count}/{len(main_videos)} 对视频")
    
    def process_video_pair(self, main_video, aux_video):
        try:
            temp_dir = os.path.join(self.output_dir, "temp")
            os.makedirs(temp_dir, exist_ok=True)
            
            temp_a = os.path.join(temp_dir, "temp_A.MP4")
            temp_b = os.path.join(temp_dir, "temp_B.MP4")
            temp_c = os.path.join(temp_dir, "temp_C.MP4")
            output_name = os.path.basename(main_video).split('.')[0] + "_processed.mp4"
            output_path = os.path.join(self.output_dir, output_name)

            self.progress_callback(f"\n处理视频对: {os.path.basename(main_video)} 和 {os.path.basename(aux_video)}")

            # 获取主视频信息
            probe_cmd = f'ffprobe -v error -show_entries format=duration -show_entries stream=width,height -of json "{main_video}"'
            probe_result = subprocess.run(probe_cmd, shell=True, capture_output=True, 
                                        text=True, encoding='utf-8', errors='replace',
                                        creationflags=subprocess.CREATE_NO_WINDOW)
            video_info = json.loads(probe_result.stdout)
            
            duration = float(video_info['format']['duration'])
            width = int(video_info['streams'][0]['width'])
            height = int(video_info['streams'][0]['height'])

            self.progress_callback(f"主视频信息: 时长={duration:.2f}s, 分辨率={width}x{height}")

            # 检查ffmpeg是否可用
            try:
                subprocess.run(['ffmpeg', '-version'], check=True, capture_output=True, creationflags=subprocess.CREATE_NO_WINDOW)
            except Exception as e:
                self.progress_callback(f"错误: ffmpeg不可用 - {str(e)}")
                return False

            # 检查输入文件是否存在
            if not os.path.exists(aux_video):
                self.progress_callback(f"错误: 辅助视频文件不存在 - {aux_video}")
                return False

            # 确保临时目录存在
            os.makedirs(os.path.dirname(temp_b), exist_ok=True)

            # 处理辅助视频
            self.progress_callback("步骤1/3: 处理辅助视频...")
            try:
                cmd1 = [
                    'ffmpeg', '-y',
                    '-stream_loop', '-1',
                    '-i', aux_video,
                    '-t', f'{duration:.6f}',
                    '-vf', f'scale={width}:{height}:force_original_aspect_ratio=disable,setsar=1:1,fps=30',
                    '-r', '30',
                    '-map_metadata', '-1',
                    '-c:v', 'libx264',
                    '-b:v', '5000k',
                    '-maxrate', '5000k',
                    '-bufsize', '5000k',
                    '-x264opts', 'nal-hrd=cbr',
                    '-c:a', 'copy',
                    temp_b
                ]
                result = subprocess.run(cmd1, check=True, capture_output=True, 
                                     text=True, encoding='utf-8', errors='replace',
                                     creationflags=subprocess.CREATE_NO_WINDOW)
                if result.stderr:
                    self.progress_callback(f"警告: {result.stderr}")
            except subprocess.CalledProcessError as e:
                self.progress_callback(f"处理辅助视频失败: {e.stderr}")
                return False

            # 处理主视频
            self.progress_callback("步骤2/3: 处理主视频...")
            try:
                cmd2 = [
                    'ffmpeg', '-y',
                    '-i', main_video,
                    '-vf', 'setsar=1:1,fps=30',
                    '-r', '30',
                    '-map_metadata', '-1',
                    '-f', 'mp4',
                    '-c:v', 'libx264',
                    '-b:v', '5000k',
                    '-maxrate', '5000k',
                    '-bufsize', '10000k',
                    '-x264opts', 'nal-hrd=cbr',
                    '-strict', 'experimental',
                    temp_a
                ]
                result = subprocess.run(cmd2, check=True, capture_output=True, 
                                     text=True, encoding='utf-8', errors='replace',
                                     creationflags=subprocess.CREATE_NO_WINDOW)
                if result.stderr:
                    self.progress_callback(f"警告: {result.stderr}")
            except subprocess.CalledProcessError as e:
                self.progress_callback(f"处理主视频失败: {e.stderr}")
                return False

            # 合并视频
            self.progress_callback("步骤3/3: 合并视频...")
            try:
                cmd3 = [
                    'ffmpeg', '-y',
                    '-i', temp_a,
                    '-i', temp_b,
                    '-fflags', '+genpts',
                    '-avoid_negative_ts', 'make_zero',
                    '-filter_complex', '[0:v][1:v]interleave,setsar=1:1',
                    '-vsync', 'vfr',
                    '-map', '0:a',
                    '-c:a', 'copy',
                    '-map_metadata', '-1',
                    '-c:v', 'libx264',
                    '-x264-params', 'stitchable=1',
                    temp_c
                ]
                result = subprocess.run(cmd3, check=True, capture_output=True, 
                                     text=True, encoding='utf-8', errors='replace',
                                     creationflags=subprocess.CREATE_NO_WINDOW)
                if result.stderr:
                    self.progress_callback(f"警告: {result.stderr}")
            except subprocess.CalledProcessError as e:
                self.progress_callback(f"合并视频失败: {e.stderr}")
                return False

            # 第四步处理
            self.progress_callback("步骤4/4: 生成最终输出...")
            try:
                cmd4 = [
                    'ffmpeg', '-y',
                    '-i', temp_b,
                    '-i', temp_b,
                    '-i', temp_c,  # 使用第三步的临时输出作为输入
                    '-i', temp_b,
                    '-map', '0:v:0',
                    '-map', '1:v:0',
                    '-map', '2:v:0',
                    '-map', '3:v:0',
                    '-map', '2:a:0',
                    '-c', 'copy',
                    '-movflags', 'faststart',
                    '-disposition:v:0', '0',
                    '-disposition:v:1', '0',
                    '-disposition:v:2', 'default',
                    '-disposition:v:3', '0',
                    '-disposition:a:0', 'default',
                    output_path
                ]
                result = subprocess.run(cmd4, check=True, capture_output=True,
                                     text=True, encoding='utf-8', errors='replace',
                                     creationflags=subprocess.CREATE_NO_WINDOW)
                if result.stderr:
                    self.progress_callback(f"警告: {result.stderr}")
            except subprocess.CalledProcessError as e:
                self.progress_callback(f"生成最终输出失败: {e.stderr}")
                return False

            # 清理临时文件
            try:
                if os.path.exists(temp_a):
                    os.remove(temp_a)
                if os.path.exists(temp_b):
                    os.remove(temp_b)
                if os.path.exists(temp_c):
                    os.remove(temp_c)
            except Exception as e:
                self.progress_callback(f"警告: 清理临时文件失败 - {str(e)}")
            return True

        except Exception as e:
            self.progress_callback(f"处理失败: {str(e)}")
            return False

if __name__ == "__main__":
    root = tk.Tk()
    app = VideoProcessorApp(root)
    root.mainloop()
