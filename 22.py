# Decompiled with PyLingual (https://pylingual.io)
# Internal filename: main.py
# Bytecode version: 3.12.0rc2 (3531)
# Source timestamp: 1970-01-01 00:00:00 UTC (0)

import os
import re
import random
import tempfile
import shutil
from datetime import datetime
import sys
import time
import json
import subprocess
from pathlib import Path
from ffm_wsc import modify_video_duration
os.environ['QT_QPA_PLATFORM_PLUGIN_PATH'] = os.path.join(os.path.dirname(os.__file__), 'site-packages', 'PyQt6', 'Qt6', 'plugins', 'platforms')
from PyQt6.QtWidgets import QApplication, QMainWindow, QWidget, QLabel, QLineEdit, QPushButton, QRadioButton, QVBoxLayout, QHBoxLayout, QListWidget, QListWidgetItem, QMenu, QGroupBox, QMessageBox, QTextEdit, QFileDialog, QProgressBar, QTabWidget, QInputDialog, QDialog, QDialogButtonBox, QCheckBox
from PyQt6.QtCore import Qt, QSize, QTimer, QThread, pyqtSignal, QEvent
from PyQt6.QtGui import QIntValidator, QDoubleValidator, QDragEnterEvent, QDropEvent, QKeySequence
from PyQt6.QtWidgets import QStyledItemDelegate
from PyQt6.QtGui import QIcon
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad
import base64
import cv2

class CenterAlignDelegate(QStyledItemDelegate):
    def initStyleOption(self, option, index):
        super().initStyleOption(option, index)
        option.displayAlignment = Qt.AlignmentFlag.AlignCenter | Qt.AlignmentFlag.AlignVCenter

class VideoListWidget(QListWidget):
    def __init__(self):
        super().__init__()
        self.setAcceptDrops(True)
        self.setMinimumSize(400, 300)
        self.setFocusPolicy(Qt.FocusPolicy.NoFocus)
        self.setSelectionMode(QListWidget.SelectionMode.ExtendedSelection)
        self.setSelectionBehavior(QListWidget.SelectionBehavior.SelectItems)
        self.setMouseTracking(True)
        self.setUniformItemSizes(True)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.setItemDelegate(CenterAlignDelegate())
        self.main_window = None
        self.setStyleSheet('\n            QListWidget {\n                background-color: #2b2b2b;\n                border: 2px solid #555555;\n                border-radius: 6px;\n                padding: 2px;\n            }\n            QListWidget::item {\n                color: #ffffff;\n                padding: 3px;\n                margin: 1px;\n                border-radius: 3px;\n                background-color: #3b3b3b;\n                min-height: 20px;\n                border: none;\n                outline: none;\n            }\n            QListWidget::item:hover {\n                background-color: #4CAF50;  /* 直接使用选中颜色 */\n                color: white;\n            }\n            QListWidget::item:selected {\n                background-color: #4CAF50;\n                color: white;\n                border: none;\n                outline: none;\n            }\n            QListWidget::item:selected:hover {\n                background-color: #4CAF50;  /* 保持选中颜色 */\n                color: white;\n            }\n            QListWidget::item:selected:active {\n                background-color: #4CAF50;  /* 保持选中颜色 */\n                color: white;\n            }\n            QListWidget:focus {\n                outline: none;\n            }\n            QScrollBar:vertical {\n                border: none;\n                background: #2b2b2b;\n                width: 8px;\n                margin: 0px;\n            }\n            QScrollBar::handle:vertical {\n                background: #555555;\n                border-radius: 4px;\n                min-height: 20px;\n            }\n            QScrollBar::handle:vertical:hover {\n                background: #666666;\n            }\n            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {\n                height: 0px;\n            }\n            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {\n                background: none;\n            }\n        ')
        self.show_placeholder()
        self.selection_timer = QTimer()
        self.selection_timer.setSingleShot(True)
        self.selection_timer.setInterval(100)
        self.selection_timer.timeout.connect(self.process_selection)
        self.itemSelectionChanged.connect(self.on_selection_changed)
        self.last_processed_path = None

    def show_placeholder(self):
        """显示提示文本"""  # inserted
        self.placeholder = QListWidgetItem()
        list_size = self.size()
        self.placeholder.setSizeHint(QSize(list_size.width() - 20, list_size.height() - 20))
        self.placeholder.setText('拖放视频文件到这里\n支持 .mp4 和 .mov 格式')
        self.placeholder.setForeground(Qt.GlobalColor.gray)
        self.placeholder.setFlags(self.placeholder.flags() & ~Qt.ItemFlag.ItemIsEnabled)
        self.addItem(self.placeholder)

    def resizeEvent(self, event):
        """重写调整大小事件，确保提示文本始终填充可见区域"""  # inserted
        super().resizeEvent(event)
        if self.count() == 1 and self.item(0) == self.placeholder:
            list_size = event.size()
            self.placeholder.setSizeHint(QSize(list_size.width() - 20, list_size.height() - 20))

    def dragEnterEvent(self, event: QDragEnterEvent):
        """处理拖入事件"""  # inserted
        if event.mimeData().hasUrls():
            urls = event.mimeData().urls()
            has_valid_files = False
            for url in urls:
                file_path = url.toLocalFile()
                if file_path.lower().endswith(('.mp4', '.mov')):
                    has_valid_files = True
                    break
            if has_valid_files:
                event.accept()
                self.setStyleSheet(self.styleSheet().replace('border: 2px solid #555555', 'border: 2px dashed #4CAF50'))
            else:  # inserted
                event.ignore()
        else:  # inserted
            event.ignore()

    def dragMoveEvent(self, event):
        """处理拖动事件"""  # inserted
        if event.mimeData().hasUrls():
            event.accept()
        else:  # inserted
            event.ignore()

    def dragLeaveEvent(self, event):
        """处理拖出事件"""  # inserted
        self.setStyleSheet(self.styleSheet().replace('border: 2px dashed #4CAF50', 'border: 2px solid #555555'))
        event.accept()

    def dropEvent(self, event: QDropEvent):
        """处理放置事件"""  # inserted
        self.setStyleSheet(self.styleSheet().replace('border: 2px dashed #4CAF50', 'border: 2px solid #555555'))
        urls = event.mimeData().urls()
        if not urls:
            event.ignore()
            return
        if self.count() == 1 and self.item(0) == self.placeholder:
            self.takeItem(0)
        for url in urls:
            file_path = url.toLocalFile()
            if file_path.lower().endswith(('.mp4', '.mov')):
                self.add_video_item(file_path)
        event.accept()

    def add_video_item(self, file_path: str):
        """添加视频文件到列表"""  # inserted
        file_name = os.path.basename(file_path)
        existing_items = self.findItems(file_name, Qt.MatchFlag.MatchExactly)
        if not existing_items:
            item = QListWidgetItem(file_name)
            item.setToolTip(file_path)
            item.setData(Qt.ItemDataRole.UserRole, file_path)
            self.addItem(item)
            try:
                cap = cv2.VideoCapture(file_path)
                if cap.isOpened():
                    cap.get(cv2.CAP_PROP_FRAME_WIDTH)
                    cap.get(cv2.CAP_PROP_FRAME_HEIGHT)
                    cap.release()
            except:
                return

    def clear(self):
        """重写clear方法"""  # inserted
        super().clear()
        self.show_placeholder()

    def on_selection_changed(self):
        """列表选择变化时的处理"""  # inserted
        if not self.selectedItems():
            self.clear_inputs()
            if hasattr(self, 'main_window') and self.main_window:
                if self == self.main_window.video_list:
                    self.main_window.select_all_btn.setText('全选')
                else:  # inserted
                    if self == self.main_window.video_list2:
                        self.main_window.select_all_btn2.setText('全选')
        else:  # inserted
            all_selected = len(self.selectedItems()) == self.count()
            if hasattr(self, 'main_window') and self.main_window:
                if self == self.main_window.video_list:
                    self.main_window.select_all_btn.setText('取消全选' if all_selected else '全选')
                else:  # inserted
                    if self == self.main_window.video_list2:
                        self.main_window.select_all_btn2.setText('取消全选' if all_selected else '全选')
            self.selection_timer.start()

    def process_selection(self):
        """延迟处理选择变化"""  # inserted
        selected_items = self.selectedItems()
        if selected_items:
            item = selected_items[0]
            file_path = item.toolTip()
            if hasattr(self, 'main_window'):
                self.main_window.path_input.setText(file_path)
            try:
                cap = cv2.VideoCapture(file_path)
                if not cap.isOpened():
                    QMessageBox.warning(self, '提示', '无法打开视频文件')
                cap.release()
            except Exception as e:
                self.print_log(f'读取视频信息失败: {str(e)}')  # 添加日志记录语句以确保语法正确性
            else:  # inserted
                self.path_input.clear()
                self.stuck_duration_seconds_label.setText('秒')
                QMessageBox.warning(self, '提示', f'读取视频信息失败: {str(e)}')
                return None

    def clear_inputs(self):
        """Clear input values if main window reference exists"""  # inserted
        if hasattr(self, 'main_window') and self.main_window:
            self.main_window.width_input.clear()
            self.main_window.height_input.clear()
            self.main_window.path_input.clear()

class ClWorker(QThread):
    """CL处理工作线程"""
    progress_updated = pyqtSignal(int)
    log_message = pyqtSignal(str)
    item_finished = pyqtSignal(QListWidgetItem)

    def __init__(self, items, bitrate, audio_bitrate, saturation=1.2, sharpness=1.0, use_gpu=False, new_effect=False, use_amd=False, use_intel=False, speed_mode=False, stuck_duration=6):
        super().__init__()
        self.items = items
        self.bitrate = bitrate
        self.audio_bitrate = audio_bitrate
        self.saturation = saturation
        self.sharpness = sharpness
        self.use_gpu = use_gpu
        self.new_effect = new_effect
        self.use_amd = use_amd
        self.use_intel = use_intel
        self.speed_mode = speed_mode
        self.stuck_duration = stuck_duration
        self.is_running = True
        self.current_item = None
        self.current_file = None
        self.current_output = None

    def run(self):
        """线程运行函数"""  # inserted
        try:
            startupinfo = None
            if sys.platform.startswith('win'):
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE
            subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True, startupinfo=startupinfo)
        except FileNotFoundError:
            pass  # postinserted
        else:  # inserted
            for i, item in enumerate(self.items, 1):
                if not self.is_running:
                    break
                self.current_item = item
                try:
                    input_path = item.toolTip()
                    input_file = Path(input_path)
                    if not input_file.exists():
                        self.log_message.emit(f'❌ 错误: 找不到输入文件 {input_file}')
                        continue
                    self.current_file = input_file
                    cap = cv2.VideoCapture(str(input_file))
                    if not cap.isOpened():
                        self.log_message.emit(f'❌ 错误: 无法打开视频文件 {input_file.name}')
                        continue
                except Exception as e:
                    self.log_message.emit(f'❌ 处理失败: {str(e)}')
                    continue
                finally:
                    cap.release()  # 确保资源释放

                width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                height = int(cap2.get(cv2.CAP_PROP_FRAME_HEIGHT))
                fps = cap.get(cv2.CAP_PROP_FPS)
                frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                duration = frame_count / fps
                output_dir = input_file.parent / '修改'
                try:
                    output_dir.mkdir(exist_ok=True)
                except PermissionError as e:
                    self.log_message.emit(f'❌ 权限错误: 无法创建输出目录: {str(e)}')
                    continue

                output_path = str(output_dir / input_file.name)
                self.current_output = output_path
                filters = []
                filters.append(f'eq=saturation={self.saturation}')
                if self.sharpness != 1.0:
                    luma_amount = (self.sharpness - 1.0) * 3
                    filters.append(f'unsharp=5:5:{luma_amount}:5:5:{luma_amount}')
                self.log_message.emit(f'处理文件: {input_file.name}')
                self.log_message.emit(f'输入路径: {input_path}')
                self.log_message.emit(f'输出路径: {output_path}')
                self.log_message.emit(f'原始尺寸: {width}x{height}')
                self.log_message.emit(f'原始帧率: {fps} fps\n')

                filter_string = ','.join(filters) if filters else 'null'
                self.log_message.emit('开始处理视频...')
                if self.stuck_duration != 1:
                    self.stuck_duration_new = self.stuck_duration
                    if self.use_gpu:
                        if self.speed_mode:
                            cmd = ['ffmpeg', '-hwaccel', 'cuda', '-i', input_path, '-y', '-vf', filter_string, '-c:v', 'h264_nvenc', '-preset', 'p2', '-profile:v', 'high', '-rc', 'cbr', '-b:v', f'{self.bitrate}k', '-minrate', f'{self.bitrate}k', '-maxrate', f'{self.bitrate}k', '-bufsize', f'{self.bitrate}k', '-b:a', f'{self.audio_bitrate}k', output_path]
                        else:  # inserted
                            cmd = ['ffmpeg', '-hwaccel', 'cuda', '-i', input_path, '-y', '-vf', filter_string, '-c:v', 'h264_nvenc', '-preset', 'p7', '-profile:v', 'high', '-rc', 'cbr', '-b:v', f'{self.bitrate}k', '-maxrate', f'{self.bitrate}k', '-bufsize', f'{self.bitrate}k', '-b:a', f'{self.audio_bitrate}k', '-spatial-aq', '1', '-temporal-aq', '1', output_path]
                    else:  # inserted
                        if not self.use_amd or self.speed_mode:
                            cmd = ['ffmpeg', '-hwaccel', 'dxva2', '-i', input_path, '-y', '-vf', filter_string, '-c:v', 'h264_amf', '-quality', 'speed', '-profile:v', 'main', '-rc', 'cbr', '-b:v', f'{self.bitrate}k', '-minrate', f'{self.bitrate}k', '-maxrate', f'{self.bitrate}k', '-bufsize', f'{self.bitrate}k', '-b:a', f'{self.audio_bitrate}k', output_path]
                        else:  # inserted
                            cmd = ['ffmpeg', '-hwaccel', 'dxva2', '-i', input_path, '-y', '-vf', filter_string, '-c:v', 'h264_amf', '-quality', 'balanced', '-profile:v', 'high', '-rc', 'cbr', '-b:v', f'{self.bitrate}k', '-minrate', f'{self.bitrate}k', '-maxrate', f'{self.bitrate}k', '-bufsize', f'{self.bitrate}k', '-b:a', f'{self.audio_bitrate}k', output_path]
                    if self.use_intel:
                        if self.speed_mode:
                            cmd = ['ffmpeg', '-hwaccel', 'qsv', '-i', input_path, '-y', '-vf', filter_string, '-c:v', 'h264_qsv', '-preset', 'veryfast', '-profile:v', 'main', '-b:v', f'{self.bitrate}k', '-minrate', f'{self.bitrate}k', '-maxrate', f'{self.bitrate}k', '-bufsize', f'{self.bitrate}k', '-b:a', f'{self.audio_bitrate}k', output_path]
                        else:  # inserted
                            cmd = ['ffmpeg', '-hwaccel', 'qsv', '-i', input_path, '-y', '-vf', filter_string, '-c:v', 'h264_qsv', '-preset', 'slower', '-profile:v', 'high', '-b:v', f'{self.bitrate}k', '-minrate', f'{self.bitrate}k', '-maxrate', f'{self.bitrate}k', '-bufsize', f'{self.bitrate}k', '-b:a', f'{self.audio_bitrate}k', output_path]
                    else:  # inserted
                        cmd = ['ffmpeg', '-i', input_path, '-y', '-vf', filter_string, '-c:v', 'libx264', '-preset', 'slow', '-profile:v', 'high', '-b:v', f'{self.bitrate}k', '-minrate', f'{self.bitrate}k', '-maxrate', f'{self.bitrate}k', '-bufsize', f'{self.bitrate}k', '-b:a', f'{self.audio_bitrate}k', output_path]
                else:  # inserted
                    if self.use_gpu:
                        if self.speed_mode:
                            cmd = ['ffmpeg', '-hwaccel', 'cuda', '-i', input_path, '-y', '-vf', filter_string, '-c:v', 'h264_nvenc', '-preset', 'p2', '-profile:v', 'high', '-rc', 'vbr', '-cq', '23', '-qmin', '23', '-qmax', '28', '-b:v', f'{self.bitrate}k', '-r', f'{fps}-vsync', 'cfr', '-b:a', f'{self.audio_bitrate}k', output_path]
                        else:  # inserted
                            cmd = ['ffmpeg', '-hwaccel', 'cuda', '-i', input_path, '-y', '-vf', filter_string, '-c:v', 'h264_nvenc', '-preset', 'p7', '-profile:v', 'high', '-rc', 'vbr', '-cq', '23', '-qmin', '23', '-qmax', '28', '-b:v', f'{self.bitrate}k', '-b:a', f'{self.audio_bitrate}k', '-spatial-aq', '1', '-temporal-aq', '1', output_path]
                    else:  # inserted
                        if self.use_amd:
                            if self.speed_mode:
                                cmd = ['ffmpeg', '-hwaccel', 'dxva2', '-i', input_path, '-y', '-vf', filter_string, '-c:v', 'h264_amf', '-quality', 'speed', '-profile:v', 'main', '-rc', 'vbr_peak', '-qp_i', '23', '-qp_p', '25', '-qp_b', '27', f'-b:v', f'{self.bitrate}k', '-maxrate', f'{self.bitrate}k', '-bufsize', f'{self.audio_bitrate}k', output_path]
                            else:  # inserted
                                cmd = ['ffmpeg', '-hwaccel', 'dxva2', '-i', input_path, '-y', '-vf', filter_string, '-c:v', 'h264_amf', '-quality', 'balanced', '-profile:v', 'high', '-rc', 'vbr_peak', '-qp_i', '26', '-qp_p', '28', '-qp_b', '30', f'{self.bitrate}k', '-maxrate', f'{self.bitrate}k', '-bufsize', f'{self.audio_bitrate}k', output_path]
                        else:  # inserted
                            if self.use_intel:
                                if self.speed_mode:
                                    cmd = ['ffmpeg', '-hwaccel', 'qsv', '-i', input_path, '-y', '-vf', filter_string, '-c:v', 'h264_qsv', '-preset', 'veryfast', '-profile:v', 'main', '-global_quality', '23', '-b:v', f'{self.bitrate}k', '-maxrate', f'{self.bitrate}k', '-bufsize', f'{self.bitrate}k', '-r', f'{fps}', '-vsync', 'cfr', '-b:a', f'{self.audio_bitrate}k', output_path]
                                else:  # inserted
                                    cmd = ['ffmpeg', '-hwaccel', 'qsv', '-i', input_path, '-y', '-vf', filter_string, '-c:v', 'h264_qsv', '-preset', 'slower', '-profile:v', 'high', '-global_quality', '23', '-look_ahead', '1', '-b:v', f'{self.bitrate}k', '-maxrate', f'{self.bitrate}k', '-bufsize', f'{self.bitrate}k', '-b:a', f'{self.audio_bitrate}k', output_path]
                            else:  # inserted
                                cmd = ['ffmpeg', '-i', input_path, '-y', '-vf', filter_string, '-c:v', 'libx264', '-preset', 'slow', '-profile:v', 'high', '-crf', '23', '-b:v', f'{self.bitrate}k', '-maxrate', f'{self.bitrate}k', '-bufsize', f'{self.bitrate}k', '-b:a', f'{self.audio_bitrate}k', output_path]

                try:
                    process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True, encoding='utf-8', errors='replace', startupinfo=startupinfo if sys.platform.startswith('win') else None)
                    while True:
                        if not self.is_running:
                            process.kill()
                            break
                        output = process.stderr.readline()
                        if output == '' and process.poll() is not None:
                            break
                        if output:
                            match = re.search('time=(\\d+:\\d+:\\d+\\.\\d+)', output)
                            if match:
                                current_time_str = match.group(1)
                                current_time = self.convert_time_to_seconds(current_time_str)
                                if duration > 0:
                                    progress = int(current_time / duration * 100)
                                    self.progress_updated.emit(progress)
                            elif any(err in output.lower() for err in ['error', 'fail']):
                                self.log_message.emit(output.strip())
                except Exception as e:
                    self.log_message.emit(f'❌ 处理过程出错: {str(e)}\n')
                    continue
                finally:
                    if process:
                        process.stdout.close()
                        process.stderr.close()
                        time.sleep(0.5)
                        if process.returncode == 0:
                            self.log_message.emit(f'✅ {input_file.name} 处理完成')
                            self.log_message.emit(f'   保存至: {output_path}\n')
                            self.item_finished.emit(item)
                        else:  # inserted
                            self.log_message.emit(f'❌ {input_file.name} 处理失败\n')

            if self.is_running:
                self.log_message.emit('=========\n')
            self.log_message.emit('❌ 错误: 找不到 ffmpeg，请确保 ffmpeg 已正确安装并添加到系统环境变量中。')
            return
        except subprocess.CalledProcessError:
            self.log_message.emit('❌ 错误: ffmpeg 命令执行失败。')
            return

    def convert_time_to_seconds(self, time_str):
        """将时间字符串转换为秒数"""  # inserted
        h, m, s = map(float, time_str.split(':'))
        return int(h * 3600 + m * 60 + s)

    def stop(self):
        """停止处理"""  # inserted
        self.is_running = False

class SaturationSharpnessInput(QLineEdit):
    def __init__(self, parent=None, min_val=0.8, max_val=1.2):
        super().__init__(parent)
        self.min_val = min_val
        self.max_val = max_val
        self.setValidator(QDoubleValidator())

    def focusOutEvent(self, event):
        super().focusOutEvent(event)
        try:
            value = float(self.text() or '1.0')
            if value < self.min_val:
                self.setText(str(self.min_val))
            else:  # inserted
                if value > self.max_val:
                    self.setText(str(self.max_val))
            self.setText(f'{float(self.text()):.1f}')
        except ValueError:
            self.setText('1.0')
        else:  # inserted
            pass
class VideoProcessor(QMainWindow):
    VERSION_MAJOR = 6
    VERSION_MINOR = 1
    VERSION_PATCH = 0
    VERSION_BUILD = 0
    VERSION = f'{VERSION_MAJOR}.{VERSION_MINOR}.{VERSION_PATCH}.{VERSION_BUILD}'
    AES_KEY = b'JDHNAYNDHNHFDJNX'
    AES_IV = b'DHNAYNDHNHFDJNX2'
    DEBUG = False
    use_cpu_for_fusion = None
    use_quality_mode = None

    def __init__(self):
        self.presets = {}
        self.load_settings()
        super().__init__()
        self.protected_shortcuts = {'windows+e', 'ctrl+s', 'ctrl+x', 'ctrl+o', 'windows+r', 'ctrl+z', 'windows+d', 'windows+l', 'alt+f4', 'ctrl+a', 'ctrl+v', 'ctrl+n', 'ctrl+f', 'ctrl+w', 'ctrl+c', 'alt+tab', 'ctrl+y', 'ctrl+p', 'ctrl+r'}
        if getattr(sys, 'frozen', False):
            base_path = sys._MEIPASS
        else:  # inserted
            base_path = os.path.dirname(os.path.abspath(__file__))
        ffmpeg_path = os.path.join(base_path, 'bin')
        if ffmpeg_path not in os.environ['PATH']:
            os.environ['PATH'] = ffmpeg_path + os.pathsep + os.environ['PATH']
        self.create_widgets()
        self.detect_and_set_gpu_options()
        self.initUI()
        self.setStyleSheet(self.get_dark_style())
        self.duration_input.textChanged.connect(self.update_one_click_button_state)
        self.update_one_click_button_state()

    def create_widgets(self):
        """创建所有控件"""  # inserted
        self.path_input = QLineEdit()
        self.path_input.setReadOnly(True)
        self.path_input.setPlaceholderText('点击选择或拖入视频到下方列表区域，支持 .mp4 和 .mov 格式')
        self.path_input.setStyleSheet('\n            QLineEdit {\n                background-color: #2b2b2b;\n                color: white;\n                border: 1px solid #555555;\n                border-radius: 3px;\n                padding: 5px;\n                cursor: pointer;\n            }\n            QLineEdit:hover {\n                background-color: #323232;\n                border-color: #666666;\n            }\n        ')
        self.path_input.mousePressEvent = self.select_files
        self.saturation_input = SaturationSharpnessInput(min_val=0.8, max_val=1.2)
        self.saturation_input.setFixedWidth(80)
        self.saturation_input.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.saturation_input.setText('1.0')
        self.saturation_input.setStyleSheet('\n            QLineEdit {\n                background-color: #2b2b2b;\n                color: white;\n                border: 1px solid #555555;\n                border-radius: 3px;\n                padding: 5px;\n                min-height: 20px;\n            }\n            QLineEdit:focus {\n                border: 1px solid #666666;\n            }\n        ')
        self.sharpness_input = SaturationSharpnessInput(min_val=0.8, max_val=1.2)
        self.sharpness_input.setFixedWidth(80)
        self.sharpness_input.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.sharpness_input.setText('1.0')
        self.sharpness_input.setStyleSheet('\n            QLineEdit {\n                background-color: #2b2b2b;\n                color: white;\n                border: 1px solid #555555;\n                border-radius: 3px;\n                padding: 5px;\n                min-height: 20px;\n            }\n            QLineEdit:focus {\n                border: 1px solid #666666;\n            }\n        ')
        self.clear_btn = QPushButton('清空')
        self.clear_btn.setToolTip('清空所有输入和文件列表')
        self.clear_btn.setStyleSheet('\n            QPushButton {\n                background-color: #4a4a4a;\n                border: none;\n                border-radius: 3px;\n                padding: 5px 15px;\n                color: #ffffff;\n            }\n            QPushButton:hover {\n                background-color: #d32f2f;\n            }\n            QPushButton:pressed {\n                background-color: #b71c1c;\n            }\n        ')
        self.video_list = VideoListWidget()
        self.video_list.setMinimumSize(200, 300)
        self.video_list.main_window = self
        self.select_all_btn = QPushButton('全选')
        self.select_all_btn.setFixedSize(80, 30)
        self.select_all_btn.setStyleSheet('\n            QPushButton {\n                background-color: #4a4a4a;\n                border: none;\n                border-radius: 3px;\n                color: white;\n                padding: 5px 15px;\n            }\n            QPushButton:hover {\n                background-color: #5a5a5a;\n            }\n            QPushButton:pressed {\n                background-color: #3a3a3a;\n            }\n        ')
        self.select_all_btn.clicked.connect(self.toggle_select_all)
        self.delete_btn = QPushButton('删除')
        self.delete_btn.setFixedSize(80, 30)
        self.delete_btn.setStyleSheet('\n            QPushButton {\n                background-color: #f44336;\n                border: none;\n                border-radius: 3px;\n                color: white;\n                padding: 5px 15px;\n            }\n            QPushButton:hover {\n                background-color: #d32f2f;\n            }\n            QPushButton:pressed {\n                background-color: #b71c1c;\n            }\n            QPushButton:disabled {\n                background-color: #666666;\n                color: #999999;\n            }\n        ')
        self.delete_btn.clicked.connect(self.delete_selected_items)
        self.video_list2 = VideoListWidget()
        self.video_list2.setMinimumSize(200, 300)
        self.video_list2.main_window = self
        self.select_all_btn2 = QPushButton('全选')
        self.select_all_btn2.setFixedSize(80, 30)
        self.select_all_btn2.setStyleSheet('\n            QPushButton {\n                background-color: #4a4a4a;\n                border: none;\n                border-radius: 3px;\n                color: white;\n                padding: 5px 15px;\n            }\n            QPushButton:hover {\n                background-color: #5a5a5a;\n            }\n            QPushButton:pressed {\n                background-color: #3a3a3a;\n            }\n        ')
        self.select_all_btn2.clicked.connect(self.toggle_select_all2)
        self.delete_btn2 = QPushButton('删除')
        self.delete_btn2.setFixedSize(80, 30)
        self.delete_btn2.setStyleSheet('\n            QPushButton {\n                background-color: #f44336;\n                border: none;\n                border-radius: 3px;\n                color: white;\n                padding: 5px 15px;\n            }\n            QPushButton:hover {\n                background-color: #d32f2f;\n            }\n            QPushButton:pressed {\n                background-color: #b71c1c;\n            }\n            QPushButton:disabled {\n                background-color: #666666;\n                color: #999999;\n            }\n        ')
        self.delete_btn2.clicked.connect(self.delete_selected_items2)
        self.width_input = QLineEdit()
        self.width_input.setFixedWidth(80)
        self.width_input.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.width_input.setValidator(QIntValidator(1, 9999))
        self.width_input.setStyleSheet('\n            QLineEdit {\n                background-color: #2b2b2b;\n                color: white;\n                border: 1px solid #555555;\n                border-radius: 3px;\n                padding: 5px;\n                min-height: 20px;\n            }\n            QLineEdit:focus {\n                border: 1px solid #666666;\n            }\n        ')
        self.height_input = QLineEdit()
        self.height_input.setFixedWidth(80)
        self.height_input.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.height_input.setValidator(QIntValidator(1, 9999))
        self.height_input.setStyleSheet('\n            QLineEdit {\n                background-color: #2b2b2b;\n                color: white;\n                border: 1px solid #555555;\n                border-radius: 3px;\n                padding: 5px;\n                min-height: 20px;\n            }\n            QLineEdit:focus {\n                border: 1px solid #666666;\n            }\n        ')
        self.bitrate_input = QLineEdit()
        self.bitrate_input.setFixedWidth(80)
        self.bitrate_input.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.bitrate_input.setText('5000')
        self.bitrate_input.setValidator(QIntValidator(1, 99999))
        self.bitrate_input.setStyleSheet('\n            QLineEdit {\n                background-color: #2b2b2b;\n                color: white;  /* 改为白色 */\n                border: 1px solid #555555;\n                border-radius: 3px;\n                padding: 5px;\n                min-height: 20px;\n            }\n            QLineEdit:focus {\n                border: 1px solid #666666;\n            }\n        ')
        self.audio_input = QLineEdit()
        self.audio_input.setFixedWidth(80)
        self.audio_input.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.audio_input.setText('120')
        self.audio_input.setValidator(QIntValidator(1, 999))
        self.audio_input.setStyleSheet('\n            QLineEdit {\n                background-color: #2b2b2b;\n                color: white;  /* 改为白色 */\n                border: 1px solid #555555;\n                border-radius: 3px;\n                padding: 5px;\n                min-height: 20px;\n            }\n            QLineEdit:focus {\n                border: 1px solid #666666;\n            }\n\n        ')
        self.duration_input = QLineEdit()
        self.duration_input.setFixedWidth(80)
        self.duration_input.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.duration_input.setValidator(QIntValidator(1, 999999))
        self.duration_input.setStyleSheet('\n            QLineEdit {\n                background-color: #2b2b2b;\n                color: white;\n                border: 1px solid #555555;\n                border-radius: 3px;\n                padding: 5px;\n                min-height: 20px;\n            }\n            QLineEdit:focus {\n                border: 1px solid #666666;\n            }\n        ')
        self.stuck_duration_input = QLineEdit()
        self.stuck_duration_input.setFixedWidth(80)
        self.stuck_duration_input.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.stuck_duration_input.setText('1')
        self.stuck_duration_input.setValidator(QIntValidator(1, 999999))
        self.stuck_duration_input.setStyleSheet('\n            QLineEdit {\n                background-color: #2b2b2b;\n                color: white;\n                border: 1px solid #555555;\n                border-radius: 3px;\n                padding: 5px;\n                min-height: 20px;\n            }\n            QLineEdit:focus {\n                border: 1px solid #666666;\n            }\n        ')
        self.gpu_group = QGroupBox('GPU加速')
        self.gpu_layout = QHBoxLayout()
        self.cpu_radio = QRadioButton('CPU')
        self.amd_radio = QRadioButton('AMD')
        self.gpu_radio = QRadioButton('NVIDIA')
        self.intel_radio = QRadioButton('Intel')
        self.cpu_radio.setChecked(True)
        self.gpu_layout.addWidget(self.cpu_radio)
        self.gpu_layout.addWidget(self.amd_radio)
        self.gpu_layout.addWidget(self.gpu_radio)
        self.gpu_layout.addWidget(self.intel_radio)
        self.gpu_group.setLayout(self.gpu_layout)
        self.cl_btn = QPushButton('处理')
        self.cl_btn.setFixedSize(80, 30)
        self.cl_btn.setStyleSheet('\n            QPushButton {\n                background-color: #4CAF50;\n                border: none;\n                border-radius: 3px;\n                color: white;\n                padding: 5px 15px;\n            }\n            QPushButton:hover {\n                background-color: #45a049;\n            }\n            QPushButton:pressed {\n                background-color: #3d8b40;\n            }\n        ')
        self.mode_group = QGroupBox()
        self.mode_group.setStyleSheet('\n            QGroupBox {\n                border: none;\n                margin-top: 0px;\n            }\n        ')
        self.mode_layout = QHBoxLayout()
        self.mode_layout.setContentsMargins(0, 0, 0, 0)
        self.mode_layout.setSpacing(10)
        self.speed_radio = QRadioButton('速度')
        self.quality_radio = QRadioButton('质量')
        radio_style = '\n            QRadioButton {\n                color: white;\n                spacing: 5px;\n            }\n            QRadioButton::indicator {\n                width: 13px;\n                height: 13px;\n            }\n            QRadioButton::indicator:unchecked {\n                background-color: #2b2b2b;\n                border: 2px solid #555555;\n                border-radius: 7px;\n            }\n            QRadioButton::indicator:checked {\n                background-color: #4CAF50;\n                border: 2px solid #4CAF50;\n                border-radius: 7px;\n            }\n            QRadioButton::indicator:hover {\n                border-color: #666666;\n            }\n        '
        self.speed_radio.setStyleSheet(radio_style)
        self.quality_radio.setStyleSheet(radio_style)
        self.quality_radio.setChecked(True)
        self.mode_layout.addWidget(self.speed_radio)
        self.mode_layout.addWidget(self.quality_radio)
        self.mode_layout.addWidget(self.cl_btn)
        self.mode_group.setLayout(self.mode_layout)
        self.progress_bar = QProgressBar()
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.process_btn = QPushButton('执行')
        self.process_btn.setFixedSize(80, 30)
        self.process_btn.setStyleSheet('\n            QPushButton {\n                background-color: #4CAF50;\n                border: none;\n                border-radius: 3px;\n                color: white;\n                padding: 5px 15px;\n            }\n            QPushButton:hover {\n                background-color: #45a049;\n            }\n            QPushButton:pressed {\n                background-color: #3d8b40;\n            }\n        ')
        self.one_click_btn = QPushButton('一键处理')
        self.one_click_btn.setFixedSize(80, 30)
        self.one_click_btn.setStyleSheet('\n            QPushButton {\n                background-color: #2196F3;\n                border: none;\n                border-radius: 3px;\n                color: white;\n                padding: 5px 15px;\n            }\n            QPushButton:hover {\n                background-color: #1976D2;\n            }\n            QPushButton:pressed {\n                background-color: #1565C0;\n            }\n        ')
        self.merge_video_btn = QPushButton('视频合并')
        self.merge_video_btn.setFixedSize(80, 30)
        self.merge_video_btn.setStyleSheet('\n            QPushButton {\n                background-color: #FF9800;\n                border: none;\n                border-radius: 3px;\n                color: white;\n                padding: 5px 15px;\n            }\n            QPushButton:hover {\n                background-color: #F57C00;\n            }\n            QPushButton:pressed {\n                background-color: #EF6C00;\n            }\n        ')
        self.fusion_video_btn = QPushButton('视频豹合')
        self.fusion_video_btn.setFixedSize(80, 30)
        self.fusion_video_btn.setStyleSheet('\n            QPushButton {\n                background-color: #9C27B0;\n                border: none;\n                border-radius: 3px;\n                color: white;\n                padding: 5px 15px;\n            }\n            QPushButton:hover {\n                background-color: #8E24AA;\n            }\n            QPushButton:pressed {\n                background-color: #7B1FA2;\n            }\n        ')
        self.ks_fusion_btn = QPushButton('KS豹合')
        self.ks_fusion_btn.setFixedSize(80, 30)
        self.ks_fusion_btn.setStyleSheet('\n            QPushButton {\n                background-color: #E91E63;\n                border: none;\n                border-radius: 3px;\n                color: white;\n                padding: 5px 15px;\n            }\n            QPushButton:hover {\n                background-color: #D81B60;\n            }\n            QPushButton:pressed {\n                background-color: #C2185B;\n            }\n        ')
        self.process_btn.clicked.connect(self.process_videos)
        self.merge_video_btn.clicked.connect(self.handle_video_merge)
        self.fusion_video_btn.clicked.connect(self.handle_video_fusion)
        self.ks_fusion_btn.clicked.connect(self.handle_ks_fusion)
        self.video_list.itemSelectionChanged.connect(self.on_selection_changed)
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setStyleSheet('\n            QTextEdit {\n                background-color: #1e1e1e;\n                color: #00ff00;\n                border: 1px solid #444444;\n                border-radius: 0px;\n                padding: 5px;\n                font-family: Consolas, Monaco, monospace;\n                font-size: 12px;\n                line-height: 1.2;\n            }\n            QScrollBar:vertical {\n                border: none;\n                background: #2b2b2b;\n                width: 10px;\n                margin: 0px;\n            }\n            QScrollBar::handle:vertical {\n                background: #666666;\n                min-height: 20px;\n                border-radius: 5px;\n            }\n            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {\n                height: 0px;\n            }\n        ')
        self.log_text.setMinimumHeight(150)
        self.log_text.setPlaceholderText('处理日志将在此显示......')
        self.blue_btn = QPushButton('蓝色按钮')
        self.blue_btn.setFixedSize(80, 30)
        self.blue_btn.setStyleSheet('\n            QPushButton {\n                background-color: #2196F3;\n                border: none;\n                border-radius: 3px;\n                color: white;\n                padding: 5px 15px;\n            }\n            QPushButton:hover {\n                background-color: #1976D2;\n            }\n            QPushButton:pressed {\n                background-color: #1565C0;\n            }\n        ')
        self.stuck_duration_seconds_label = QLabel('秒')
        self.stuck_duration_seconds_label.setStyleSheet('\n            QLabel {\n                color: white;\n                padding-left: 4px;\n                qproperty-alignment: AlignCenter;\n                min-width: 30px;\n            }\n        ')
        self.seconds_label = QLabel('秒')
        self.seconds_label.setStyleSheet('\n            QLabel {\n                color: white;\n                padding-left: 4px;\n                qproperty-alignment: AlignCenter;\n                min-width: 30px;\n            }\n        ')
        self.activate_btn = QPushButton('生效')
        self.activate_btn.setCheckable(True)
        self.activate_btn.setFixedSize(80, 30)
        self.activate_btn.setStyleSheet('\n            QPushButton {\n                background-color: #4CAF50;\n                border: none;\n                border-radius: 3px;\n                color: white;\n                padding: 5px;\n                min-width: 80px;\n                max-width: 80px;\n                min-height: 30px;\n                max-height: 30px;\n            }\n            QPushButton:hover {\n                background-color: #45a049;\n            }\n            QPushButton:checked {\n                background-color: #f44336;\n            }\n            QPushButton:checked:hover {\n                background-color: #d32f2f;\n            }\n        ')
        self.activate_btn.clicked.connect(self.toggle_key_combination)

    def detect_and_set_gpu_options(self):
        """检测GPU并设置相应选项"""  # inserted
        nvidia_available = self.check_nvidia_gpu()
        amd_available = self.check_amd_gpu()
        intel_available = self.check_intel_gpu()
        self.print_log('GPU检测结果：')
        if nvidia_available:
            self.print_log('✅ 检测到 NVIDIA GPU，可以使用 NVIDIA 加速')
        else:  # inserted
            self.print_log('❌ 未检测到 NVIDIA GPU')
        if amd_available:
            self.print_log('✅ 检测到 AMD GPU，可以使用 AMD 加速')
        else:  # inserted
            self.print_log('❌ 未检测到 AMD GPU')
        if intel_available:
            self.print_log('✅ 检测到 Intel GPU，可以使用 QSV 加速')
        else:  # inserted
            self.print_log('❌ 未检测到 Intel GPU')
        if not nvidia_available and (not amd_available) and (not intel_available):
            self.print_log('ℹ️ 将使用 CPU 处理视频')
        self.print_log('')
        self.gpu_radio.setEnabled(nvidia_available)
        self.amd_radio.setEnabled(amd_available)
        self.intel_radio.setEnabled(intel_available)
        if nvidia_available:
            self.gpu_radio.setChecked(True)
        else:  # inserted
            if amd_available:
                self.amd_radio.setChecked(True)
            else:  # inserted
                if intel_available:
                    self.intel_radio.setChecked(True)
                else:  # inserted
                    self.cpu_radio.setChecked(True)
        if not nvidia_available:
            self.gpu_radio.setToolTip('未检测到支持的NVIDIA显卡')
        if not amd_available:
            self.amd_radio.setToolTip('未检测到支持的AMD显卡')
        if not intel_available:
            self.intel_radio.setToolTip('未检测到支持的Intel显卡')

    def check_nvidia_gpu(self):
        """检查是否有可用的NVIDIA GPU"""  # inserted
        try:
            startupinfo = None
            if sys.platform.startswith('win'):
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE
            result = subprocess.run(['nvidia-smi'], capture_output=True, text=True, startupinfo=startupinfo)
            return result.returncode == 0
        except:
            return False

    def check_amd_gpu(self):
        """检查是否有可用的AMD GPU"""  # inserted
        try:
            if sys.platform.startswith('win'):
                cmd = 'Get-WmiObject Win32_VideoController | Select-Object Name'
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                result = subprocess.run(['powershell', '-Command', cmd], capture_output=True, text=True, startupinfo=startupinfo)
                if result.returncode == 0:
                    output = result.stdout.lower()
                    if 'amd' in output or 'radeon' in output:
                        for line in result.stdout.splitlines():
                            if 'amd' in line.lower() or 'radeon' in line.lower():
                                self.print_log(f'检测到 AMD 显卡: {line.strip()}')
                        return True
        except Exception as e:
            self.print_log(f'AMD GPU 检测失败: {str(e)}')  # 添加日志记录
            return False
        finally:
            pass  # 确保资源释放

    def check_intel_gpu(self):
        """检查是否有可用的Intel GPU"""  # inserted
        try:
            if sys.platform.startswith('win'):
                cmd = 'Get-WmiObject Win32_VideoController | Select-Object Name'
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                result = subprocess.run(['powershell', '-Command', cmd], capture_output=True, text=True, startupinfo=startupinfo)
                if result.returncode == 0:
                    output = result.stdout.lower()
                    if 'intel' in output and ('hd graphics' in output or 'uhd graphics' in output or 'iris' in output):
                        for line in result.stdout.splitlines():
                            if 'intel' in line.lower():
                                self.print_log(f'检测到 Intel 显卡: {line.strip()}')
                        return True
        except Exception as e:
            self.print_log(f'Intel GPU 检测失败: {str(e)}')  # 添加日志记录
            return False
        finally:
            pass  # 确保资源释放



    def initUI(self):
        self.set_application_icon()
        self.setWindowTitle(f'视频处理 v{self.VERSION}')
        self.resize(800, 600)
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet('\n            QTabWidget::pane {\n                border: 1px solid #555555;\n                background: #2b2b2b;\n                border-radius: 3px;\n            }\n            QTabWidget::tab-bar {\n                left: 5px;\n            }\n            QTabBar::tab {\n                background: #3b3b3b;\n                color: #ffffff;\n                padding: 8px 20px;\n                border: 1px solid #555555;\n                border-bottom: none;\n                border-top-left-radius: 4px;\n                border-top-right-radius: 4px;\n                margin-right: 2px;\n            }\n            QTabBar::tab:selected {\n                background: #4CAF50;\n            }\n            QTabBar::tab:hover {\n                background: #45a049;\n            }\n        ')
        self.tab1 = QWidget()
        tab1_layout = QVBoxLayout(self.tab1)
        tab1_layout.setSpacing(10)
        tab1_layout.setContentsMargins(10, 10, 10, 10)
        top_layout = QHBoxLayout()
        top_layout.addWidget(self.path_input, 1)
        top_layout.addWidget(self.clear_btn)
        top_layout.setSpacing(10)
        top_layout.setContentsMargins(0, 0, 0, 0)
        tab1_layout.addLayout(top_layout)
        middle_layout = QHBoxLayout()
        list_layout_left = QVBoxLayout()
        left_title = QLabel('视频重组')
        left_title.setStyleSheet('\n            QLabel {\n                color: #4CAF50;\n                font-size: 12px;\n                padding: 5px;\n            }\n        ')
        list_layout_left.addWidget(left_title)
        list_layout_left.addWidget(self.video_list)
        left_btn_layout = QHBoxLayout()
        left_btn_layout.addWidget(self.select_all_btn)
        left_btn_layout.addWidget(self.delete_btn)
        list_layout_left.addLayout(left_btn_layout)
        list_layout_right = QVBoxLayout()
        right_title = QLabel('时长处理')
        right_title.setStyleSheet('\n            QLabel {\n                color: #4CAF50;\n                font-size: 12px;\n                padding: 5px;\n            }\n        ')
        list_layout_right.addWidget(right_title)
        list_layout_right.addWidget(self.video_list2)
        right_btn_layout = QHBoxLayout()
        right_btn_layout.addWidget(self.select_all_btn2)
        right_btn_layout.addWidget(self.delete_btn2)
        list_layout_right.addLayout(right_btn_layout)
        middle_layout.addLayout(list_layout_left)
        middle_layout.addLayout(list_layout_right)
        right_layout = QVBoxLayout()
        right_layout.setSpacing(15)
        right_layout.setContentsMargins(0, 0, 0, 0)
        settings_group = QGroupBox()
        settings_group.setStyleSheet('\n            QGroupBox {\n                border: 2px solid #4CAF50;\n                border-radius: 6px;\n                padding: 15px;\n                margin-top: 0px;  /* 移除顶部边距 */\n            }\n        ')
        settings_layout = QVBoxLayout(settings_group)
        settings_layout.setSpacing(10)
        label_style = '\n            QLabel {\n                color: white;\n                padding: 0px;\n                margin: 0px 2px;\n                qproperty-alignment: AlignCenter;\n            }\n        '
        unit_label_style = '\n            QLabel {\n                color: white;\n                padding-left: 4px;\n                qproperty-alignment: AlignCenter;\n                min-width: 30px;\n            }\n        '
        size_layout = QHBoxLayout()
        size_label = QLabel('尺寸')
        size_label.setFixedWidth(80)
        size_label.setStyleSheet(label_style)
        width_label = QLabel('宽')
        width_label.setFixedWidth(25)
        width_label.setStyleSheet(label_style)
        height_label = QLabel('高')
        height_label.setFixedWidth(25)
        height_label.setStyleSheet(label_style)
        size_layout.addWidget(size_label)
        size_layout.addWidget(width_label)
        size_layout.addWidget(self.width_input)
        size_layout.addWidget(height_label)
        size_layout.addWidget(self.height_input)
        size_layout.addStretch()
        size_layout.setSpacing(4)
        bitrate_layout = QHBoxLayout()
        bitrate_label = QLabel('数据速率')
        bitrate_label.setFixedWidth(80)
        bitrate_label.setStyleSheet(label_style)
        kbps_label1 = QLabel('kbps')
        kbps_label1.setStyleSheet(unit_label_style)
        bitrate_layout.addWidget(bitrate_label)
        bitrate_layout.addWidget(self.bitrate_input)
        bitrate_layout.addWidget(kbps_label1)
        bitrate_layout.addStretch()
        audio_layout = QHBoxLayout()
        audio_label = QLabel('音频比特率')
        audio_label.setFixedWidth(80)
        audio_label.setStyleSheet(label_style)
        kbps_label2 = QLabel('kbps')
        kbps_label2.setStyleSheet(unit_label_style)
        audio_layout.addWidget(audio_label)
        audio_layout.addWidget(self.audio_input)
        audio_layout.addWidget(kbps_label2)
        audio_layout.addStretch()
        saturation_layout = QHBoxLayout()
        saturation_label = QLabel('饱和度')
        saturation_label.setFixedWidth(80)
        saturation_label.setStyleSheet(label_style)
        saturation_layout.addWidget(saturation_label)
        saturation_layout.addWidget(self.saturation_input)
        saturation_layout.addStretch()
        sharpness_layout = QHBoxLayout()
        sharpness_label = QLabel('锐化')
        sharpness_label.setFixedWidth(80)
        sharpness_label.setStyleSheet(label_style)
        sharpness_layout.addWidget(sharpness_label)
        sharpness_layout.addWidget(self.sharpness_input)
        sharpness_layout.addStretch()
        settings_layout.addLayout(size_layout)
        settings_layout.addLayout(bitrate_layout)
        settings_layout.addLayout(audio_layout)
        settings_layout.addLayout(saturation_layout)
        settings_layout.addLayout(sharpness_layout)
        options_layout = QVBoxLayout()
        gpu_btn_layout = QHBoxLayout()
        gpu_btn_layout.addWidget(self.gpu_group)
        gpu_btn_layout.addWidget(self.mode_group)
        options_layout.addLayout(gpu_btn_layout)
        stuck_duration_layout = QHBoxLayout()
        stuck_duration_label = QLabel('卡完播')
        stuck_duration_label.setFixedWidth(80)
        stuck_duration_label.setStyleSheet(label_style)
        stuck_duration_layout.addWidget(stuck_duration_label)
        stuck_duration_layout.addWidget(self.stuck_duration_input)
        stuck_duration_layout.addWidget(self.stuck_duration_seconds_label)
        stuck_duration_layout.addStretch()
        options_layout.addLayout(stuck_duration_layout)
        options_layout.addWidget(self.progress_bar)
        settings_layout.addLayout(options_layout)
        right_layout.addWidget(settings_group)
        duration_layout = QHBoxLayout()
        duration_layout.setContentsMargins(0, 0, 0, 0)
        duration_layout.setSpacing(4)
        duration_label = QLabel('伪时长')
        duration_label.setFixedWidth(80)
        duration_label.setStyleSheet(label_style)
        duration_layout.addWidget(duration_label)
        duration_layout.addWidget(self.duration_input)
        duration_layout.addWidget(self.seconds_label)
        duration_layout.addWidget(self.process_btn)
        duration_layout.addStretch()
        duration_layout.addWidget(self.merge_video_btn)
        duration_layout.addWidget(self.fusion_video_btn)
        duration_layout.addWidget(self.ks_fusion_btn)
        duration_layout.addWidget(self.one_click_btn)
        right_layout.addLayout(duration_layout)
        right_layout.addStretch()
        middle_layout.addLayout(right_layout)
        tab1_layout.addLayout(middle_layout)
        tab1_layout.addWidget(self.log_text)
        self.tab_widget.addTab(self.tab1, '视频处理')
        self.tab2 = QWidget()
        tab2_layout = QVBoxLayout(self.tab2)
        tab2_layout.setSpacing(10)
        tab2_layout.setContentsMargins(10, 10, 10, 10)
        final_key_layout = QHBoxLayout()
        final_key_label = QLabel('快捷键:')
        final_key_label.setStyleSheet('color: white;')
        self.final_key_input = QLineEdit()
        self.final_key_input.setReadOnly(True)
        self.final_key_input.setPlaceholderText('点击此处按下键盘按键...')
        self.final_key_input.setStyleSheet('\n            QLineEdit {\n                background-color: #2b2b2b;\n                color: white;\n                border: 1px solid #555555;\n                border-radius: 3px;\n                padding: 5px;\n            }\n            QLineEdit:focus {\n                border: 1px solid #4CAF50;\n            }\n        ')
        self.final_key_input.installEventFilter(self)
        final_key_layout.addWidget(final_key_label)
        final_key_layout.addWidget(self.final_key_input)
        tab2_layout.addLayout(final_key_layout)
        self.combo_key_inputs = []
        for i in range(5):
            combo_layout = QHBoxLayout()
            if i == 0:
                label_text = '切割:'
            else:
                if i == 1:
                    label_text = '移动:'
                else:
                    if i == 2:
                        label_text = '删除:'
                    else:
                        label_text = f'组合键 {i + 1}:'
            combo_label = QLabel(label_text)
            combo_label.setStyleSheet('color: white;')
            combo_input = QLineEdit()
            combo_input.setReadOnly(True)
            combo_input.setPlaceholderText('点击此处按下键盘按键...')
            combo_input.setStyleSheet('\n                QLineEdit {\n                    background-color: #2b2b2b;\n                    color: white;\n                    border: 1px solid #555555;\n                    border-radius: 3px;\n                    padding: 5px;\n                }\n                QLineEdit:focus {\n                    border: 1px solid #4CAF50;\n                }\n            ')
            combo_input.installEventFilter(self)
            combo_layout.addWidget(combo_label)
            combo_layout.addWidget(combo_input)
            self.combo_key_inputs.append(combo_input)
            tab2_layout.addLayout(combo_layout)
        self.activate_btn = QPushButton('生效')
        self.activate_btn.setCheckable(True)
        self.activate_btn.setFixedSize(80, 30)
        self.activate_btn.setStyleSheet('\n            QPushButton {\n                background-color: #4CAF50;\n                border: none;\n                border-radius: 3px;\n                color: white;\n                padding: 5px;\n                min-width: 80px;\n                max-width: 80px;\n                min-height: 30px;\n                max-height: 30px;\n            }\n            QPushButton:hover {\n                background-color: #45a049;\n            }\n            QPushButton:checked {\n                background-color: #f44336;\n            }\n            QPushButton:checked:hover {\n                background-color: #d32f2f;\n            }\n        ')
        self.activate_btn.clicked.connect(self.toggle_key_combination)
        tab2_layout.addWidget(self.activate_btn, alignment=Qt.AlignmentFlag.AlignCenter)
        self.tab_widget.addTab(self.tab2, '快捷键')
        self.final_key_input.installEventFilter(self)
        for input_field in self.combo_key_inputs:
            input_field.installEventFilter(self)
        self.activate_btn.toggled.connect(self.toggle_key_combination)
        self.keyboard_listener = None
        self.final_key = None
        self.combo_keys = []
        preset_group = QGroupBox('预设管理')
        preset_group.setStyleSheet('\n            QGroupBox {\n                border: 2px solid #4CAF50;\n                border-radius: 6px;\n                margin-top: 10px;\n                padding: 10px;\n            }\n            QGroupBox::title {\n                color: white;\n                subcontrol-origin: margin;\n                left: 10px;\n            }\n        ')
        preset_layout = QVBoxLayout()
        self.preset_list = QListWidget()
        self.preset_list.setStyleSheet('\n            QListWidget {\n                background-color: #2b2b2b;\n                border: 1px solid #555555;\n                border-radius: 5px;\n                padding: 5px;\n                outline: none;\n            }\n            QListWidget::item {\n                color: #ffffff;\n                background-color: transparent;\n                border-radius: 4px;\n                padding: 8px;\n                margin: 2px;\n            }\n            QListWidget::item:hover {\n                background-color: #3a3a3a;\n                border: 1px solid #666666;\n            }\n            QListWidget::item:selected {\n                background-color: #2d5a2d;\n                border: 1px solid #4CAF50;\n                color: #ffffff;\n            }\n            QListWidget::item:selected:active {\n                background-color: #367c36;\n                border: 1px solid #5cb85c;\n            }\n            QListWidget::item:selected:!active {\n                background-color: #2d5a2d;\n                border: 1px solid #4CAF50;\n            }\n            QListWidget::item:hover:!selected {\n                background-color: #3a3a3a;\n                border: 1px solid #666666;\n            }\n            QListWidget:focus {\n                border: 1px solid #4CAF50;\n            }\n        ')
        preset_layout.addWidget(self.preset_list)
        preset_btn_layout = QHBoxLayout()
        save_preset_btn = QPushButton('保存为预设')
        save_preset_btn.setStyleSheet('\n            QPushButton {\n                background-color: #4CAF50;\n                border: none;\n                border-radius: 3px;\n                color: white;\n                padding: 5px 15px;\n            }\n            QPushButton:hover {\n                background-color: #45a049;\n            }\n        ')
        save_preset_btn.clicked.connect(self.show_add_preset_dialog)
        load_preset_btn = QPushButton('加载预设')
        load_preset_btn.setStyleSheet('\n            QPushButton {\n                background-color: #2196F3;\n                border: none;\n                border-radius: 3px;\n                color: white;\n                padding: 5px 15px;\n            }\n            QPushButton:hover {\n                background-color: #1976D2;\n            }\n        ')
        load_preset_btn.clicked.connect(lambda: self.load_preset(self.preset_list.currentItem().text()) if self.preset_list.currentItem() else None)
        preset_btn_layout.addWidget(save_preset_btn)
        preset_btn_layout.addWidget(load_preset_btn)
        preset_layout.addLayout(preset_btn_layout)
        preset_group.setLayout(preset_layout)
        tab2_layout.addWidget(preset_group)
        main_layout.addWidget(self.tab_widget)
        self.clear_btn.clicked.connect(self.clear_all)
        self.cl_btn.clicked.connect(self.handle_cl)
        self.one_click_btn.clicked.connect(self.handle_one_click_process)
        if hasattr(self, 'last_keys'):
            if self.last_keys.get('final_key'):
                self.final_key_input.setText(self.last_keys['final_key'])
            for i, key in enumerate(self.last_keys.get('combo_keys', [])):
                if i < len(self.combo_key_inputs):
                    self.combo_key_inputs[i].setText(key)
        self.update_preset_list()

    def set_application_icon(self):
        """设置应用程序图标"""  # inserted
        try:
            current_dir = os.path.dirname(os.path.abspath(__file__))
            if getattr(sys, 'frozen', False):
                base_path = sys._MEIPASS
            else:
                base_path = os.path.dirname(current_dir)
            icon_path = os.path.join(base_path, 'assets', 'icon.ico')
            self.logo_path = os.path.join(base_path, 'assets', 'logo.png')
            if os.path.exists(icon_path):
                self.setWindowIcon(QIcon(icon_path))
                print(f'成功加载图标: {icon_path}')
            else:
                print(f'图标文件不存在: {icon_path}')
        except Exception as e:
            print(f'设置图标失败: {str(e)}')
        finally:
            pass

    def get_dark_style(self):
        return '\n            QMainWindow {\n                background-color: #2b2b2b;\n            }\n            QWidget {\n                background-color: #2b2b2b;\n                color: #ffffff;\n            }\n            QLineEdit {\n                background-color: #3b3b3b;\n                border: 1px solid #555555;\n                border-radius: 3px;\n                padding: 5px;\n                color: #ffffff;\n            }\n            QPushButton {\n                background-color: #4a4a4a;\n                border: none;\n                border-radius: 3px;\n                padding: 5px 15px;\n                color: #ffffff;\n            }\n            QPushButton:hover {\n                background-color: #5a5a5a;\n            }\n            QPushButton:pressed {\n                background-color: #3a3a3a;\n            }\n            QFrame {\n                border: 1px solid #555555;\n                border-radius: 3px;\n            }\n            QCheckBox {\n                spacing: 5px;\n            }\n            QCheckBox::indicator {\n                width: 13px;\n                height: 13px;\n            }\n            QCheckBox::indicator:unchecked {\n                background-color: #3b3b3b;\n                border: 1px solid #555555;\n            }\n            QCheckBox::indicator:checked {\n                background-color: #4CAF50;\n                border: 1px solid #555555;\n            }\n            QRadioButton {\n                spacing: 5px;\n            }\n            QRadioButton::indicator {\n                width: 13px;\n                height: 13px;\n                border-radius: 7px;\n            }\n            QRadioButton::indicator:unchecked {\n                background-color: #3b3b3b;\n                border: 1px solid #555555;\n            }\n            QRadioButton::indicator:checked {\n                background-color: #4CAF50;\n                border: 1px solid #555555;\n            }\n            QLabel {\n                color: #ffffff;\n            }\n        '

    def clear_all(self):
        """清除所有内容"""  # inserted
        self.video_list.clear()
        self.video_list2.clear()
        self.width_input.clear()
        self.height_input.clear()
        self.bitrate_input.setText('5000')
        self.audio_input.setText('120')
        self.duration_input.setText('')
        self.path_input.clear()
        self.log_text.clear()

    def format_duration(self, seconds):
        """将秒数转换为 HH:MM:SS 格式"""  # inserted
        try:
            seconds = int(seconds)
            hours = seconds // 3600
            minutes = seconds % 3600 // 60
            secs = seconds % 60
            return f'{hours:02d}:{minutes:02d}:{secs:02d}'
        except ValueError:
            return '00:00:00'
        finally:
            pass

    def process_videos(self):
        """处理视频按钮点击事件"""  # inserted
        selected_items = self.video_list2.selectedItems()
        if not selected_items and self.video_list2.count() > 0 and (not (self.video_list2.count() == 1 and self.video_list2.item(0) == self.video_list2.placeholder)):
            first_item = self.video_list2.item(0)
            first_item.setSelected(True)
            selected_items = [first_item]
        if not selected_items:
            QMessageBox.warning(self, '提示', '请在右侧列表选择要处理的视频文件', QMessageBox.StandardButton.Ok)
            return
        if not self.duration_input.text().strip():
            QMessageBox.warning(self, '提示', '请输入伪时长')
            self.duration_input.setFocus()
            return
        try:
            seconds = self.duration_input.text().strip()
            target_duration = self.format_duration(seconds)
            for item in selected_items:
                input_path = item.toolTip()
                input_file = Path(input_path)
                output_dir = input_file.parent / '修改'
                if not input_file.exists():
                    self.print_log(f'❌ 错误: 输入文件不存在: {input_file.name}\n')
                    continue
                try:
                    import stat
                    current_permissions = input_file.stat().st_mode
                    input_file.chmod(current_permissions | stat.S_IWRITE)
                except Exception as e:
                    self.print_log(f'⚠️ 警告: 无法移除文件只读属性 {input_file.name}: {str(e)}\n')
                    pass
                else:
                    if not os.access(input_file, os.R_OK):
                        self.print_log(f'❌ 错误: 无法读取输入文件: {input_file.name}\n')
                        continue
                else:
                    try:
                        output_dir.mkdir(exist_ok=True)
                        if not os.access(output_dir, os.W_OK):
                            self.print_log(f'❌ 错误: 无法写入输出目录: {output_dir}\n')
                    except PermissionError as e:
                        self.print_log(f'❌ 权限错误: 无法访问文件 {input_file.name}\n{str(e)}\n')
                        continue
                    else:
                        output_name = f'{input_file.stem}{input_file.suffix}'
                        output_path = str(output_dir / output_name)
                        modify_video_duration(input_path, output_path, target_duration)
                        self.print_log(f'✅ {input_file.name} 处理完成')
                        self.print_log(f'   保存至: {output_path}\n')
                        row = self.video_list2.row(item)
                        if row >= 0:
                            removed_item = self.video_list2.takeItem(row)
                            del removed_item
                        if self.video_list2.count() > 0 and (not (self.video_list2.count() == 1 and self.video_list2.item(0) == self.video_list2.placeholder)):
                            next_item = self.video_list2.item(0)
                            if next_item:
                                next_item.setSelected(True)
                                QTimer.singleShot(1000, self.process_videos)
        except Exception as e:
            self.print_log(f'❌ 处理失败 {input_file.name}: {str(e)}\n')
            QMessageBox.warning(self, '错误', '请输入有效的数值')

    def show_context_menu(self, position):
        menu = QMenu(self)
        menu.setStyleSheet('\n            QMenu {\n                background-color: #2b2b2b;\n                border: 1px solid #555555;\n                padding: 5px;\n            }\n            QMenu::item {\n                color: #ffffff;\n                padding: 5px 20px;\n            }\n            QMenu::item:selected {\n                background-color: #4CAF50;\n            }\n        ')
        delete_action = menu.addAction('删除')
        clear_action = menu.addAction('清空列表')
        action = menu.exec(self.video_list2.mapToGlobal(position))
        if action == delete_action:
            for item in self.video_list2.selectedItems():
                if item!= self.video_list2.placeholder:
                    self.video_list2.takeItem(self.video_list2.row(item))
            if self.video_list2.count() == 0:
                self.video_list2.clear()
        else:
            if action == clear_action:
                self.video_list2.clear()

    def process_finished(self, success, error, file_name, temp_file):
        """时长修改完成的回调"""  # inserted
        try:
            if Path(temp_file).exists():
                pass
        except Exception as e:
            self.status_text.append(f'警告：无法删除临时文件: {str(e)}\n')
            try:
                import os
                os.system(f'del /f \"{temp_file}\"' if os.name == 'nt' else f'rm -f \"{temp_file}\"')
            except Exception as e:
                self.status_text.append(f'警告：无法强制删除临时文件: {str(e)}\n')
        else:
            try:
                import stat
                temp_file_path = Path(temp_file)
                temp_file_path.chmod(stat.S_IWRITE | stat.S_IREAD)
            except Exception as e:
                self.status_text.append(f'警告：无法修改临时文件权限: {str(e)}\n')
                pass
            else:
                try:
                    Path(temp_file).unlink()
                except PermissionError:
                    self.status_text.append(f'警告：无法删除临时文件: {str(e)}\n')
        else:
            if success:
                self.success_count += 1
                base_name = Path(file_name).stem
                self.status_text.append(f'✅ {base_name} 已处理完成\n')
            else:
                self.error_count += 1
                self.error_messages.append(f'• {file_name}: {error}')
                self.status_text.append(f'❌ {file_name} 时长修改失败: {error}\n')
            self.processed_count += 1
            self.check_all_completed()

    def check_all_completed(self):
        """检查是否所有文件都处理完成"""  # inserted
        if self.processed_count == self.total_files:
            processed_files = []
            for i in range(self.video_list2.count()):
                item = self.video_list2.item(i)
                if not item.isSelected():
                    continue
                file_path = item.toolTip()
                try:
                    import stat
                    output_file_path = Path(file_path)
                    if output_file_path.exists():
                        output_file_path.chmod(stat.S_IWRITE | stat.S_IREAD)
                except Exception as e:
                    self.status_text.append(f'警告：无法修改输出文件权限: {str(e)}\n')
                    pass
                else:
                    file_name = output_file_path.name
                    processed_files.append(file_name)
            else:
                for file_name in processed_files:
                    self.status_text.append(f'{file_name} 已处理完成')
                self.status_text.verticalScrollBar().setValue(self.status_text.verticalScrollBar().maximum())
                self.enable_process_button()

    def clear_inputs(self):
        """清除输入框的值"""  # inserted
        self.width_input.clear()
        self.height_input.clear()

    def on_selection_changed(self):
        """列表选择变化时的处理"""  # inserted
        if not self.video_list.selectedItems():
            self.clear_inputs()
            self.select_all_btn.setText('全选')
            self.stuck_duration_seconds_label.setText('秒')
            return
        all_selected = len(self.video_list.selectedItems()) == self.video_list.count()
        self.select_all_btn.setText('取消全选' if all_selected else '全选')
        self.process_selection()
        self.update_duration_label()

    def process_selection(self):
        """处理选择变化"""  # inserted
        selected_items = self.video_list.selectedItems()
        if selected_items:
            item = selected_items[0]
            file_path = item.toolTip()
            self.path_input.setText(file_path)
            try:
                cap = cv2.VideoCapture(file_path)
                if cap.isOpened():
                    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                    fps = cap.get(cv2.CAP_PROP_FPS)
                    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                    duration = int(frame_count / fps)
                    self.width_input.setText(str(width))
                    self.height_input.setText(str(height))
                    if duration > 0:
                        self.stuck_duration_input.setValidator(QIntValidator(1, duration))
                        current_value = self.stuck_duration_input.text()
                        if current_value and int(current_value) > duration:
                            self.stuck_duration_input.setText(str(duration))
                cap.release()
            except Exception as e:
                self.print_log(f'读取视频信息失败: {str(e)}')  # 添加日志记录语句以确保语法正确性
        else:
            self.path_input.clear()
            self.stuck_duration_seconds_label.setText('秒')
            QMessageBox.warning(self, '提示', f'读取视频信息失败: {str(e)}')
            return None

    def print_log(self, message):
        """打印日志信息到文本框"""  # inserted
        self.log_text.append(message)
        self.log_text.verticalScrollBar().setValue(self.log_text.verticalScrollBar().maximum())
        QApplication.processEvents()

    def select_files(self, event):
        """点击选择文件"""  # inserted
        file_dialog = QFileDialog()
        file_dialog.setFileMode(QFileDialog.FileMode.ExistingFiles)
        file_dialog.setNameFilter('视频文件 (*.mp4 *.mov)')
        file_dialog.setViewMode(QFileDialog.ViewMode.List)
        if file_dialog.exec():
            selected_files = file_dialog.selectedFiles()
            self.video_list.clearSelection()
            if self.video_list.count() == 1:
                if self.video_list.item(0) == self.video_list.placeholder:
                    self.video_list.takeItem(0)
            for file_path in selected_files:
                items = self.video_list.findItems(Path(file_path).name, Qt.MatchFlag.MatchExactly)
                if not items:
                    item = QListWidgetItem(Path(file_path).name)
                    item.setToolTip(file_path)
                    item.setData(Qt.ItemDataRole.UserRole, file_path)
                    self.video_list.addItem(item)
                items = self.video_list.findItems(Path(file_path).name, Qt.MatchFlag.MatchExactly)
                for item in items:
                    item.setSelected(True)
            if selected_files:
                if len(selected_files) == 1:
                    self.path_input.setText(selected_files[0])
                else:
                    self.path_input.setText(f'已选择 {len(selected_files)} 个文件')
                self.print_log('已添加文件：')
                for file_path in selected_files:
                    self.print_log(f'• {Path(file_path).name}')
                self.print_log('')

    def dragEnterEvent(self, event):
        """拖拽进入事件"""  # inserted
        if event.mimeData().hasUrls():
            event.accept()
        else:
            event.ignore()

    def dropEvent(self, event):
        """拖拽释放事件"""  # inserted
        files = [url.toLocalFile() for url in event.mimeData().urls()]
        valid_files = [f for f in files if Path(f).suffix.lower() in ['.mp4', '.mov']]
        if not valid_files:
            QMessageBox.warning(self, '提示', '请选择 .mp4 或 .mov 格式的视频文件', QMessageBox.StandardButton.Ok)
            return
        self.video_list.clearSelection()
        for file_path in valid_files:
            items = self.video_list.findItems(Path(file_path).name, Qt.MatchFlag.MatchExactly)
            if not items:
                item = QListWidgetItem(Path(file_path).name)
                item.setToolTip(file_path)
                item.setData(Qt.ItemDataRole.UserRole, file_path)
                self.video_list.addItem(item)
            items = self.video_list.findItems(Path(file_path).name, Qt.MatchFlag.MatchExactly)
            for item in items:
                item.setSelected(True)
        if len(valid_files) == 1:
            self.path_input.setText(valid_files[0])
        else:
            self.path_input.setText(f'已选择 {len(valid_files)} 个文件')
        self.print_log('已添加文件：')
        for file_path in valid_files:
            self.print_log(f'• {Path(file_path).name}')
        self.print_log('')

    def handle_cl(self):
        """按钮点击事件"""  # inserted
        selected_items = self.video_list.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, '提示', '请在左侧列表选择要处理的视频文件', QMessageBox.StandardButton.Ok)
            return
        if not hasattr(self, 'check_timer'):
            self.check_timer = QTimer()
            self.check_timer.timeout.connect(self.check_processing_status)
            self.check_timer.setInterval(1000)
        if not hasattr(self, 'is_one_click_processing'):
            self.is_one_click_processing = False
        self.process_btn.setEnabled(False)
        self.process_btn.setStyleSheet('\n            QPushButton {\n                background-color: #666666;\n                border: none;\n                border-radius: 3px;\n                color: #999999;\n                padding: 5px 15px;\n            }\n        ')
        if not self.bitrate_input.text().strip() or not self.audio_input.text().strip() or (not self.stuck_duration_input.text().strip()):
            QMessageBox.warning(self, '错误', '请确保比特率和音频比特率已填写！', QMessageBox.StandardButton.Ok)
            return
        try:
            bitrate = int(self.bitrate_input.text().strip())
            audio_bitrate = int(self.audio_input.text().strip())
            use_gpu = self.gpu_radio.isChecked()
            use_amd = self.amd_radio.isChecked()
            use_intel = self.intel_radio.isChecked()
            speed_mode = self.speed_radio.isChecked()
            if (use_gpu or use_amd or use_intel) and len(selected_items) >= 2:
                saturation = round(random.uniform(0.8, 1.2), 2)
                sharpness = round(random.uniform(0.8, 1.2), 2)
                self.print_log(f'使用随机参数 - 饱和度: {saturation}, 锐化值: {sharpness}')
            else:
                saturation = float(self.saturation_input.text().strip())
                sharpness = float(self.sharpness_input.text().strip())
                if not 0.8 <= saturation <= 1.2:
                    raise ValueError('饱和度必须在0.8-1.2之间，1以下为降低饱和，1以上为增加饱和')
                if not 0.8 <= sharpness <= 1.2:
                    raise ValueError('锐化值必须在0.8-1.2之间，1以下为降低锐化，1以上为增强锐化')
        except ValueError as e:
            QMessageBox.warning(self, '错误', f'请输入有效的参数值: {str(e)}', QMessageBox.StandardButton.Ok)
            return None
        new_effect = False
        self.cl_worker = ClWorker(selected_items, bitrate, audio_bitrate, saturation=saturation, sharpness=sharpness, use_gpu=use_gpu, new_effect=new_effect, use_amd=use_amd, use_intel=use_intel, speed_mode=speed_mode, stuck_duration=int(self.stuck_duration_input.text().strip()))
        self.cl_worker.progress_updated.connect(self.update_progress)
        self.cl_worker.log_message.connect(self.print_log)
        self.cl_worker.item_finished.connect(self.remove_finished_item)
        self.cl_worker.finished.connect(self.cl_process_finished)
        self.cl_btn.setEnabled(False)
        self.cl_btn.setText('处理中')
        self.cl_worker.start()

    def closeEvent(self, event):
        """关闭窗口时的处理"""  # inserted
        if hasattr(self, 'fusion_worker') and self.fusion_worker and self.fusion_worker.isRunning():
            reply = QMessageBox.question(self, '确认关闭', '有视频正在豹合中，确定要关闭吗？\n未完成的豹合视频将被删除。', QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No, QMessageBox.StandardButton.No)
            if reply == QMessageBox.StandardButton.Yes:
                self.fusion_worker.stop()
                self.fusion_worker.wait()
                try:
                    if hasattr(self.fusion_worker, 'current_output_path') and os.path.exists(self.fusion_worker.current_output_path):
                        os.remove(self.fusion_worker.current_output_path)
                        self.print_log('已删除未完成的豹合文件')
                except Exception as e:
                    pass
                else:
                    self.save_settings()
                    event.accept()
            else:
                event.ignore()



        else:
            self.save_settings()
            event.accept()
            self.print_log(f'删除未完成文件失败: {str(e)}')

    def remove_finished_item(self, item):
        """移除处理完成的项目"""  # inserted
        try:
            original_path = item.toolTip()
            input_file = Path(original_path)
            output_dir = input_file.parent / '修改'
            output_name = f'{input_file.stem}{input_file.suffix}'
            output_path = str(output_dir / output_name)
            if Path(output_path).exists():
                new_item = QListWidgetItem(output_name)
                new_item.setToolTip(output_path)
                if self.video_list2.count() == 1 and self.video_list2.item(0) == self.video_list2.placeholder:
                    self.video_list2.takeItem(0)
                self.video_list2.addItem(new_item)
                if hasattr(self, 'is_one_click_processing') and self.is_one_click_processing:
                    if hasattr(self, 'check_timer'):
                        self.check_timer.start()
                    self.print_log('一键处理：项目已添加到右侧列表，准备执行伪时长处理')
            row = self.video_list.row(item)
            if row >= 0:
                self.video_list.takeItem(row)


            if self.video_list.count() == 0:
                self.path_input.clear()
        except Exception as e:
            self.print_log(f'移除项目时出错: {str(e)}\n')
            return None

    def cl_process_finished(self):
        """处理完成的回调"""  # inserted
        try:
            if hasattr(self, 'check_timer'):
                self.check_timer.start()
            self.cl_btn.setEnabled(True)
            self.cl_btn.setText('处理')
            self.process_btn.setEnabled(True)
            self.process_btn.setStyleSheet('\n                QPushButton {\n                    background-color: #4CAF50;\n                    border: none;\n                    border-radius: 3px;\n                    color: white;\n                    padding: 5px 15px;\n                }\n                QPushButton:hover {\n                    background-color: #45a049;\n                }\n                QPushButton:pressed {\n                    background-color: #3d8b40;\n                }\n            ')
            self.progress_bar.setValue(0)
        except Exception as e:
            self.print_log(f'处理完成回调时出错: {str(e)}\n')

    def toggle_select_all(self):
        """切换左侧列表的全选/取消全选状态"""  # inserted
        if self.video_list.count() == 0 or (self.video_list.count() == 1 and self.video_list.item(0) == self.video_list.placeholder):
            return None
        all_selected = len(self.video_list.selectedItems()) == self.video_list.count()
        for i in range(self.video_list.count()):
            self.video_list.item(i).setSelected(not all_selected)
        self.select_all_btn.setText('全选' if all_selected else '取消全选')

    def toggle_select_all2(self):
        """切换右侧列表的全选/取消全选状态"""  # inserted
        if self.video_list2.count() == 0 or (self.video_list2.count() == 1 and self.video_list2.item(0) == self.video_list2.placeholder):
            return None
        all_selected = len(self.video_list2.selectedItems()) == self.video_list2.count()
        for i in range(self.video_list2.count()):
            self.video_list2.item(i).setSelected(not all_selected)
        self.select_all_btn2.setText('全选' if all_selected else '取消全选')

    def update_progress(self, progress):
        """更新进度条"""  # inserted
        self.progress_bar.setValue(progress)

    def update_one_click_button_state(self):
        """更新一键处理按钮的状态"""  # inserted
        duration = self.duration_input.text().strip()
        if duration:
            self.one_click_btn.setEnabled(True)
            self.one_click_btn.setStyleSheet('\n                QPushButton {\n                    background-color: #2196F3;\n                    border: none;\n                    border-radius: 3px;\n                    color: white;\n                    padding: 5px 15px;\n                }\n                QPushButton:hover {\n                    background-color: #1976D2;\n                }\n                QPushButton:pressed {\n                    background-color: #1565C0;\n                }\n            ')
        else:  # inserted
            self.one_click_btn.setEnabled(False)
            self.one_click_btn.setStyleSheet('\n                QPushButton {\n                    background-color: #666666;\n                    border: none;\n                    border-radius: 3px;\n                    color: #999999;\n                    padding: 5px 15px;\n                }\n            ')

    def handle_one_click_process(self):
        """一键处理按钮点击事件"""  # inserted
        selected_items = self.video_list.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, '提示', '请在左侧列表选择要处理的视频文件', QMessageBox.StandardButton.Ok)
            return
        self.is_one_click_processing = True
        self.one_click_btn.setEnabled(False)
        self.one_click_btn.setText('处理中')
        self.process_btn.setEnabled(False)
        self.process_btn.setStyleSheet('\n            QPushButton {\n                background-color: #666666;\n                border: none;\n                border-radius: 3px;\n                color: #999999;\n                padding: 5px 15px;\n            }\n        ')
        if not self.bitrate_input.text().strip() or not self.audio_input.text().strip() or (not self.stuck_duration_input.text().strip()):
            QMessageBox.warning(self, '错误', '请确保比特率和音频比特率已填写！', QMessageBox.StandardButton.Ok)
            return
        try:
            bitrate = int(self.bitrate_input.text().strip())
            audio_bitrate = int(self.audio_input.text().strip())
            use_gpu = self.gpu_radio.isChecked()
            use_amd = self.amd_radio.isChecked()
            use_intel = self.intel_radio.isChecked()
            speed_mode = self.speed_radio.isChecked()
            if (use_gpu or use_amd or use_intel) and len(self.video_list.selectedItems()) >= 2:
                saturation = round(random.uniform(0.8, 1.2), 2)
                sharpness = round(random.uniform(0.8, 1.2), 2)
                self.print_log(f'使用随机参数 - 饱和度: {saturation}, 锐化值: {sharpness}')
            else:  # inserted
                saturation = float(self.saturation_input.text().strip())
                sharpness = float(self.sharpness_input.text().strip())
                if not 0.8 <= saturation <= 1.2:
                    raise ValueError('饱和度必须在0.8-1.2之间，1以下为降低饱和，1以上为增加饱和')
                if not 0.8 <= sharpness <= 1.2:
                    raise ValueError('锐化值必须在0.8-1.2之间，1以下为降低锐化，1以上为增强锐化')
        except ValueError as e:
            pass  # postinserted
        else:  # inserted
            pass  # postinserted
        new_effect = False
        self.cl_worker = ClWorker(self.video_list.selectedItems(), bitrate, audio_bitrate, saturation=saturation, sharpness=sharpness, use_gpu=use_gpu, new_effect=new_effect, use_amd=use_amd, use_intel=use_intel, speed_mode=speed_mode, stuck_duration=int(self.stuck_duration_input.text().strip()))
        self.cl_worker.progress_updated.connect(self.update_progress)
        self.cl_worker.log_message.connect(self.print_log)
        self.cl_worker.item_finished.connect(self.remove_finished_item)
        self.cl_worker.finished.connect(self.cl_process_finished)
        self.cl_worker.start()
        self.check_timer = QTimer()
        self.check_timer.timeout.connect(self.check_processing_status)
        self.check_timer.start(1000)
            QMessageBox.warning(self, '错误', f'请输入有效的参数值: {str(e)}', QMessageBox.StandardButton.Ok)
            return None

    def check_processing_status(self):
        """检查处理状态"""  # inserted
        if not hasattr(self, 'cl_worker') or not self.cl_worker.isRunning():
            has_valid_items = self.video_list2.count() > 0 and (not (self.video_list2.count() == 1 and self.video_list2.item(0) == self.video_list2.placeholder))
            if has_valid_items:
                self.print_log(f'检测到右侧列表有有效项目，数量：{self.video_list2.count()}')
                if hasattr(self, 'is_one_click_processing') and self.is_one_click_processing:
                    self.print_log('一键处理模式：准备执行伪时长处理')
                    self.video_list2.clearSelection()
                    self.video_list2.item(0).setSelected(True)
                    try:
                        selected_items = self.video_list2.selectedItems()
                        if selected_items:
                            self.process_videos()
                            self.print_log('一键处理：伪时长处理已执行完成')
                    except Exception as e:
                        pass  # postinserted
                    else:  # inserted
                        self.is_one_click_processing = False
                else:  # inserted
                    self.print_log('非一键处理模式，跳过自动执行伪时长处理')
            self.check_timer.stop()
            self.one_click_btn.setEnabled(True)
            self.one_click_btn.setText('一键处理')
            self.update_one_click_button_state()
            self.print_log(f'伪时长处理出错：{str(e)}')

    def get_video_duration(self, file_path):
        """获取视频时长（秒）"""  # inserted
        try:
            cap = cv2.VideoCapture(file_path)
            if cap.isOpened():
                fps = cap.get(cv2.CAP_PROP_FPS)
                frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                duration = int(frame_count / fps)
                cap.release()
                return duration
        except Exception as e:
            pass  # postinserted
        else:  # inserted
            pass  # postinserted
        return 0
            self.print_log(f'获取视频时长失败: {str(e)}')
            return 0

    def update_duration_label(self):
        """更新时长标签"""  # inserted
        selected_items = self.video_list.selectedItems()
        if selected_items:
            total_duration = 0
            for item in selected_items:
                file_path = item.toolTip()
                duration = self.get_video_duration(file_path)
                total_duration += duration
            self.stuck_duration_seconds_label.setText(f'秒 (总时长: {total_duration}秒)')
            if total_duration > 0:
                self.stuck_duration_input.setValidator(QIntValidator(1, total_duration))
                current_value = self.stuck_duration_input.text()
                if current_value and int(current_value) > total_duration:
                    self.stuck_duration_input.setText(str(total_duration))
        else:  # inserted
            self.stuck_duration_seconds_label.setText('秒')
            self.stuck_duration_input.setValidator(QIntValidator(1, 999999))

    def eventFilter(self, obj, event):
        """事件过滤器，用于捕获按键事件"""  # inserted
        if event.type() == QEvent.Type.KeyPress and isinstance(obj, QLineEdit):
            key = event.key()
            modifiers = event.modifiers()
            if key == Qt.Key.Key_Backspace and (obj in self.combo_key_inputs or obj == self.final_key_input):
                obj.clear()
                self.print_log('已清除按键设置')
                return True
            if key in [Qt.Key.Key_Control, Qt.Key.Key_Shift, Qt.Key.Key_Alt, Qt.Key.Key_Meta]:
                return True
            key_text = ''
            if modifiers & Qt.KeyboardModifier.ControlModifier:
                key_text += 'ctrl+'
            if modifiers & Qt.KeyboardModifier.AltModifier:
                key_text += 'alt+'
            if modifiers & Qt.KeyboardModifier.ShiftModifier:
                key_text += 'shift+'
            if modifiers & Qt.KeyboardModifier.MetaModifier:
                key_text += 'windows+'
            if key!= Qt.Key.Key_unknown:
                main_key = QKeySequence(key).toString().lower()
                key_text += main_key
            if key_text.endswith('+'):
                return True
            key_text = key_text.lower()
            if obj in self.combo_key_inputs or obj == self.final_key_input:
                obj.setText(key_text)
                self.print_log(f'设置按键: {key_text}')
                return True
        return super().eventFilter(obj, event)

    def check_and_install_keyboard(self):
        """检查并安装keyboard库"""  # inserted
        try:
            import keyboard
            return True
        except ImportError:
            try:
                self.print_log('正在安装keyboard库...')
                import subprocess
                subprocess.check_call(['pip', 'install', 'keyboard'])
                import keyboard
                self.print_log('keyboard库安装成功')
            except Exception as e:
                pass  # postinserted
            else:  # inserted
                return True
                self.print_log(f'安装keyboard库失败: {str(e)}')
                QMessageBox.warning(self, '错误', '请手动安装keyboard库: pip install keyboard')
                return False

    def toggle_key_combination(self, checked):
        """切换按键组合功能的开启/关闭状态"""  # inserted
        try:
            if checked:
                if not self.final_key_input.text():
                    self.print_log('请至少设置一个最终按键')
                    self.activate_btn.setChecked(False)
                    return
                has_combo_key = False
                for input_field in self.combo_key_inputs:
                    if input_field.text():
                        pass  # postinserted
        except Exception as e:
                    else:  # inserted
                        has_combo_key = True
                        break
                if not has_combo_key:
                    self.print_log('请至少设置一个组合键')
                    self.activate_btn.setChecked(False)
                else:  # inserted
                    if not self.check_and_install_keyboard():
                        self.activate_btn.setChecked(False)
                    else:  # inserted
                        self.activate_btn.setText('取消')
                        self.print_log('正在启动快捷键功能...')
                        if self.start_keyboard_listener():
                            self.print_log('快捷键功能已启动')
                        else:  # inserted
                            self.activate_btn.setChecked(False)
                            self.activate_btn.setText('生效')
            else:  # inserted
                self.stop_keyboard_listener()
                self.activate_btn.setText('生效')
                self.print_log('快捷键功能已停止')
                self.print_log(f'切换按键组合功能失败: {str(e)}')
                self.activate_btn.setChecked(False)
                self.activate_btn.setText('生效')

    def start_keyboard_listener(self):
        """启动键盘监听器"""  # inserted
        try:
            import keyboard as self

            def key_callback(e):
                try:
                    if not self.activate_btn.isChecked():
                        return
                    if not e.event_type == keyboard.KEY_DOWN:
                        return
                except Exception as e:
                    pass  # postinserted
                else:  # inserted
                    has_modifiers = any([keyboard.is_pressed('ctrl'), keyboard.is_pressed('alt'), keyboard.is_pressed('shift'), keyboard.is_pressed('windows')])
                    trigger_key = self.final_key_input.text().lower()
                    current_key = e.name.lower()
                    if '+' not in trigger_key:
                        if has_modifiers:
                            return False
                    else:  # inserted
                        if current_key == trigger_key:
                            self.print_log(f'触发快捷键: {current_key}')
                            self.execute_key_sequence()
                            return True
                    else:  # inserted
                        return False
                    key_name = '+'.join(e.modifiers + [e.name]).lower()
                    if key_name == trigger_key:
                        self.print_log(f'触发快捷键: {key_name}')
                        self.execute_key_sequence()
                        return True
                else:  # inserted
                    return False
                        self.print_log(f'按键处理错误: {str(e)}')
                        return False
            self.keyboard_listener = self.on_press(key_callback)
            return True
        except Exception as e:
            self.print_log(f'启动键盘监听器失败: {str(e)}')
            return False
        else:  # inserted
            pass

    def stop_keyboard_listener(self):
        """停止键盘监听"""  # inserted
        try:
            import keyboard
            if hasattr(self, 'keyboard_listener'):
                keyboard.unhook(self.keyboard_listener)
                self.keyboard_listener = None
        except Exception as e:
            self.print_log(f'停止键盘监听器失败: {str(e)}')
            return None
        else:  # inserted
            pass

    def execute_key_sequence(self):
        """执行按键序列"""  # inserted
        try:
            import keyboard
            import time
            combo_keys = [input_field.text().lower() for input_field in self.combo_key_inputs if input_field.text()]
                if not combo_keys:
                    return
                protected_shortcuts_lower = set((s.lower() for s in self.protected_shortcuts))
                if any((key.lower() in protected_shortcuts_lower for key in combo_keys)):
                    self.print_log('组合键序列包含系统保护的快捷键，已跳过执行')
                    return
            finally:  # inserted
                self.print_log('开始执行组合键序列...')
                for key in combo_keys:
                    pass  # postinserted
                finally:  # inserted
                    try:
                        self.print_log(f'执行按键: {key}')
                        keyboard.press_and_release(key) if '+' not in key else keyboard.press_and_release(key)

    def load_settings(self):
        """加载保存的设置"""  # inserted
        try:
            settings_file = Path('settings.json')
            if settings_file.exists():
                with open(settings_file, 'r', encoding='utf-8') as f:
                    pass  # postinserted
        except Exception as e:
                    data = json.load(f)
                    self.last_keys = data.get('last_keys', {})
                    self.presets = data.get('presets', {})
                self.print_log(f'加载设置失败: {str(e)}')

    def save_settings(self):
        """保存设置"""  # inserted
        try:
            current_keys = {'final_key': self.final_key_input.text(), 'combo_keys': [input_field.text() for input_field in self.combo_key_inputs]}
        except Exception as e:
            self.print_log(f'保存设置失败: {str(e)}')

    def add_preset(self, name):
        """添加新的预设"""  # inserted
        current_keys = {'final_key': self.final_key_input.text(), 'combo_keys': [input_field.text() for input_field in self.combo_key_inputs]}
        self.presets[name] = current_keys
        self.save_settings()
        self.update_preset_list()

    def load_preset(self, name):
        """加载预设"""  # inserted
        if name in self.presets:
            preset = self.presets[name]
            final_key = preset['final_key']
            self.final_key_input.setText(final_key)
            if '+' in final_key:
                key_parts = final_key.split('+')
                self.final_key = QKeySequence('+'.join(key_parts)).toString()
            else:  # inserted
                self.final_key = QKeySequence(final_key).toString()
            self.combo_keys = []
            for i, key in enumerate(preset['combo_keys']):
                if i < len(self.combo_key_inputs):
                    combo_input = self.combo_key_inputs[i]
                    combo_input.setText(key)
                    if key:
                        if '+' in key:
                            key_parts = key.split('+')
                            self.combo_keys.append(QKeySequence('+'.join(key_parts)).toString())
                        else:  # inserted
                            self.combo_keys.append(QKeySequence(key).toString())
            self.print_log(f'已加载预设: {name}')

    def update_preset_list(self):
        """更新预设列表"""  # inserted
        self.preset_list.clear()
        for name in self.presets:
            self.preset_list.addItem(name)

    def show_add_preset_dialog(self):
        """显示添加预设对话框"""  # inserted
        name, ok = QInputDialog.getText(self, '添加预设', '请输入预设名称:')
        if ok and name:
            self.add_preset(name)

    def handle_video_merge(self):
        """视频合并功能"""  # inserted
        self.fix_missing_item_paths()
        selected_items = self.video_list.selectedItems()
        if len(selected_items) < 2:
            self.print_log('请至少选择两个视频文件进行合并')
            return
        video_paths = []
        for item in selected_items:
            path = item.data(Qt.ItemDataRole.UserRole)
            if path is None:
                self.print_log(f'警告：项目 \'{item.text()}\' 没有有效的文件路径，将被跳过')
                continue
            if not os.path.exists(path):
                self.print_log(f'警告：文件不存在：{path}，将被跳过')
                continue
            video_paths.append(path)
        if len(video_paths) < 2:
            self.print_log('合并失败：没有足够的有效视频文件（至少需要2个）')
            return
        default_filename = 'merged_video.mp4'
        output_path, _ = QFileDialog.getSaveFileName(self, '保存合并后的视频', default_filename, 'MP4 文件 (*.mp4)')
        if not output_path:
            self.print_log('未选择保存路径，取消操作')
            return
        temp_file_path = os.path.join(tempfile.gettempdir(), f'video_merge_list_{int(time.time())}.txt')
        try:
            with open(temp_file_path, 'w', encoding='utf-8') as f:
                for video_path in video_paths:
                    safe_path = video_path.replace('\\', '/')
                    f.write(f'file \'{safe_path}\'\n')
            self.print_log(f'开始合并 {len(video_paths)} 个视频文件...')
            ffmpeg_cmd = ['ffmpeg', '-y', '-f', 'concat', '-safe', '0', '-i', temp_file_path, '-c', 'copy', output_path]
            process = subprocess.Popen(ffmpeg_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, creationflags=subprocess.CREATE_NO_WINDOW)
            stdout, stderr = process.communicate()
            if process.returncode == 0:
                self.print_log(f'视频合并成功，已保存到：{output_path}')
                self.video_list.clear()
                self.print_log('已清空视频列表')
            else:  # inserted
                self.print_log(f'视频合并失败：{stderr}')
                self.print_log(f'临时文件路径: {temp_file_path}')
                if os.path.exists(temp_file_path):
                    with open(temp_file_path, 'r', encoding='utf-8') as f:
                        self.print_log(f'临时文件内容:\n{f.read()}')
                else:  # inserted
                    self.print_log('临时文件不存在，可能在创建后被删除')
        except Exception as e:
            self.print_log(f'视频合并过程中出错：{str(e)}')
            import traceback
            self.print_log(traceback.format_exc())
        finally:
            try:
                if os.path.exists(temp_file_path):
                    os.remove(temp_file_path)
            except Exception as e:
                self.print_log(f'删除临时文件时出错：{str(e)}')

    def fix_missing_item_paths(self):
        """修复列表中已存在的项目，为没有存储文件路径的项目添加路径数据"""  # inserted
        for i in range(self.video_list.count()):
            item = self.video_list.item(i)
            if hasattr(self.video_list, 'placeholder'):
                if item == self.video_list.placeholder:
                    continue
            if item.data(Qt.ItemDataRole.UserRole) is None and item.toolTip():
                item.setData(Qt.ItemDataRole.UserRole, item.toolTip())
                self.print_log(f'已为项目 \'{item.text()}\' 修复文件路径数据')
            else:  # inserted
                if item.data(Qt.ItemDataRole.UserRole) is None:
                    potential_path = os.path.join(os.getcwd(), item.text())
                    if os.path.exists(potential_path):
                        item.setData(Qt.ItemDataRole.UserRole, potential_path)
                        item.setToolTip(potential_path)
                        self.print_log(f'已为项目 \'{item.text()}\' 设置当前目录中的文件路径')
                        continue
                    for j in range(self.video_list.count()):
                        other_item = self.video_list.item(j)
                        other_path = other_item.data(Qt.ItemDataRole.UserRole)
                        if other_path and os.path.exists(other_path):
                            potential_dir = os.path.dirname(other_path)
                            potential_path = os.path.join(potential_dir, item.text())
                            if os.path.exists(potential_path):
                                item.setData(Qt.ItemDataRole.UserRole, potential_path)
                                item.setToolTip(potential_path)
                                self.print_log(f'已为项目 \'{item.text()}\' 设置目录 \'{potential_dir}\' 中的文件路径')
                                break

    def handle_video_fusion(self):
        """处理视频融合"""  # inserted
        if not self.video_list.count() or not self.video_list2.count():
            self.print_log('请先添加视频到左右列表')
            return
        if self.video_list.count()!= self.video_list2.count():
            self.print_log('左右列表视频数量必须相同')
            return
        dialog = QDialog(self)
        dialog.setWindowTitle('选择处理方式')
        dialog.setFixedSize(300, 200)
        layout = QVBoxLayout()
        mode_group = QGroupBox('选择处理方式')
        mode_layout = QVBoxLayout()
        cpu_radio = QRadioButton('使用CPU处理')
        gpu_radio = QRadioButton('使用GPU处理')
        mode_layout.addWidget(cpu_radio)
        mode_layout.addWidget(gpu_radio)
        mode_group.setLayout(mode_layout)
        quality_group = QGroupBox('选择处理模式')
        quality_layout = QVBoxLayout()
        quality_radio = QRadioButton('质量模式')
        speed_radio = QRadioButton('速度模式')
        quality_layout.addWidget(quality_radio)
        quality_layout.addWidget(speed_radio)
        quality_group.setLayout(quality_layout)
        gpu_radio.setChecked(True)
        quality_radio.setChecked(True)
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(mode_group)
        layout.addWidget(quality_group)
        layout.addWidget(button_box)
        dialog.setLayout(layout)
        if dialog.exec()!= QDialog.DialogCode.Accepted:
            return
        use_cpu = cpu_radio.isChecked()
        use_quality = quality_radio.isChecked()
        self.fusion_video_btn.setEnabled(False)
        self.clear_btn.setEnabled(False)
        self.video_list.setSelectionMode(QListWidget.SelectionMode.NoSelection)
        self.video_list2.setSelectionMode(QListWidget.SelectionMode.NoSelection)
        save_dir = os.path.join(os.path.dirname(self.video_list.item(0).data(Qt.ItemDataRole.UserRole)), '视频豹合')
        os.makedirs(save_dir, exist_ok=True)
        video_pairs = []
        for i in range(self.video_list.count()):
            video1 = self.video_list.item(i).data(Qt.ItemDataRole.UserRole)
            video2 = self.video_list2.item(i).data(Qt.ItemDataRole.UserRole)
            video_pairs.append((video1, video2))
        self.fusion_worker = VideoFusionWorker(video_pairs=video_pairs, save_dir=save_dir, use_cpu=use_cpu, use_quality=use_quality)
        self.fusion_worker.progress_updated.connect(self.update_progress)
        self.fusion_worker.log_message.connect(self.print_log)
        self.fusion_worker.process_finished.connect(self.on_fusion_finished)
        self.fusion_worker.start()

    def on_fusion_finished(self, success, error_msg, save_path):
        """处理视频豹合完成后的操作"""  # inserted
        self.fusion_video_btn.setEnabled(True)
        self.fusion_video_btn.setText('视频豹合')
        self.merge_video_btn.setEnabled(True)
        self.clear_btn.setEnabled(True)
        self.select_all_btn.setEnabled(True)
        self.select_all_btn2.setEnabled(True)
        self.one_click_btn.setEnabled(True)
        self.process_btn.setEnabled(True)
        self.video_list.setSelectionMode(QListWidget.SelectionMode.ExtendedSelection)
        self.video_list2.setSelectionMode(QListWidget.SelectionMode.ExtendedSelection)
        if success:
            if self.video_list.count() > 0 and self.video_list2.count() > 0:
                self.video_list.takeItem(0)
                self.video_list2.takeItem(0)
                self.print_log('已从列表中删除已处理的视频对')
            if self.video_list.count() > 0 and self.video_list2.count() > 0:
                self.print_log('请点击豹合按钮继续处理下一对视频')
            else:  # inserted
                self.print_log('所有视频对处理完成')
                QMessageBox.information(self, '完成', '所有视频对已处理完成')
        else:  # inserted
            self.print_log(f'视频豹合失败: {error_msg}')
            QMessageBox.warning(self, '错误', f'视频豹合失败: {error_msg}')
        self.progress_bar.setValue(100)
        QTimer.singleShot(2000, lambda: self.progress_bar.setValue(0))

    def closeEvent(self, event):
        """关闭窗口时的处理"""  # inserted
        if hasattr(self, 'fusion_worker') and self.fusion_worker and self.fusion_worker.isRunning():
            reply = QMessageBox.question(self, '确认关闭', '有视频正在豹合中，确定要关闭吗？\n未完成的豹合视频将被删除。', QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No, QMessageBox.StandardButton.No)
            if reply == QMessageBox.StandardButton.Yes:
                self.fusion_worker.stop()
                self.fusion_worker.wait()
                try:
                    if hasattr(self.fusion_worker, 'current_output_path') and os.path.exists(self.fusion_worker.current_output_path):
                        os.remove(self.fusion_worker.current_output_path)
                        self.print_log('已删除未完成的豹合文件')
                except Exception as e:
                    self.print_log(f'删除未完成文件失败: {str(e)}')
                finally:
                    self.save_settings()
                    event.accept()
            else:
                event.ignore()
        else:
            self.save_settings()
            event.accept()

    def handle_ks_fusion(self):
        """处理KS豹合"""  # inserted
        if not self.video_list.count() or not self.video_list2.count():
            self.print_log('请先添加视频到左右列表')
            return
        if self.video_list.count()!= self.video_list2.count():
            self.print_log('左右列表视频数量必须相同')
            return
        dialog = QDialog(self)
        dialog.setWindowTitle('选择处理方式')
        dialog.setFixedSize(300, 200)
        layout = QVBoxLayout()
        mode_group = QGroupBox('选择处理方式')
        mode_layout = QVBoxLayout()
        cpu_radio = QRadioButton('使用CPU处理')
        gpu_radio = QRadioButton('使用GPU处理')
        mode_layout.addWidget(cpu_radio)
        mode_layout.addWidget(gpu_radio)
        mode_group.setLayout(mode_layout)
        quality_group = QGroupBox('选择处理模式')
        quality_layout = QVBoxLayout()
        quality_radio = QRadioButton('质量模式')
        speed_radio = QRadioButton('速度模式')
        quality_layout.addWidget(quality_radio)
        quality_layout.addWidget(speed_radio)
        quality_group.setLayout(quality_layout)
        gpu_radio.setChecked(True)
        quality_radio.setChecked(True)
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(mode_group)
        layout.addWidget(quality_group)
        layout.addWidget(button_box)
        dialog.setLayout(layout)
        if dialog.exec()!= QDialog.DialogCode.Accepted:
            return
        use_cpu = cpu_radio.isChecked()
        use_quality = quality_radio.isChecked()
        self.ks_fusion_btn.setEnabled(False)
        self.clear_btn.setEnabled(False)
        self.video_list.setSelectionMode(QListWidget.SelectionMode.NoSelection)
        self.video_list2.setSelectionMode(QListWidget.SelectionMode.NoSelection)
        save_dir = os.path.join(os.path.dirname(self.video_list.item(0).data(Qt.ItemDataRole.UserRole)), 'KS豹合')
        os.makedirs(save_dir, exist_ok=True)
        video_pairs = []
        for i in range(self.video_list.count()):
            video1 = self.video_list.item(i).data(Qt.ItemDataRole.UserRole)
            video2 = self.video_list2.item(i).data(Qt.ItemDataRole.UserRole)
            video_pairs.append((video1, video2))
        self.ks_fusion_worker = KSFusionWorker(video_pairs=video_pairs, save_dir=save_dir, use_cpu=use_cpu, use_quality=use_quality)
        self.ks_fusion_worker.progress_updated.connect(self.update_progress)
        self.ks_fusion_worker.log_message.connect(self.print_log)
        self.ks_fusion_worker.process_finished.connect(self.on_ks_fusion_finished)
        self.ks_fusion_worker.start()

    def on_ks_fusion_finished(self, success, error_msg, save_path):
        """处理KS豹合完成后的操作"""  # inserted
        self.ks_fusion_btn.setEnabled(True)
        self.ks_fusion_btn.setText('KS豹合')
        self.merge_video_btn.setEnabled(True)
        self.clear_btn.setEnabled(True)
        self.select_all_btn.setEnabled(True)
        self.select_all_btn2.setEnabled(True)
        self.one_click_btn.setEnabled(True)
        self.process_btn.setEnabled(True)
        self.video_list.setSelectionMode(QListWidget.SelectionMode.ExtendedSelection)
        self.video_list2.setSelectionMode(QListWidget.SelectionMode.ExtendedSelection)
        if success:
            if self.video_list.count() > 0 and self.video_list2.count() > 0:
                self.video_list.takeItem(0)
                self.video_list2.takeItem(0)
                self.print_log('已从列表中删除已处理的视频对')
            if self.video_list.count() > 0 and self.video_list2.count() > 0:
                self.print_log('请点击豹合按钮继续处理下一对视频')
            else:  # inserted
                self.print_log('所有视频对处理完成')
                QMessageBox.information(self, '完成', '所有视频对已处理完成')
        else:  # inserted
            self.print_log(f'KS豹合失败: {error_msg}')
            QMessageBox.warning(self, '错误', f'KS豹合失败: {error_msg}')
        self.progress_bar.setValue(100)
        QTimer.singleShot(2000, lambda: self.progress_bar.setValue(0))

    def delete_selected_items(self):
        """删除左侧列表中选中的项目"""  # inserted
        selected_items = self.video_list.selectedItems()
        if not selected_items:
            return
        for item in reversed(selected_items):
            self.video_list.takeItem(self.video_list.row(item))
        self.on_selection_changed()

    def delete_selected_items2(self):
        """删除右侧列表中选中的项目"""  # inserted
        selected_items = self.video_list2.selectedItems()
        if not selected_items:
            return
        for item in reversed(selected_items):
            self.video_list2.takeItem(self.video_list2.row(item))
        if hasattr(self, 'select_all_btn2'):
            if self.video_list2.count() == 0:
                self.select_all_btn2.setText('全选')
            else:  # inserted
                all_selected = len(self.video_list2.selectedItems()) == self.video_list2.count()
                self.select_all_btn2.setText('取消全选' if all_selected else '全选')

class VideoFusionWorker(QThread):
    """视频豹合工作线程"""
    progress_updated = pyqtSignal(int)
    log_message = pyqtSignal(str)
    process_finished = pyqtSignal(bool, str, str)

    def __init__(self, video_pairs, save_dir, use_cpu=False, use_quality=True):
        super().__init__()
        self.video_pairs = video_pairs
        self.save_dir = save_dir
        self.process = None
        self.stopped = False
        self.video_duration = 0
        self.use_cuda = not use_cpu and self.check_cuda_support()
        self.current_pair_index = 0
        self.total_pairs = len(video_pairs)
        self.current_output_path = None
        self.use_quality = use_quality

    def check_cuda_support(self):
        """检查系统是否支持CUDA"""  # inserted
        try:
            result = subprocess.run(['ffmpeg', '-hwaccels'], capture_output=True, text=True)
            if 'cuda' in result.stdout.lower():
                self.log_message.emit('检测到CUDA支持，将使用硬件加速')
                return True
            self.log_message.emit('未检测到CUDA支持，将使用软件编码')
            return False
        except Exception as e:
            self.log_message.emit(f'CUDA检测失败: {str(e)}，将使用软件编码')
            return False

    def run(self):
        try:
            for i, (video1_path, video2_path) in enumerate(self.video_pairs):
                if self.stopped:
                    break
                self.current_pair_index = i
                self.log_message.emit(f'开始处理第 {i + 1}/{self.total_pairs} 对视频')
                left_video_name = os.path.splitext(os.path.basename(video1_path))[0]
                base_name = f'{left_video_name}_豹合'
                output_name = f'{base_name}.mp4'
                output_path = os.path.join(self.save_dir, output_name)
                counter = 1
                while os.path.exists(output_path):
                    output_name = f'{base_name}_{counter}.mp4'
                    output_path = os.path.join(self.save_dir, output_name)
                    counter += 1
                self.current_output_path = output_path
                probe_cmd = ['ffprobe', '-v', 'error', '-select_streams', 'v:0', '-show_entries', 'stream=duration', '-of', 'json', str(video1_path)]
                if sys.platform.startswith('win'):
                    startupinfo = subprocess.STARTUPINFO()
                    startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                    startupinfo.wShowWindow = subprocess.SW_HIDE
                    probe_result = subprocess.run(probe_cmd, capture_output=True, text=True, encoding='utf-8', errors='replace', startupinfo=startupinfo)
                else:  # inserted
                    probe_result = subprocess.run(probe_cmd, capture_output=True, text=True, encoding='utf-8', errors='replace')
                video_info = json.loads(probe_result.stdout)
                if 'streams' not in video_info or not video_info['streams']:
                    self.log_message.emit('错误：无法获取第一个视频的视频流信息')
                    self.process_finished.emit(False, '无法获取第一个视频的视频流信息', '')
                    continue
        except Exception as e:
            else:  # inserted
                try:
                    duration = float(video_info['streams'][0]['duration'])
                    self.log_message.emit(f'视频时长: {duration:.2f} 秒')
                except (KeyError, ValueError, TypeError):
                    pass  # postinserted
                else:  # inserted
                    self.video_duration = duration
                    self.log_message.emit(f'根据第一个视频时长（{duration:.2f}秒）显示豹合进度')
                    probe_cmd = ['ffprobe', '-v', 'error', '-select_streams', 'v:0', '-show_entries', 'stream=width,height', '-of', 'json', str(video1_path)]
                    if sys.platform.startswith('win'):
                        startupinfo = subprocess.STARTUPINFO()
                        startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                        startupinfo.wShowWindow = subprocess.SW_HIDE
                        probe_result = subprocess.run(probe_cmd, capture_output=True, text=True, encoding='utf-8', errors='replace', startupinfo=startupinfo)
                    else:  # inserted
                        probe_result = subprocess.run(probe_cmd, capture_output=True, text=True, encoding='utf-8', errors='replace')
                    video_info = json.loads(probe_result.stdout)
                    width = int(video_info['streams'][0]['width'])
                    height = int(video_info['streams'][0]['height'])
                    self.log_message.emit(f'左边视频尺寸: {width}x{height}')
                    ffmpeg_cmd = ['ffmpeg', '-hwaccel', 'cuda' if self.use_cuda else 'none', '-i', video1_path, '-i', video2_path, '-map', '0:v', '-map', '1:v', '-map', '0:a', '-map', '1:a', '-s', f'{width}x{height}', '-map_metadata', '-1']
                    if self.use_cuda:
                        if self.use_quality:
                            ffmpeg_cmd.extend(['-c:v:0', 'h264_nvenc', '-b:v:0', '4000k', '-maxrate:v:0', '4000k', '-bufsize:v:0', '4000k', '-preset', 'p4', '-c:v:1', 'h264_nvenc', '-b:v:1', '12000k', '-maxrate:v:1', '12000k', '-bufsize:v:1', '12000k', '-preset', 'p4'])
                        else:  # inserted
                            ffmpeg_cmd.extend(['-c:v:0', 'h264_nvenc', '-b:v:0', '2000k', '-maxrate:v:0', '2000k', '-bufsize:v:0', '2000k', '-preset', 'p4', '-c:v:1', 'h264_nvenc', '-b:v:1', '6000k', '-maxrate:v:1', '6000k', '-bufsize:v:1', '6000k', '-preset', 'p4'])
                    else:  # inserted
                        if self.use_quality:
                            ffmpeg_cmd.extend(['-c:v:0', 'libx264', '-b:v:0', '4000k', '-maxrate:v:0', '4000k', '-bufsize:v:0', '4000k', '-x264opts', 'nal-hrd=cbr', '-c:v:1', 'libx264', '-b:v:1', '12000k', '-maxrate:v:1', '12000k', '-bufsize:v:1', '12000k', '-x264opts', 'nal-hrd=cbr'])
                        else:  # inserted
                            ffmpeg_cmd.extend(['-c:v:0', 'libx264', '-b:v:0', '2000k', '-maxrate:v:0', '2000k', '-bufsize:v:0', '2000k', '-x264opts', 'nal-hrd=cbr', '-c:v:1', 'libx264', '-b:v:1', '6000k', '-maxrate:v:1', '6000k', '-bufsize:v:1', '6000k', '-x264opts', 'nal-hrd=cbr'])
                    ffmpeg_cmd.extend(['-c:a:0', 'aac', '-b:a:0', '280k', '-c:a:1', 'aac', '-b:a:1', '180k', '-strict', 'experimental', '-f', 'mp4', '-y', self.current_output_path])
                    if sys.platform.startswith('win'):
                        startupinfo = subprocess.STARTUPINFO()
                        startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                        startupinfo.wShowWindow = subprocess.SW_HIDE
                        self.process = subprocess.Popen(ffmpeg_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True, encoding='utf-8', errors='replace', startupinfo=startupinfo)
                    else:  # inserted
                        self.process = subprocess.Popen(ffmpeg_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True, encoding='utf-8', errors='replace')
                    start_time = time.time()
                    last_progress = 0
                    last_update_time = start_time
                    last_log_progress = 0
                    while True:
                        if self.stopped:
                            self.process.terminate()
                            break
                        line = self.process.stderr.readline()
                        if not line and self.process.poll() is not None:
                            break
                        time_match = re.search('time=(\\d+):(\\d+):(\\d+)\\.(\\d+)', line)
                        if time_match:
                            hours, minutes, seconds, centiseconds = map(int, time_match.groups())
                            current_time = hours * 3600 + minutes * 60 + seconds + centiseconds / 100
                            progress = min(99, max(0, int(current_time / self.video_duration * 100)))
                            current_time_stamp = time.time()
                            if progress > last_progress and (current_time_stamp - last_update_time >= 0.5 or progress - last_progress >= 5):
                                last_progress = progress
                                last_update_time = current_time_stamp
                                self.progress_updated.emit(progress)
                                if progress >= last_log_progress + 10:
                                    last_log_progress = progress - progress % 10
                                    elapsed = current_time_stamp - start_time
                                    if progress > 0:
                                        estimated_total = elapsed / progress * 100
                                        remaining = max(0, estimated_total - elapsed)
                                        self.log_message.emit(f'豹合进度: {progress}% (处理到: {int(current_time)}秒/{self.video_duration:.1f}秒, 预计剩余: {int(remaining)}秒)')
                        continue
                    stdout, stderr = self.process.communicate()
                    if self.process.returncode == 0:
                        self.log_message.emit(f'第 {i + 1}/{self.total_pairs} 对视频豹合处理完成')
                    else:  # inserted
                        try:
                            probe_cmd = ['ffprobe', '-v', 'error', '-select_streams', 'v:0', '-show_entries', 'stream=duration', '-of', 'json', str(video2_path)]
                            if sys.platform.startswith('win'):
                                startupinfo = subprocess.STARTUPINFO()
                                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                                startupinfo.wShowWindow = subprocess.SW_HIDE
                                probe_result = subprocess.run(probe_cmd, capture_output=True, text=True, encoding='utf-8', errors='replace', startupinfo=startupinfo)
                            else:  # inserted
                                probe_result = subprocess.run(probe_cmd, capture_output=True, text=True, encoding='utf-8', errors='replace')
                            video_info = json.loads(probe_result.stdout)
                            if 'streams' in video_info and video_info['streams']:
                                right_duration = float(video_info['streams'][0]['duration'])
                                self.log_message.emit(f'右边视频时长: {right_duration:.2f} 秒')
                                hours = int(right_duration // 3600)
                                minutes = int(right_duration % 3600 // 60)
                                seconds = int(right_duration % 60)
                                duration_str = f'{hours:02d}:{minutes:02d}:{seconds:02d}'
                                from ffm_wsc import modify_video_duration
                                temp_output = self.current_output_path + '.temp'
                                modify_video_duration(self.current_output_path, temp_output, duration_str)
                                os.remove(self.current_output_path)
                                os.rename(temp_output, self.current_output_path)
                                self.log_message.emit(f'✅ 已修改豹合后视频时长为右边视频时长: {duration_str}')
                        finally:  # inserted
                            self.log_message.emit(f'视频已保存至: {self.current_output_path}')
                            self.process_finished.emit(True, '', self.current_output_path)
                    else:  # inserted
                        error_msg = f'第 {i + 1}/{self.total_pairs} 对视频豹合失败: {stderr}'
                        self.log_message.emit(error_msg)
                        self.process_finished.emit(False, error_msg, self.current_output_path)
            self.log_message.emit('所有视频对处理完成')
            self.progress_updated.emit(100)
                duration = 60.0
                self.log_message.emit(f'无法获取视频时长，使用默认值: {duration} 秒')
                self.log_message.emit(f'修改视频时长时出错: {str(e)}')
                error_msg = f'视频豹合过程中出错: {str(e)}'
                self.log_message.emit(error_msg)
                self.process_finished.emit(False, error_msg, '')

    def stop(self):
        """停止处理"""  # inserted
        self.stopped = True
        if self.process:
            self.process.terminate()

class KSFusionWorker(QThread):
    """KS豹合工作线程"""
    progress_updated = pyqtSignal(int)
    log_message = pyqtSignal(str)
    process_finished = pyqtSignal(bool, str, str)

    def __init__(self, video_pairs, save_dir, use_cpu=False, use_quality=True):
        super().__init__()
        self.video_pairs = video_pairs
        self.save_dir = save_dir
        self.process = None
        self.stopped = False
        self.video_duration = 0
        self.use_cuda = not use_cpu and self.check_cuda_support()
        self.current_pair_index = 0
        self.total_pairs = len(video_pairs)
        self.current_output_path = None
        self.use_quality = use_quality

    def check_cuda_support(self):
        """检查系统是否支持CUDA"""  # inserted
        try:
            result = subprocess.run(['ffmpeg', '-hwaccels'], capture_output=True, text=True)
            if 'cuda' in result.stdout.lower():
                self.log_message.emit('检测到CUDA支持，将使用硬件加速')
                return True
            self.log_message.emit('未检测到CUDA支持，将使用软件编码')
            return False
        except Exception as e:
            self.log_message.emit(f'CUDA检测失败: {str(e)}，将使用软件编码')
            return False

    def run(self):
        try:
            for i, (video1_path, video2_path) in enumerate(self.video_pairs):
                if self.stopped:
                    return
                self.current_pair_index = i
                self.log_message.emit(f'开始处理第 {i + 1}/{self.total_pairs} 对视频')
                left_video_name = os.path.splitext(os.path.basename(video1_path))[0]
                base_name = f'{left_video_name}_KS豹合'
                output_name = f'{base_name}.mp4'
                output_path = os.path.join(self.save_dir, output_name)
                counter = 1
                while os.path.exists(output_path):
                    output_name = f'{base_name}_{counter}.mp4'
                    output_path = os.path.join(self.save_dir, output_name)
                    counter += 1
        except Exception as e:
            else:  # inserted
                self.current_output_path = output_path
                probe_cmd = ['ffprobe', '-v', 'error', '-select_streams', 'v:0', '-show_entries', 'stream=duration', '-of', 'json', str(video1_path)]
                if sys.platform.startswith('win'):
                    startupinfo = subprocess.STARTUPINFO()
                    startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                    startupinfo.wShowWindow = subprocess.SW_HIDE
                    probe_result = subprocess.run(probe_cmd, capture_output=True, text=True, encoding='utf-8', errors='replace', startupinfo=startupinfo)
                else:  # inserted
                    probe_result = subprocess.run(probe_cmd, capture_output=True, text=True, encoding='utf-8', errors='replace')
                video_info = json.loads(probe_result.stdout)
                if 'streams' not in video_info or not video_info['streams']:
                    self.log_message.emit('错误：无法获取第一个视频的视频流信息')
                    self.process_finished.emit(False, '无法获取第一个视频的视频流信息', '')
                    continue
            else:  # inserted
                try:
                    duration = float(video_info['streams'][0]['duration'])
                    self.log_message.emit(f'视频时长: {duration:.2f} 秒')
                except (KeyError, ValueError, TypeError):
                    pass  # postinserted
                else:  # inserted
                    self.video_duration = duration
                    self.log_message.emit(f'根据第一个视频时长（{duration:.2f}秒）显示豹合进度')
                    probe_cmd = ['ffprobe', '-v', 'error', '-select_streams', 'v:0', '-show_entries', 'stream=width,height', '-of', 'json', str(video1_path)]
                    if sys.platform.startswith('win'):
                        startupinfo = subprocess.STARTUPINFO()
                        startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                        startupinfo.wShowWindow = subprocess.SW_HIDE
                        probe_result = subprocess.run(probe_cmd, capture_output=True, text=True, encoding='utf-8', errors='replace', startupinfo=startupinfo)
                    else:  # inserted
                        probe_result = subprocess.run(probe_cmd, capture_output=True, text=True, encoding='utf-8', errors='replace')
                    video_info = json.loads(probe_result.stdout)
                    width = int(video_info['streams'][0]['width'])
                    height = int(video_info['streams'][0]['height'])
                    self.log_message.emit(f'左边视频尺寸: {width}x{height}')
                    ffmpeg_cmd = ['ffmpeg', '-hwaccel', 'cuda' if self.use_cuda else 'none', '-i', video1_path, '-i', video2_path, '-map', '1:v', '-map', '0:v', '-map', '0:a', '-map', '1:a', '-s', f'{width}x{height}', '-map_metadata', '-1']
                    if self.use_cuda:
                        if self.use_quality:
                            ffmpeg_cmd.extend(['-c:v:0', 'h264_nvenc', '-b:v:0', '4000k', '-maxrate:v:0', '4000k', '-bufsize:v:0', '4000k', '-preset', 'p4', '-c:v:1', 'h264_nvenc', '-b:v:1', '12000k', '-maxrate:v:1', '12000k', '-bufsize:v:1', '12000k', '-preset', 'p4'])
                        else:  # inserted
                            ffmpeg_cmd.extend(['-c:v:0', 'h264_nvenc', '-b:v:0', '2000k', '-maxrate:v:0', '2000k', '-bufsize:v:0', '2000k', '-preset', 'p4', '-c:v:1', 'h264_nvenc', '-b:v:1', '6000k', '-maxrate:v:1', '6000k', '-bufsize:v:1', '6000k', '-preset', 'p4'])
                    else:  # inserted
                        if self.use_quality:
                            ffmpeg_cmd.extend(['-c:v:0', 'libx264', '-b:v:0', '4000k', '-maxrate:v:0', '4000k', '-bufsize:v:0', '4000k', '-x264opts', 'nal-hrd=cbr', '-c:v:1', 'libx264', '-b:v:1', '12000k', '-maxrate:v:1', '12000k', '-bufsize:v:1', '12000k', '-x264opts', 'nal-hrd=cbr'])
                        else:  # inserted
                            ffmpeg_cmd.extend(['-c:v:0', 'libx264', '-b:v:0', '2000k', '-maxrate:v:0', '2000k', '-bufsize:v:0', '2000k', '-x264opts', 'nal-hrd=cbr', '-c:v:1', 'libx264', '-b:v:1', '6000k', '-maxrate:v:1', '6000k', '-bufsize:v:1', '6000k', '-x264opts', 'nal-hrd=cbr'])
                    ffmpeg_cmd.extend(['-c:a:0', 'aac', '-b:a:0', '280k', '-c:a:1', 'aac', '-b:a:1', '180k', '-strict', 'experimental', '-f', 'mp4', '-y', self.current_output_path])
                    if sys.platform.startswith('win'):
                        startupinfo = subprocess.STARTUPINFO()
                        startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                        startupinfo.wShowWindow = subprocess.SW_HIDE
                        self.process = subprocess.Popen(ffmpeg_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True, encoding='utf-8', errors='replace', startupinfo=startupinfo)
                    else:  # inserted
                        self.process = subprocess.Popen(ffmpeg_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True, encoding='utf-8', errors='replace')
                    start_time = time.time()
                    last_progress = 0
                    last_update_time = start_time
                    last_log_progress = 0
                    while True:
                        if self.stopped:
                            self.process.terminate()
                            break
                        line = self.process.stderr.readline()
                        if not line and self.process.poll() is not None:
                            break
                        time_match = re.search('time=(\\d+):(\\d+):(\\d+)\\.(\\d+)', line)
                        if time_match:
                            hours, minutes, seconds, centiseconds = map(int, time_match.groups())
                            current_time = hours * 3600 + minutes * 60 + seconds + centiseconds / 100
                            progress = min(99, max(0, int(current_time / self.video_duration * 100)))
                            current_time_stamp = time.time()
                            if progress > last_progress and (current_time_stamp - last_update_time >= 0.5 or progress - last_progress >= 5):
                                last_progress = progress
                                last_update_time = current_time_stamp
                                self.progress_updated.emit(progress)
                                if progress >= last_log_progress + 10:
                                    last_log_progress = progress - progress % 10
                                    elapsed = current_time_stamp - start_time
                                    if progress > 0:
                                        estimated_total = elapsed / progress * 100
                                        remaining = max(0, estimated_total - elapsed)
                                        self.log_message.emit(f'KS豹合进度: {progress}% (处理到: {int(current_time)}秒/{self.video_duration:.1f}秒, 预计剩余: {int(remaining)}秒)')
                        continue
                    stdout, stderr = self.process.communicate()
                    self.progress_updated.emit(100)
                    total_time = time.time() - start_time
                    self.log_message.emit(f'视频处理完成，总耗时: {int(total_time)}秒')
                    if self.process.returncode == 0:
                        self.log_message.emit('KS豹合处理完成')
                        self.log_message.emit(f'视频已保存至: {self.current_output_path}')
                        self.process_finished.emit(True, '', self.current_output_path)
                    else:  # inserted
                        error_msg = f'KS豹合失败，错误代码：{self.process.returncode}\n'
                        if stderr:
                            error_msg += f'错误信息：{stderr}'
                        self.log_message.emit(error_msg)
                        self.process_finished.emit(False, error_msg, '')
                duration = 60.0
                self.log_message.emit(f'无法获取视频时长，使用默认值: {duration} 秒')
                error_msg = f'KS豹合过程中出错: {str(e)}'
                self.log_message.emit(error_msg)
                self.process_finished.emit(False, error_msg, '')

    def stop(self):
        """停止处理"""  # inserted
        self.stopped = True
        if self.process:
            self.process.terminate()

def main():
    app = QApplication(sys.argv)
    window = VideoProcessor()
    window.show()
    sys.exit(app.exec())
if __name__ == '__main__':
    main()