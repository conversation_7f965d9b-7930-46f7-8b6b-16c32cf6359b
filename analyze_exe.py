import pefile
import sys

def analyze_exe(file_path):
    try:
        pe = pefile.PE(file_path)
        
        # Search for common packer signatures in string table
        packers = {
            'PyInstaller': False,
            'cx_Freeze': False,
            'unfreezecx': False
        }
        
        for section in pe.sections:
            data = section.get_data()
            strings = data.decode('utf-8', errors='ignore')
            
            if 'PyInstaller' in strings:
                packers['PyInstaller'] = True
            if 'cx_Freeze' in strings:
                packers['cx_Freeze'] = True
            if 'unfreezecx' in strings:
                packers['unfreezecx'] = True
        
        print(f"Analysis of {file_path}:")
        for packer, found in packers.items():
            print(f"{packer}: {'Yes' if found else 'No'}")
            
        if not any(packers.values()):
            print("No known Python packer identified. May be custom packed.")
            
    except Exception as e:
        print(f"Error analyzing file: {e}")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python analyze_exe.py <path_to_exe>")
        sys.exit(1)
    
    analyze_exe(sys.argv[1])
