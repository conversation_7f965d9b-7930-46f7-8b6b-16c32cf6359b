# -*- coding: utf-8 -*-
import sys
import os
import struct
import shutil
import cv2

def find_moov_atom(file):
    while True:
        size_data = file.read(4)
        if not size_data or len(size_data) < 4:
            return None

        size = struct.unpack('>I', size_data)[0]
        type_data = file.read(4)
        if not type_data:
            return None

        if type_data == b'moov':
            return file.tell() - 8, size

        if size > 8:
            file.seek(size - 8, 1)

def modify_video_duration(input_file, output_file, duration_str):
    try:
        # 将时长字符串转换为秒
        parts = duration_str.split(':')
        if len(parts) == 3:
            hours, minutes, seconds = map(int, parts)
            total_seconds = hours * 3600 + minutes * 60 + seconds
        elif len(parts) == 2:
            minutes, seconds = map(int, parts)
            total_seconds = minutes * 60 + seconds
        else:
            raise ValueError("时长格式不正确，应为 HH:MM:SS 或 MM:SS")

        # 复制输入文件到输出文件
        shutil.copy2(input_file, output_file)

        # 打开输出文件进行修改
        with open(output_file, 'r+b') as f:
            # 查找 moov atom
            moov_pos = find_moov_atom(f)
            if not moov_pos:
                raise Exception("无法找到 moov atom")

            pos, size = moov_pos
            print(f"找到 moov atom 在位置 {pos}，大小为 {size}")

            # 修改 mvhd 时长
            f.seek(pos + 8)  # 跳过 moov header
            while True:
                atom_size = struct.unpack('>I', f.read(4))[0]
                atom_type = f.read(4)

                if atom_type == b'mvhd':
                    print("找到 mvhd atom")
                    version = struct.unpack('B', f.read(1))[0]
                    f.seek(3, 1)  # 跳过 flags

                    if version == 1:
                        f.seek(16, 1)  # 跳过 creation_time 和 modification_time
                        time_scale = struct.unpack('>I', f.read(4))[0]
                        duration = total_seconds * time_scale
                        f.write(struct.pack('>Q', duration))
                    else:
                        f.seek(8, 1)  # 跳过 creation_time 和 modification_time
                        time_scale = struct.unpack('>I', f.read(4))[0]
                        duration = total_seconds * time_scale
                        f.write(struct.pack('>I', duration))

                    break

                if atom_size <= 0:
                    break

                f.seek(atom_size - 8, 1)  # 跳到下一个 atom

    except Exception as e:
        print(f"发生错误: {e}")
        # 清理可能存在的输出文件
        if os.path.exists(output_file):
            os.remove(output_file)
        sys.exit(1)

def get_video_size(file_path):
    cap = cv2.VideoCapture(file_path)
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    cap.release()
    return width, height
