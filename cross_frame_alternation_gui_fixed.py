import os
import sys
import subprocess
import threading
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QPushButton, QLabel, QFileDialog, QProgressBar, QComboBox,
                            QCheckBox, QGroupBox, QRadioButton, QButtonGroup, QTextEdit,
                            QMessageBox, QSpacerItem, QSizePolicy)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QSize
from PyQt5.QtGui import QIcon, QFont
import cross_frame_alternation_fast as cross_frame_alternation

class FFmpegWorker(QThread):
    """处理FFmpeg命令的工作线程"""
    progress_update = pyqtSignal(str)
    process_finished = pyqtSignal(bool, str, str)  # 添加临时目录路径参数

    def __init__(self, main_video, aux_video, output_file, ffmpeg_path, speed_priority=False):
        super().__init__()
        self.main_video = main_video
        self.aux_video = aux_video
        self.output_file = output_file
        self.ffmpeg_path = ffmpeg_path
        self.speed_priority = speed_priority
        self.temp_dir = None

    def run(self):
        try:
            # 重定向标准输出和标准错误
            original_stdout = sys.stdout
            original_stderr = sys.stderr

            class ThreadSafeWriter:
                def __init__(self, signal):
                    self.signal = signal
                    self.buffer = ""

                def write(self, text):
                    self.buffer += text
                    if '\n' in self.buffer:
                        lines = self.buffer.split('\n')
                        for line in lines[:-1]:
                            if line.strip():
                                self.signal.emit(line.strip())
                        self.buffer = lines[-1]

                def flush(self):
                    if self.buffer:
                        self.signal.emit(self.buffer)
                        self.buffer = ""

            # 替换标准输出和标准错误
            sys.stdout = ThreadSafeWriter(self.progress_update)
            sys.stderr = ThreadSafeWriter(self.progress_update)

            try:
                # 执行视频处理
                success, error, temp_dir = cross_frame_alternation.create_cross_alternating_video_fast(
                    self.main_video,
                    self.aux_video,
                    self.output_file,
                    self.ffmpeg_path,
                    self.speed_priority
                )

                # 保存临时目录路径
                self.temp_dir = temp_dir

                # 发送处理结果
                self.process_finished.emit(success, error, temp_dir)
            finally:
                # 恢复标准输出和标准错误
                sys.stdout = original_stdout
                sys.stderr = original_stderr
        except Exception as e:
            self.process_finished.emit(False, str(e), "")

class CrossFrameAlternationGUI(QMainWindow):
    """视频帧交叉替换工具GUI界面"""

    def __init__(self):
        super().__init__()
        self.init_ui()
        self.ffmpeg_path = self.find_ffmpeg()
        # 添加临时目录列表，用于在程序关闭时清理
        self.temp_dirs = []

    def init_ui(self):
        """初始化UI界面"""
        self.setWindowTitle("视频帧交叉替换工具 - 优化版")
        self.setMinimumSize(800, 700)

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)

        # 创建文件选择区域
        file_group = QGroupBox("文件选择")
        file_layout = QVBoxLayout(file_group)

        # 主视频选择
        main_video_layout = QHBoxLayout()
        main_video_label = QLabel("主视频:")
        self.main_video_path = QLabel("未选择")
        self.main_video_path.setStyleSheet("background-color: #f0f0f0; padding: 5px; border-radius: 3px;")
        main_video_btn = QPushButton("浏览...")
        main_video_btn.clicked.connect(self.select_main_video)
        main_video_layout.addWidget(main_video_label)
        main_video_layout.addWidget(self.main_video_path, 1)
        main_video_layout.addWidget(main_video_btn)
        file_layout.addLayout(main_video_layout)

        # 辅助视频选择
        aux_video_layout = QHBoxLayout()
        aux_video_label = QLabel("辅助视频:")
        self.aux_video_path = QLabel("未选择")
        self.aux_video_path.setStyleSheet("background-color: #f0f0f0; padding: 5px; border-radius: 3px;")
        aux_video_btn = QPushButton("浏览...")
        aux_video_btn.clicked.connect(self.select_aux_video)
        aux_video_layout.addWidget(aux_video_label)
        aux_video_layout.addWidget(self.aux_video_path, 1)
        aux_video_layout.addWidget(aux_video_btn)
        file_layout.addLayout(aux_video_layout)

        # 输出文件选择
        output_layout = QHBoxLayout()
        output_label = QLabel("输出文件:")
        self.output_path = QLabel("未选择")
        self.output_path.setStyleSheet("background-color: #f0f0f0; padding: 5px; border-radius: 3px;")
        output_btn = QPushButton("浏览...")
        output_btn.clicked.connect(self.select_output_file)
        output_layout.addWidget(output_label)
        output_layout.addWidget(self.output_path, 1)
        output_layout.addWidget(output_btn)
        file_layout.addLayout(output_layout)

        main_layout.addWidget(file_group)
        
        # 创建选项区域
        options_group = QGroupBox("处理选项")
        options_layout = QVBoxLayout(options_group)
        
        # 速度优先选项
        speed_layout = QHBoxLayout()
        self.speed_priority_check = QCheckBox("速度优先")
        self.speed_priority_check.setToolTip("选中此项将优先考虑处理速度，可能会略微降低视频质量")
        speed_layout.addWidget(self.speed_priority_check)
        speed_layout.addStretch()
        options_layout.addLayout(speed_layout)
        
        # 实时清理选项
        cleanup_layout = QHBoxLayout()
        self.realtime_cleanup_check = QCheckBox("实时清理缓存")
        self.realtime_cleanup_check.setChecked(True)
        self.realtime_cleanup_check.setToolTip("处理过程中自动清理不需要的临时文件，节省磁盘空间")
        cleanup_layout.addWidget(self.realtime_cleanup_check)
        cleanup_layout.addStretch()
        options_layout.addLayout(cleanup_layout)
        
        main_layout.addWidget(options_group)

        # 创建处理按钮和进度条
        process_layout = QHBoxLayout()
        self.process_btn = QPushButton("开始处理")
        self.process_btn.setMinimumHeight(40)
        self.process_btn.clicked.connect(self.start_processing)
        process_layout.addWidget(self.process_btn)
        main_layout.addLayout(process_layout)

        self.progress_bar = QProgressBar()
        self.progress_bar.setTextVisible(False)
        self.progress_bar.setRange(0, 0)  # 设置为不确定模式
        self.progress_bar.hide()
        main_layout.addWidget(self.progress_bar)

        # 创建日志区域
        log_group = QGroupBox("处理日志")
        log_layout = QVBoxLayout(log_group)
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        main_layout.addWidget(log_group)

        # 添加说明文本
        info_label = QLabel("说明: 本工具将两个视频的帧进行交叉替换，奇数帧使用主视频，偶数帧使用辅助视频，生成新视频。输出视频参数为120帧，20000码率，使用主视频的音频。优化版本支持一边合成一边清理缓存，大幅提升处理速度。")
        info_label.setWordWrap(True)
        info_label.setStyleSheet("color: #666; padding: 10px;")
        main_layout.addWidget(info_label)

        # 添加免责声明
        disclaimer = QLabel("免责声明: 仅供内部使用，禁止销售。")
        disclaimer.setAlignment(Qt.AlignCenter)
        disclaimer.setStyleSheet("color: #999; padding: 5px;")
        main_layout.addWidget(disclaimer)

        # 初始化工作线程
        self.worker = None

    def find_ffmpeg(self):
        """查找FFmpeg可执行文件路径"""
        # 首先检查当前目录
        if os.path.exists("ffmpeg.exe"):
            return os.path.abspath("ffmpeg.exe")

        # 然后检查PATH环境变量
        try:
            result = subprocess.run(["where", "ffmpeg"], capture_output=True, text=True, check=True)
            if result.stdout.strip():
                return "ffmpeg"
        except:
            pass

        # 最后使用默认值
        return "ffmpeg"

    def select_main_video(self):
        """选择主视频文件"""
        file_path, _ = QFileDialog.getOpenFileName(self, "选择主视频", "", "视频文件 (*.mp4 *.avi *.mov *.mkv);;所有文件 (*)")
        if file_path:
            self.main_video_path.setText(file_path)
            self.log(f"已选择主视频: {file_path}")

    def select_aux_video(self):
        """选择辅助视频文件"""
        file_path, _ = QFileDialog.getOpenFileName(self, "选择辅助视频", "", "视频文件 (*.mp4 *.avi *.mov *.mkv);;所有文件 (*)")
        if file_path:
            self.aux_video_path.setText(file_path)
            self.log(f"已选择辅助视频: {file_path}")

    def select_output_file(self):
        """选择输出文件"""
        file_path, _ = QFileDialog.getSaveFileName(self, "选择输出文件", "", "MP4文件 (*.mp4);;所有文件 (*)")
        if file_path:
            if not file_path.lower().endswith('.mp4'):
                file_path += '.mp4'
            self.output_path.setText(file_path)
            self.log(f"已选择输出文件: {file_path}")

    def log(self, message):
        """添加日志消息"""
        self.log_text.append(message)
        # 滚动到底部
        self.log_text.verticalScrollBar().setValue(self.log_text.verticalScrollBar().maximum())

    def update_progress(self, message):
        """更新进度信息"""
        self.log(message)

    def process_finished(self, success, message, temp_dir):
        """处理完成回调"""
        self.progress_bar.hide()
        self.process_btn.setEnabled(True)

        if success:
            QMessageBox.information(self, "处理完成", "视频处理成功！")
            self.log("处理完成！")
        else:
            QMessageBox.critical(self, "处理失败", f"视频处理失败: {message}")
            self.log(f"处理失败: {message}")

    def start_processing(self):
        """开始处理视频"""
        # 检查输入
        main_video = self.main_video_path.text()
        if main_video == "未选择":
            QMessageBox.warning(self, "输入错误", "请选择主视频文件")
            return

        aux_video = self.aux_video_path.text()
        if aux_video == "未选择":
            QMessageBox.warning(self, "输入错误", "请选择辅助视频文件")
            return

        output_file = self.output_path.text()
        if output_file == "未选择":
            QMessageBox.warning(self, "输入错误", "请选择输出文件")
            return

        # 禁用UI
        self.process_btn.setEnabled(False)
        self.progress_bar.show()

        # 清空日志
        self.log_text.clear()

        # 获取选项
        speed_priority = self.speed_priority_check.isChecked()
        realtime_cleanup = self.realtime_cleanup_check.isChecked()
        
        if speed_priority:
            self.log("已启用速度优先模式，处理速度将更快，但可能略微降低视频质量")
        
        if realtime_cleanup:
            self.log("已启用实时清理缓存，将在处理过程中自动清理临时文件")
        
        # 启动工作线程
        self.log("开始处理视频...")
        self.worker = FFmpegWorker(main_video, aux_video, output_file, self.ffmpeg_path, speed_priority)
        self.worker.progress_update.connect(self.update_progress)
        self.worker.process_finished.connect(self.process_finished)
        self.worker.start()

    def closeEvent(self, event):
        """程序关闭时的事件处理"""
        event.accept()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = CrossFrameAlternationGUI()
    window.show()
    sys.exit(app.exec_())
